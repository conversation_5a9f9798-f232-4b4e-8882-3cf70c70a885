package com.facishare.open.outer.oa.connector.common.api.result;

import com.facishare.open.outer.oa.connector.common.api.admin.*;
import com.facishare.open.outer.oa.connector.common.api.enums.BindTypeEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.OuterOaAppInfoTypeEnum;
import com.facishare.open.outer.oa.connector.common.api.params.CepArg;
import com.facishare.open.outer.oa.connector.common.api.utils.ConnectorVoUtils;
import lombok.Data;

import java.io.Serializable;

@Data
public class OuterOAConnectSettingResult extends CepArg implements Serializable {

    private ConnectParams connectParams;
    private ChannelEnum channelEnum;
    private BindTypeEnum bindTypeEnum;
    private String bindStatus;
    private OuterOaAppInfoTypeEnum outerOaAppInfoTypeEnum;

    /**
     * 连接器配置参数内部类
     */
    @Data
    public static class ConnectParams implements Serializable {
        /**
         * 钉钉渠道,因为钉钉渠道没有分开
         */
        private DingConnectorVo dingding;
        /**
         * 飞书_Lark渠道
         */
        private FeiShuConnectorVo lark;

        private FeiShuConnectorVo feishu;
        /**
         * 企业微信渠道
         */
        private QywxConnectorVo qywx;
        /**
         * 标准渠道
         */
        private StandardConnectorVo standard_oa;
    }
    
    /**
     * 获取对应的ConnectorVo实例
     * 从ConnectParams中获取对应的ConnectorVo
     *
     * @param connectParams 连接参数
     * @param channelEnum 渠道类型

     * @return 对应的ConnectorVo实例
     */
    public static BaseConnectorVo getOuterOAConnectSettingResult(ConnectParams connectParams, ChannelEnum channelEnum) {
        return ConnectorVoUtils.getConnectorVoFromConnectParams(connectParams, channelEnum);
    }

    /**
     * 设置ConnectorVo到ConnectParams中
     * 根据渠道和应用类型，将数据反射设置到对应的字段
     *
     * @param sourceVo 源数据对象
     * @param channelEnum 渠道类型
     * @param outerOaAppInfoTypeEnum 应用类型
     */
    public void setConnectorVo(BaseConnectorVo sourceVo, ChannelEnum channelEnum, OuterOaAppInfoTypeEnum outerOaAppInfoTypeEnum) {
        if (this.connectParams == null) {
            this.connectParams = new ConnectParams();
        }
        ConnectorVoUtils.setConnectorVoToConnectParams(this.connectParams, sourceVo, channelEnum);
        this.channelEnum = channelEnum;
        this.outerOaAppInfoTypeEnum = outerOaAppInfoTypeEnum;
    }
}
