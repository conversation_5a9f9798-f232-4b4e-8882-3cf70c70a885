package com.facishare.open.ding.provider.task;

import com.alibaba.fastjson.JSONObject;
import com.facishare.open.ding.provider.model.result.CrmResponseResult;
import com.facishare.open.ding.provider.model.result.crm.ErpObjListData;
import com.facishare.open.ding.provider.model.result.crm.ProductListData;
import com.facishare.open.ding.provider.utils.CrmRequestUrl;
import com.facishare.open.ding.provider.utils.CrmRequestUtils;
import com.facishare.open.ding.api.enums.CrmApiNameEnum;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <p>fs-crm 接口</p>
 * <p>
 * http://ncrm.nsvc.foneshare.cn/API/v1/inner/object
 * @version 1.0
 * @dateTime 2018/7/17 17:28
 */

@Slf4j
@Component
public class NCrmManager {

    @ReloadableProperty("custom.object.url.prefix")
    public String CUSTOM_OBJECT_URL_PREFIX;

    @ReloadableProperty("rest.object.url.prefix")
    public String REST_OBJECT_URL_PREFIX;

    private Gson gson = new Gson();

    /**
     * CRM系统管理员身份
     **/
    private int USER_SYSTEM = -10000;

    private String SLASH = "/";

    private static final String COMMON_API_NAME_KEY = "object_describe_api_name";


    /**
     * 分页查询ERP或V2对象
     * @param url
     * @param ei
     * @param userId
     * @param lastSyncTime
     * @param currentSyncTime
     * @param offset
     * @param target
     * @param <T>
     * @return
     */
    public <T> CrmResponseResult<T> baseQueryErpOrV2Page(String url,
                                                         int ei,
                                                         int userId,
                                                         String apiName,
                                                         Long lastSyncTime,
                                                         Long currentSyncTime,
                                                         Map<String, Object> filter,
                                                         int offset, Class<T> target) {
        Map<String, Object> params = Maps.newHashMap();
        Map<String, Object> searchQueryMap = Maps.newHashMap();
        searchQueryMap.put("limit", 100);
        searchQueryMap.put("offset", offset);
        List<Map<String, Object>> filters = Lists.newArrayList();
        if (filter != null) {
            filters.add(filter);
        }
        if (Objects.nonNull(lastSyncTime)) {
            Map<String, Object> filterMap = Maps.newHashMap();
            filterMap.put("field_name", "last_modified_time");
            filterMap.put("field_values", lastSyncTime);
            filterMap.put("operator", "GT");
            filters.add(filterMap);

            Map<String, Object> filterEndMap = Maps.newHashMap();
            filterEndMap.put("field_name", "last_modified_time");
            filterEndMap.put("field_values", currentSyncTime);
            filterEndMap.put("operator", "LTE");
            filters.add(filterMap);
        }

        List<Map<String, Object>> orders = Lists.newArrayList();
        Map<String, Object> orderMap = Maps.newHashMap();
        orderMap.put("field_name", "create_time");
        orderMap.put("isAsc", true);

        searchQueryMap.put("filters", filters);
        searchQueryMap.put("orders", orders);

        params.put(COMMON_API_NAME_KEY, apiName);
        params.put("search_query_info", JSONObject.toJSON(searchQueryMap).toString());

        String json = gson.toJson(params);
        return CrmRequestUtils.queryErp(url, ei, userId, json, target);
    }

    /**
     *V2查询列表接口（rest）
     */
    public <T> CrmResponseResult<T> baseQueryV2Rest(String url,
                                                         int ei,
                                                         int userId,
                                                         String apiName,
                                                         Long lastSyncTime,
                                                         Long currentSyncTime,
                                                         List<Map<String, Object>> filter,
                                                         int offset, Class<T> target) {
        Map<String, Object> params = Maps.newHashMap();
        Map<String, Object> searchQueryMap = Maps.newHashMap();
        searchQueryMap.put("limit", 100);
        searchQueryMap.put("offset", offset);
        List<Map<String, Object>> filters = Lists.newArrayList();
        if (filter != null) {
            filters.addAll(filter);
        }
        if (Objects.nonNull(lastSyncTime)) {
            Map<String, Object> filterMap = Maps.newHashMap();
            filterMap.put("field_name", "last_modified_time");
            filterMap.put("field_values", lastSyncTime);
            filterMap.put("operator", "GT");
            filters.add(filterMap);
            Map<String, Object> filterEndMap = Maps.newHashMap();
            filterEndMap.put("field_name", "last_modified_time");
            filterEndMap.put("field_values", currentSyncTime);
            filterEndMap.put("operator", "LTE");
            filters.add(filterMap);
        }

        List<Map<String, Object>> orders = Lists.newArrayList();
        Map<String, Object> orderMap = Maps.newHashMap();
        orderMap.put("field_name", "create_time");
        orderMap.put("isAsc", true);

        searchQueryMap.put("filters", filters);
        searchQueryMap.put("orders", orders);

        params.put(COMMON_API_NAME_KEY, apiName);
        params.put("search_query_info", JSONObject.toJSON(searchQueryMap).toString());

        String json = gson.toJson(params);
        return CrmRequestUtils.queryRest(url, ei, userId, json, target);
    }


    /**
     * 分页查询 ERP对象 或 回款对象
     * @param ei
     * @param apiName
     * @param lastSyncTime
     * @param currentSyncTime
     * @param offset
     * @return buildErpV2ObjectListUrlNew
     */
    public CrmResponseResult<ErpObjListData> pageErpObjectList(int ei, String apiName, Long lastSyncTime, Long currentSyncTime, int offset, Map<String, Object> filter) {
        String url = Joiner.on(StringUtils.EMPTY).join(CUSTOM_OBJECT_URL_PREFIX, SLASH, CrmRequestUrl.buildErpV2ObjectListUrl(apiName));
        return baseQueryErpOrV2Page(url, ei, USER_SYSTEM, apiName, lastSyncTime, currentSyncTime, filter, offset, ErpObjListData.class);
    }

    /**
     * 查询产品
     */
    public CrmResponseResult<ErpObjListData> pageErpObjectListNew(int ei, String apiName, Long lastSyncTime, Long currentSyncTime, int offset, Map<String, Object> filter) {
        String url = Joiner.on(StringUtils.EMPTY).join(CUSTOM_OBJECT_URL_PREFIX, SLASH, CrmRequestUrl.buildErpV2ObjectListUrlNew(apiName));
        return baseQueryErpOrV2Page(url, ei, USER_SYSTEM, apiName, lastSyncTime, currentSyncTime, filter, offset, ErpObjListData.class);
    }

    /**
     * V2查询列表接口（rest接口）
     */
    public CrmResponseResult<ErpObjListData> pageObjectListRest(int ei, String apiName, Long lastSyncTime, Long currentSyncTime, int offset, List<Map<String, Object>> filter) {
        String url = Joiner.on(StringUtils.EMPTY).join(REST_OBJECT_URL_PREFIX, SLASH, CrmRequestUrl.buildErpV2ObjectListUrlNew(apiName));
        return baseQueryV2Rest(url, ei, USER_SYSTEM, apiName, lastSyncTime, currentSyncTime, filter, offset, ErpObjListData.class);
    }

    /**
     * 通过客户id筛选查询联系人  V2接口
     * @param ei
     * @param accountId
     * @return
     */
    public CrmResponseResult<ErpObjListData> queryContactByAccountId(Integer ei, String accountId){
        String url = Joiner.on(StringUtils.EMPTY).join(REST_OBJECT_URL_PREFIX, SLASH, CrmRequestUrl.buildErpV2ObjectListUrlNew(CrmApiNameEnum.ContactObj.getApiName()));
        Map<String, Object> filter = new HashMap<>();
        filter.put("field_name", "account_id");
        filter.put("field_values", Lists.newArrayList(accountId));
        filter.put("operator", "EQ");
        List<Map<String, Object>> filtersList = Lists.newArrayList(filter);
        return baseQueryV2Rest(url, ei, USER_SYSTEM, CrmApiNameEnum.ContactObj.getApiName(), null, null, filtersList, 0, ErpObjListData.class);
    }


    /**
     * 根据商品id查询产品Id
     * @param ei
     * @param spuId
     * @return
     */
    public CrmResponseResult<ProductListData> getProductBySpuId(int ei, String spuId) {
        CrmResponseResult<ProductListData> result = new CrmResponseResult<>();
        Map<String, Object> filterMap = Maps.newHashMap();
        filterMap.put("field_name", "spu_id");
        filterMap.put("field_values", spuId);
        filterMap.put("operator", "EQ");
        CrmResponseResult<ErpObjListData> crmResponseResult = pageErpObjectListNew(ei, "ProductObj", null, null, 0, filterMap);
        result.setErrorCode(crmResponseResult.getErrorCode());
        result.setErrorMessage(crmResponseResult.getErrorMessage());
        if (!crmResponseResult.isSuccess()) {
            return result;
        }
        ProductListData productListData = new ProductListData();
        productListData.setData(crmResponseResult.getData().getDataList());
        result.setData(productListData);
        return result;
    }

    /**
     * 查询纷享库存对象
     * @param ei
     * @param productId
     * @return
     */
    public CrmResponseResult<ErpObjListData> queryStockObj(int ei, String productId, String warehouseId) {
        String url = Joiner.on(StringUtils.EMPTY).join(CUSTOM_OBJECT_URL_PREFIX, SLASH, CrmRequestUrl.buildErpV2ObjectListUrlNew(CrmApiNameEnum.StockObj.getApiName()));
        Map<String, Object> productFilter = new HashMap<>();
        productFilter.put("field_name", "product_id");
        productFilter.put("field_values", Lists.newArrayList(productId));
        productFilter.put("operator", "EQ");
        List<Map<String, Object>> filtersList = Lists.newArrayList(productFilter);
        Map<String, Object> warehouseFilter = new HashMap<>();
        warehouseFilter.put("field_name", "warehouse_id");
        warehouseFilter.put("field_values", Lists.newArrayList(warehouseId));
        warehouseFilter.put("operator", "EQ");
        filtersList.add(warehouseFilter);

        Map<String, Object> params = Maps.newHashMap();
        Map<String, Object> searchQueryMap = Maps.newHashMap();
        searchQueryMap.put("limit", 100);
        searchQueryMap.put("offset", 0);

        List<Map<String, Object>> orders = Lists.newArrayList();
        Map<String, Object> orderMap = Maps.newHashMap();
        orderMap.put("field_name", "create_time");
        orderMap.put("isAsc", true);

        searchQueryMap.put("filters", filtersList);
        searchQueryMap.put("orders", orders);

        params.put(COMMON_API_NAME_KEY, CrmApiNameEnum.StockObj.getApiName());
        params.put("search_query_info", JSONObject.toJSON(searchQueryMap).toString());

        String json = gson.toJson(params);
        return CrmRequestUtils.queryErp(url, ei, USER_SYSTEM, json, ErpObjListData.class);
    }

    /**
     * 创建ERP对象
     * @param ei
     * @param userId
     * @param dataMap
     * @return
     */
    public CrmResponseResult<String> createErpObject(int ei, String apiName, int userId, Map<String, Object> dataMap) {
        String url = Joiner.on(StringUtils.EMPTY).join(CUSTOM_OBJECT_URL_PREFIX, SLASH, CrmRequestUrl.buildErpObjectCreateUrl(apiName));
        if (CollectionUtils.isEmpty(dataMap)) {
            dataMap = Maps.newHashMap();
        }
        if (userId < 1000) {
            userId = USER_SYSTEM;
        }
        String json = gson.toJson(dataMap);
        return CrmRequestUtils.createErpAndGetId(url, ei, userId, json);
    }

    /**
     * 创建v2对象
     * 包括 纷享库存、回款
     * @param ei
     * @param userId
     * @param dataMap
     * @return
     */
    public CrmResponseResult<String> createV2Object(int ei, String apiName, int userId, Map<String, Object> dataMap) {
        String url = Joiner.on(StringUtils.EMPTY).join(CUSTOM_OBJECT_URL_PREFIX, SLASH, CrmRequestUrl.buildV2CreateObjectDataUrl(apiName));
        if (CollectionUtils.isEmpty(dataMap)) {
            dataMap = Maps.newHashMap();
        }
        if (userId < 1000) {
            userId = USER_SYSTEM;
        }
        String json = gson.toJson(dataMap);
        return CrmRequestUtils.createErpOrV2AndGetId(url, ei, userId, json);
    }

    /**
     *V2创建对象 rest接口
     */
    public CrmResponseResult<String> createV2ObjectRest(int ei, String apiName, int userId, Map<String, Object> dataMap) {
        String url = Joiner.on(StringUtils.EMPTY).join(REST_OBJECT_URL_PREFIX, SLASH, CrmRequestUrl.buildV2CreateObjectDataUrl(apiName));
        if (CollectionUtils.isEmpty(dataMap)) {
            dataMap = Maps.newHashMap();
        }
        if (userId < 1000) {
            userId = USER_SYSTEM;
        }
        String json = gson.toJson(dataMap);
        return CrmRequestUtils.createErpOrV2AndGetId(url, ei, userId, json);
    }



    /**
     * 更新ERP对象
     * @param ei
     * @param userId
     * @param dataMap
     * @return
     */
    public CrmResponseResult<Void> updateErpObject(int ei, String apiName, int userId, Map<String, Object> dataMap) {
        String url = Joiner.on(StringUtils.EMPTY).join(CUSTOM_OBJECT_URL_PREFIX, SLASH, CrmRequestUrl.buildErpObjectUpdateUrl(apiName));
        if (CollectionUtils.isEmpty(dataMap)) {
            dataMap = Maps.newHashMap();
        }
        if (userId < 1000) {
            userId = USER_SYSTEM;
        }
        String json = gson.toJson(dataMap);
        return CrmRequestUtils.update(url, ei, userId, json);
    }

    /**
     * 更新ERP库存专用
     * @param ei
     * @param userId
     * @param dataMap
     * @return
     */
    public CrmResponseResult<Void> updateErpStock(int ei, String apiName, int userId, Map<String, Object> dataMap) {
        String url = Joiner.on(StringUtils.EMPTY).join(CUSTOM_OBJECT_URL_PREFIX, SLASH, CrmRequestUrl.buildErpObjectUpdateUrl(apiName));
        if (CollectionUtils.isEmpty(dataMap)) {
            dataMap = Maps.newHashMap();
        }
        if (userId < 1000) {
            userId = USER_SYSTEM;
        }
        String json = gson.toJson(dataMap);
        return CrmRequestUtils.updateERPStock(url, ei, userId, json);
    }

    /**
     * 更新V2对象
     * @param ei
     * @param userId
     * @param dataMap
     * @return
     */
    public CrmResponseResult<Void> updatePayment(int ei, String apiName, int userId, Map<String, Object> dataMap) {
        String url = Joiner.on(StringUtils.EMPTY).join(CUSTOM_OBJECT_URL_PREFIX, SLASH, CrmRequestUrl.buildV2EditObjectDataUrl(apiName));
        if (CollectionUtils.isEmpty(dataMap)) {
            dataMap = Maps.newHashMap();
        }
        String json = gson.toJson(dataMap);
        return CrmRequestUtils.update(url, ei, userId, json);
    }

    /**
     * 查询ERP对象详情
     * @param ei
     * @param userId
     * @param dataMap
     * @return
     */
    public CrmResponseResult<Map> getDetailErpObject(int ei, String apiName, int userId, Map<String, Object> dataMap) {
        String url = Joiner.on(StringUtils.EMPTY).join(CUSTOM_OBJECT_URL_PREFIX, SLASH, CrmRequestUrl.buildErpObjectDetailUrl(apiName));
        if (CollectionUtils.isEmpty(dataMap)) {
            dataMap = Maps.newHashMap();
        }
        if (userId < 1000) {
            userId = USER_SYSTEM;
        }
        String json = gson.toJson(dataMap);
        return CrmRequestUtils.queryErp(url, ei, userId, json, Map.class);
    }

    /**
     * 查询库存对象详情
     * 不知是否可应用其他对象，因与CRM其他对象查询结果不同
     * @param ei
     * @param userId
     * @param dataMap
     * @return
     */
    public CrmResponseResult<Map> getErpStockDetail(int ei, String apiName, int userId, Map<String, Object> dataMap) {
        String url = Joiner.on(StringUtils.EMPTY).join(CUSTOM_OBJECT_URL_PREFIX, SLASH, CrmRequestUrl.buildErpObjectDetailUrl(apiName));
        if (CollectionUtils.isEmpty(dataMap)) {
            dataMap = Maps.newHashMap();
        }
        if (userId < 1000) {
            userId = USER_SYSTEM;
        }
        String json = gson.toJson(dataMap);
        return CrmRequestUtils.queryErpStockDetail(url, ei, userId, json, Map.class);
    }

    /**
     * 作废ERP对象
     * @param ei
     * @param userId
     * @param dataMap
     * @return
     */
    public CrmResponseResult<Void> invalidErpObject(int ei, String apiName, int userId, Map<String, Object> dataMap) {
        String url = Joiner.on(StringUtils.EMPTY).join(CUSTOM_OBJECT_URL_PREFIX, SLASH, CrmRequestUrl.buildErpObjectInvalidUrl(apiName));
        if (CollectionUtils.isEmpty(dataMap)) {
            dataMap = Maps.newHashMap();
        }
        String json = gson.toJson(dataMap);
        return CrmRequestUtils.postEdit(url, ei, userId, json);
    }

    /**
     * 批量作废,暂时只用于库存对象
     */
    public CrmResponseResult<Void> invalidBatchErpObject(int ei, String apiName, int userId, Map<String, Object> dataMap) {
        String url = Joiner.on(StringUtils.EMPTY).join(CUSTOM_OBJECT_URL_PREFIX, SLASH, CrmRequestUrl.buildErpObjectBatchInvalidUrl(apiName));
        if (CollectionUtils.isEmpty(dataMap)) {
            dataMap = Maps.newHashMap();
        }
        String json = gson.toJson(dataMap);
        return CrmRequestUtils.postEdit(url, ei, userId, json);
    }

    /**
     * 删除ERP对象
     * @param ei
     * @param userId
     * @param dataMap
     * @return
     */
    public CrmResponseResult<Void> deleteErpObject(int ei, String apiName, int userId, Map<String, Object> dataMap) {
        String url = Joiner.on(StringUtils.EMPTY).join(CUSTOM_OBJECT_URL_PREFIX, SLASH, CrmRequestUrl.buildErpObjectDeleteUrl(apiName));
        if (CollectionUtils.isEmpty(dataMap)) {
            dataMap = Maps.newHashMap();
        }
        String json = gson.toJson(dataMap);
        return CrmRequestUtils.postEdit(url, ei, userId, json);
    }

    /**
     * 批量删除ERP对象，暂时只应用于ERP库存
     */
    public CrmResponseResult<Void> deleteErpBatchObject(int ei, String apiName, int userId, Map<String, Object> dataMap) {
        String url = Joiner.on(StringUtils.EMPTY).join(CUSTOM_OBJECT_URL_PREFIX, SLASH, CrmRequestUrl.buildErpObjectBatchDeleteUrl(apiName));
        if (CollectionUtils.isEmpty(dataMap)) {
            dataMap = Maps.newHashMap();
        }
        String json = gson.toJson(dataMap);
        return CrmRequestUtils.postEdit(url, ei, userId, json);
    }






    /**
     * 批量作废V2库存对象(只适配纷享库存对象)
     */
    public CrmResponseResult<Void> invalidBatchV2Object(int ei, String apiName, int userId, Map<String, Object> dataMap) {
        String url = Joiner.on(StringUtils.EMPTY).join(CUSTOM_OBJECT_URL_PREFIX, SLASH, CrmRequestUrl.buildV2ObjectInvalidUrl(apiName));
        CrmResponseResult<Void> crmResponseResult = null;
        if (CollectionUtils.isEmpty(dataMap)) {
            dataMap = Maps.newHashMap();
        }
        List<String> objIds = (List<String>) dataMap.get("objectDataIdList");
        for (String objId:objIds){
            Map<String, Object> dataMapParam = new HashMap<>();
            dataMapParam.put("object_data_id", objId);
            dataMapParam.put("dataObjectApiName", apiName);
            String json = gson.toJson(dataMapParam);
            crmResponseResult = CrmRequestUtils.postEdit(url, ei, userId, json);
            if(!crmResponseResult.isSuccess()){
                return crmResponseResult;
            }
        }
        return crmResponseResult;
    }

    /**
     * 批量删除V2对象，暂时只应用于库存
     */
    public CrmResponseResult<Void> deleteV2BatchObject(int ei, String apiName, int userId, Map<String, Object> dataMap) {
        String url = Joiner.on(StringUtils.EMPTY).join(CUSTOM_OBJECT_URL_PREFIX, SLASH, CrmRequestUrl.buildV2ObjectBatchDeleteUrl(apiName));
        if (CollectionUtils.isEmpty(dataMap)) {
            dataMap = Maps.newHashMap();
        }
        List<String> objIds = (List<String>)dataMap.get("objectDataIdList");
        String[] stockIds = new String[objIds.size()];
        objIds.toArray(stockIds);
        Map<String, Object> dataMapParam = new HashMap<>();
        dataMapParam.put("idList", stockIds);
        dataMapParam.put("describe_api_name", apiName);
        String json = gson.toJson(dataMapParam);
        return CrmRequestUtils.postEdit(url, ei, userId, json);
    }

    /**
     * 作废V2库存对象
     */
    public CrmResponseResult<Void> invalidV2Object(int ei, String apiName, int userId, Map<String, Object> dataMap) {
        String url = Joiner.on(StringUtils.EMPTY).join(CUSTOM_OBJECT_URL_PREFIX, SLASH, CrmRequestUrl.buildV2ObjectInvalidUrl(apiName));
        if (CollectionUtils.isEmpty(dataMap)) {
            dataMap = Maps.newHashMap();
        }
        String json = gson.toJson(dataMap);
        return CrmRequestUtils.postEdit(url, ei, userId, json);
    }

    /**
     * 删除V2库存对象
     */
    public CrmResponseResult<Void> deleteV2Object(int ei, String apiName, int userId, Map<String, Object> dataMap) {
        String url = Joiner.on(StringUtils.EMPTY).join(CUSTOM_OBJECT_URL_PREFIX, SLASH, CrmRequestUrl.buildV2ObjectBatchDeleteUrl(apiName));
        if (CollectionUtils.isEmpty(dataMap)) {
            dataMap = Maps.newHashMap();
        }
        String objId = (String) dataMap.get("object_data_id");
        String[] stockIds = new String[]{objId};
        Map<String, Object> dataMapParam = new HashMap<>();
        dataMapParam.put("idList", stockIds);
        dataMapParam.put("describe_api_name", apiName);
        String json = gson.toJson(dataMapParam);
        return CrmRequestUtils.postEdit(url, ei, userId, json);
    }
}
