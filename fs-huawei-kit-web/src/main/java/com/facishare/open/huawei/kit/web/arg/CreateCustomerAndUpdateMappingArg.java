package com.facishare.open.huawei.kit.web.arg;

import lombok.Data;

import java.io.Serializable;

@Data
public class CreateCustomerAndUpdateMappingArg implements Serializable {
    /**
     * 纷享企业EA
     */
    private String fsEa;
    /**
     * 对于飞书，这个传displayId
     */
    private String outEid;
    /**
     * 对于飞书，这个传tenantKey
     */
    private String outEa;
    /**
     * 应用安装人的用户ID
     */
    private String installerUserId;
    /**
     * 应用安装人的用户名
     */
    private String installerName;
    /**
     * 企业名称
     */
    private String enterpriseName;
    /**
     * 手机号
     */
    private String installerMobilePhone;
    /**
     * 应用id
     */
    private String appId;

}
