package com.facishare.open.qywx.web.controller.outer;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.facishare.open.qywx.accountsync.model.qyweixin.QyweixinExternalContactInfo;
import com.facishare.open.qywx.accountsync.result.Result;
import com.facishare.open.qywx.save.result.QywxMessageDataResult;
import com.facishare.open.qywx.web.network.ProxyOkHttpClient;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.AbstractJUnit4SpringContextTests;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:spring-test/spring-common-test.xml")
public class ControllerOpenQYWeixinTest extends AbstractJUnit4SpringContextTests {
    @Resource
    private ControllerOpenQYWeixin controllerOpenQYWeixin;

    @Autowired
    private ProxyOkHttpClient proxyOkHttpClient;

    @Test
    public void outAccountToFsAccountBatchTest() throws Exception {
        String x = "https://qyapi.weixin.qq.com/cgi-bin/chatdata/sync_call_program?access_token=YXDSZcc1GQNMreCjIcFCwapTP3wPOSI6bz82UNcgikKdPgVrbpz_PeKojQUGesq4XF441x63CiQGwx2u1mfmVCohYjCUpfao_ywlfEXX1feM-rQrN6EcK3oKcTgMBy948fmLJqwN-lFowtqOixLHuV-EUFgyWD-8d552Vvhta2wowvXCaaLweIbZey0oYBmQZbVBXiRLS1Jqca9jByMnwQ";
        QywxMessageDataResult callData = getCallData(x, "invoke_sync_msg", "prog5UgeDHK1m6vhxTyJWj-R2auctXIAeFUm", null, "{\"cursor\":\"nc#iTnoSjxKuATuOFcinRbDEcSASXSahNSE101qAWyNARfh6CPzS-9skNtXTnQ_V1RD\",\"limit\":100,\"token\":\"ENC3wHhmmv5NZZrVRvFfUniXwkXcPGNmyEhdMxyBhH7Zx8c\"}");
        if(!callData.isSuccess()){
            log.info("message save get data:{}", JSONObject.toJSONString(callData));
            return;
        }
        System.out.println(callData);
    }

    public QywxMessageDataResult getCallData(String callUrl, String abilityId, String programId, String notifyId, String requestData ){
        Map<String,String> dataMap= Maps.newHashMap();
        dataMap.put("ability_id",abilityId);
        dataMap.put("program_id",programId);
        dataMap.put("notify_id",notifyId);
        dataMap.put("request_data",requestData);
        QywxMessageDataResult qywxMessageDataResult = proxyOkHttpClient.postUrl(callUrl,dataMap, Maps.newHashMap() ,new TypeReference<QywxMessageDataResult>(){});
        log.info("getcall data url:{}：ability_id:{},program_id：{},notify_id：{},request_data:{}:dataResult:{}",callUrl,abilityId,programId,notifyId,requestData,qywxMessageDataResult);
        return qywxMessageDataResult;
    }
}
