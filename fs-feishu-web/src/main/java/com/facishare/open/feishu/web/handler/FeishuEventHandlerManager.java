package com.facishare.open.feishu.web.handler;

import com.facishare.open.feishu.syncapi.model.event2.FeishuEventModel2;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.open.order.contacts.proxy.api.utils.TraceUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Component
public class FeishuEventHandlerManager {
    @Resource
    private List<FeishuEventHandler> eventHandlerList;
    private ExecutorService executorService = Executors.newFixedThreadPool(100);

    public String handle(String eventType,FeishuEventModel2.EventModelHeader header, String eventData) {
        if(CollectionUtils.isEmpty(eventHandlerList)) return null;

        String traceId = TraceUtils.getTraceId();

        if(StringUtils.isNotEmpty(eventType)) {
            for(FeishuEventHandler handler : eventHandlerList) {
                if(handler.accept(eventType)) {
                    executorService.submit(()->{
                        TraceUtils.initTraceId(traceId);
                        handler.handle(header,eventData);
                    });
                    return FeishuEventHandler.SUCCESS;
                }
            }
        }

        LogUtils.info("FeishuEventHandlerManager.handle,not support event,eventType={},eventData={}",eventType,eventData);
        return null;
    }
}
