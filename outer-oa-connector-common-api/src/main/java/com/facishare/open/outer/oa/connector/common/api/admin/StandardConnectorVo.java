package com.facishare.open.outer.oa.connector.common.api.admin;

import com.facishare.open.outer.oa.connector.common.api.annotation.ConnectorType;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.facishare.open.outer.oa.connector.common.api.info.SystemParams;
import lombok.*;

/**
 * 标准渠道
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ConnectorType(channel = ChannelEnum.standard_oa, fieldName = "standard_oa")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class StandardConnectorVo extends BaseConnectorVo {
    private static final long serialVersionUID = -1L;
    /**
     * 外部企业ID
     */
    private String outEa;
    /**
     * 外部企业appId
     */
    private String appId;
    /**
     * 外部企业名称
     */
    private String outName;
    private SystemParams systemParams;

    private Boolean isBind=false;

}
