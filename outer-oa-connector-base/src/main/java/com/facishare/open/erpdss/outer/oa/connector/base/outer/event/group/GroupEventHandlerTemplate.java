package com.facishare.open.erpdss.outer.oa.connector.base.outer.event.group;

import com.facishare.open.erpdss.outer.oa.connector.base.HandlerTemplate;
import com.facishare.open.erpdss.outer.oa.connector.base.context.MethodContext;
import com.facishare.open.erpdss.outer.oa.connector.base.context.TemplateResult;
import lombok.extern.slf4j.Slf4j;

/**
 * 群事件处理器模型
 * <AUTHOR>
 * @date 2024-08-20
 */

@Slf4j
public abstract class GroupEventHandlerTemplate extends HandlerTemplate {
    @Override
    public TemplateResult execute(Object data) {
        MethodContext context = MethodContext.newInstance(data);

        readGroupData(context);
        log.info("GroupEventHandlerTemplate.execute,readGroupData,context={}",context);
        if(context.isError()) {
            return context.getResult();
        }

        saveData(context);
        log.info("GroupEventHandlerTemplate.execute,saveData,context={}",context);
        if(context.isError()) {
            return context.getResult();
        }

        notifyBusiness(context);
        log.info("GroupEventHandlerTemplate.execute,notifyBusiness,context={}",context);
        return context.getResult();
    }

    /**
     * 读取群数据
     * @param context
     */
    public abstract void readGroupData(MethodContext context);

    /**
     * 存储群数据
     * @param context
     */
    public abstract void saveData(MethodContext context);

    /**
     * 通知业务方
     * @param context
     */
    public abstract void notifyBusiness(MethodContext context);
}
