package com.facishare.open.outer.oa.connector.web.openconnetor.service;


import com.facishare.open.erpdss.outer.oa.connector.base.context.TemplateResult;
import com.facishare.open.outer.oa.connector.common.api.result.Result;
import com.facishare.open.outer.oa.connector.web.openconnetor.model.OuterMsgModel;
import com.fxiaoke.message.extrnal.platform.model.arg.CreateTodoArg;

public interface OpenConnectorCreateTodoService {
    /**
     * 过滤消息
     * @param context
     */
    Result<Void> filterCreateTodoMsg(String tenantId, String dataCenterId, String connectorApiName, CreateTodoArg context);

    /**
     * 组装消息
     * @param context
     */
    Result<OuterMsgModel> buildCreateTodoMsg(String tenantId, String dataCenterId, String connectorApiName,CreateTodoArg context);

    /**
     * 发送消息
     * @param context
     */
    Result<Void> sendCreateTodoMsg(String tenantId, String dataCenterId, String connectorApiName,OuterMsgModel context);
}
