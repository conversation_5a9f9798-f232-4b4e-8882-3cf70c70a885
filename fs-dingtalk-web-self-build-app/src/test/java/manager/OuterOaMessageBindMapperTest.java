package manager;

import base.BaseAbstractTest;
import com.facishare.open.oa.base.dbproxy.pg.mapper.OuterOaMessageBindMapper;
import com.google.common.collect.Lists;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2025/4/23 19:22:47
 */
public class OuterOaMessageBindMapperTest  extends BaseAbstractTest {
    @Autowired
    OuterOaMessageBindMapper outerOaMessageBindMapper;

    @Test
    public void updateExecutorStatus() {
        outerOaMessageBindMapper.updateExecutorStatus("91450", "1", Lists.newArrayList("123"), "1", "1", "1", "1", "1", System.currentTimeMillis());
    }
}