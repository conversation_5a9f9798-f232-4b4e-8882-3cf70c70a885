package com.facishare.open.ding.api.enums;

public enum GenerateUrlTypeEnum {

    DEFAULT_URL(0, "默认无须推送"),

    CRM_URL(1, "对象详情页"),

    BPM_URL(2, "业务流详情页"),

    BI_MESSAGE_URL(4, "bi消息"),

    FILE_MESSAGE_URL(5, "文件消息"),

    ATME_URL(6, "at我消息"),

    COMMENT_REDIRECT_URL(99, "通用跳转消息")
    ;

    public int getType() {
        return type;
    }

    public String getExplain() {
        return explain;
    }

    private int type;
    private String explain;

    GenerateUrlTypeEnum(int type, String explain) {
        this.type = type;
        this.explain = explain;
    }


}
