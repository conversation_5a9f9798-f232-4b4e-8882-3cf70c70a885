package com.facishare.open.ding.provider.service;

import com.facishare.open.ding.api.service.DingMonitorService;
import com.facishare.open.oa.base.dbproxy.ch.manager.OAConnectorOpenDataManager;
import com.facishare.open.oa.base.dbproxy.ch.manager.OAConnectorOpenDataModel;
import com.facishare.open.outer.oa.connector.common.api.result.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service("DingMonitorService")
public class DingMonitorServiceImpl implements DingMonitorService {
    @Autowired
    private OAConnectorOpenDataManager oaConnectorOpenDataManager;

    @Override
    public Result<Void> uploadOaConnectorOpenData(OAConnectorOpenDataModel oaConnectorOpenDataModel) {
        oaConnectorOpenDataManager.sendMsg(oaConnectorOpenDataModel);
        return Result.newSuccess();
    }
}
