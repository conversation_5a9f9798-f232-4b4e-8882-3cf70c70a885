package com.facishare.open.outer.oa.connector.web.openconnetor.service;

import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;

/**
 * 开放连接器
 *
 * <AUTHOR>
 */
public interface Connector  {

    String getDefaultName();

    String getNameI18nKey();

    ChannelEnum getChannel();

    String getKey();


    String getModuleCode();

    Integer getConnectorId();

    default String getIconUrl(){
        return null;
    }

}
