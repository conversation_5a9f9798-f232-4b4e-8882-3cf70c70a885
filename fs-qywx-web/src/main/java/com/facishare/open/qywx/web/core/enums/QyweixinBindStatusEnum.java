package com.facishare.open.qywx.web.core.enums;

/**
 * Created by <PERSON><PERSON><PERSON> on 2018/07/26
 */
public enum QyweixinBindStatusEnum {

    BIND(0, "绑定"),
    DELETE_BIND(1, "解绑");

    private Integer code;
    private String name;

    QyweixinBindStatusEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
