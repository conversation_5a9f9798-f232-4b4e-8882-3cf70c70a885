package com.facishare.open.ding.provider.entity;

import java.io.Serializable;
import java.lang.annotation.Annotation;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import javax.persistence.Id;

import com.facishare.open.ding.common.annotation.SecurityField;
import com.facishare.open.ding.common.annotation.SecurityObj;
import com.facishare.open.outer.oa.connector.common.api.admin.DingConnectorVo;
import com.facishare.open.outer.oa.connector.common.api.enums.*;
import com.facishare.open.outer.oa.connector.common.api.result.SettingsResult;
import com.facishare.open.outer.oa.connector.i18n.I18NStringEnum;
import com.facishare.open.outer.oa.connector.i18n.I18NStringManager;
import com.google.common.collect.Lists;

import lombok.Data;

/**
 * <p>企业绑定</p>
 *
 * @version 1.0
 * @dateTime 2018-07-10 16:20
 * @anthor liqb
 */
@Data
@SecurityObj
public class DingEnterprise implements Serializable {

    @Id
    private Long id;

    /** 纷享ea  **/
    private String ea;

    /** 纷享EI **/
    private Integer ei;

    /** 纷享企业名称 **/
    private String enterpriseName;

    /** 钉钉企业标识 **/
    private String dingCorpId;

    private String agentId;

    /** 应用ID **/
    @SecurityField
    private String appKey;

    /** 应用secret **/
    @SecurityField
    private String appSecret;

    /** 免等待应用ID **/
    @SecurityField
    private String redirectAppId;

    /** 免等待应用secret **/
    @SecurityField
    private String redirectAppSecret;

    private String clientIp;

    @SecurityField
    private String token;

    /** 创建时间 **/
    private Date createTime;

    /** 更新时间 **/
    private Date updateTime;

    /** 创建人 **/
    private Integer createBy;

    /** 修改人 **/
    private Integer updateBy;

    /**是否使用回调**/
    private Integer isCallback;

    /**
     * 集成开发模式1/2
     */
    private Integer devModel;

    /** crm待办和提醒开关状态 0：推送待办 1：推送待办和提醒 2：推送提醒 3：关闭全部 **/
    private Integer alertStatus;

    /**
     * 全局index
     */
    private Integer allIndex;

    //账号自动绑定
    private Integer autBind;
    /**
     * 待办推送的位置  0：工作通知 1：钉钉待办 2：都推送
     */
    private Integer todoType;

    /**
     * 待办创建者ID，可以使用虚拟员工
     */
    private String todoCreator;

    /**
     * 待办创建者UnionID
     */
    private String todoCreatorUnionId;

    public static DingConnectorVo convert2ConnectInfo(DingEnterprise sourceData, I18NStringManager i18NStringManager) {
        DingConnectorVo connectInfo = new DingConnectorVo();
//        connectInfo.setBindStatus();
        connectInfo.setTodoType(sourceData.getTodoType());
        connectInfo.setTodoCreator(sourceData.getTodoCreator());
        connectInfo.setTodoCreatorUnionId(sourceData.getTodoCreatorUnionId());
        connectInfo.setChannel(ChannelEnum.dingding);
        connectInfo.setAppType(OuterOaAppInfoTypeEnum.selfBuild);
        connectInfo.setAuthType(AuthTypeEnum.OAUTH2.name());
        connectInfo.setAlertConfig(sourceData.convert2AlertConfig());
        connectInfo.setAlertTypes(sourceData.convert2AlertTypes());
        connectInfo.setDingCorpId(sourceData.getDingCorpId());
        connectInfo.setAppKey(sourceData.getAppKey());
        connectInfo.setAgentId(sourceData.getAgentId());
        connectInfo.setAppSecret(sourceData.getAppSecret());
        connectInfo.setRedirectAppId(sourceData.getRedirectAppId());
        connectInfo.setRedirectAppSecret(sourceData.getRedirectAppSecret());
        connectInfo.setClientIp(sourceData.getClientIp());
        connectInfo.setCreateBy(sourceData.getCreateBy());
        connectInfo.setUpdateBy(sourceData.getUpdateBy());
        connectInfo.setIsCallback(sourceData.getIsCallback());
        connectInfo.setAllIndex(sourceData.getAllIndex());
        return connectInfo;
    }

    public static Integer convert2AlertStatus(boolean alertConfig, List<AlertTypeEnum> alertTypes) {
        if (!alertConfig) {
            return 3;
        }
        final boolean todo = alertTypes.contains(AlertTypeEnum.CRM_TODO);
        final boolean notification = alertTypes.contains(AlertTypeEnum.CRM_NOTIFICATION);
        if (todo && notification) {
            return 1;
        }
        if (todo) {
            return 0;
        }
        return 2;
    }

    public boolean convert2AlertConfig() {
        return Objects.nonNull(alertStatus) && alertStatus != 3;
    }

    public List<AlertTypeEnum> convert2AlertTypes() {
        if (Objects.isNull(alertStatus) || alertStatus == 0) {
            return Lists.newArrayList(AlertTypeEnum.CRM_TODO);
        }
        if (alertStatus == 3) {
            return Lists.newArrayList();
        }
        if (alertStatus == 1) {
            return Lists.newArrayList(AlertTypeEnum.CRM_TODO, AlertTypeEnum.CRM_NOTIFICATION);
        }
        return Lists.newArrayList(AlertTypeEnum.CRM_NOTIFICATION);
    }

    public static Integer convert2DevModel(SettingsResult settingBindRules) {
        return Objects.nonNull(settingBindRules) && Objects.equals(settingBindRules.getSyncTypeEnum(), EnterpriseConfigAccountSyncTypeEnum.accountSync) ? 2 : 1;
    }

    public static Integer convert2AutBind(SettingsResult settingBindRules) {
        if (settingBindRules == null) {
            return null;
        }
        return Objects.equals(settingBindRules.getBindTypeEnum(), BindTypeEnum.auto) ? 1 : 0;
    }

    public static void main(String[] args) {
        // 获取DingEnterprise类的Class对象
        Class<DingEnterprise> clazz = DingEnterprise.class;

        // 检查是否存在@SecurityObj注解
        if (clazz.isAnnotationPresent(SecurityObj.class)) {
            // 获取@SecurityObj注解
            Annotation annotation = clazz.getAnnotation(SecurityObj.class);
            // 打印@SecurityObj注解的信息
            System.out.println("@SecurityObj annotation is present for DingEnterprise class. Annotation: " + annotation);
        } else {
            System.out.println("@SecurityObj annotation is not present for DingEnterprise class.");
        }
    }
}
