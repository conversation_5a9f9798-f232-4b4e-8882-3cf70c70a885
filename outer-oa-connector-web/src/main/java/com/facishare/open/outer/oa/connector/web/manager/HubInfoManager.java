package com.facishare.open.outer.oa.connector.web.manager;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.facishare.open.outer.oa.connector.web.openconnetor.model.HubInfo;
import com.facishare.open.outer.oa.connector.web.openconnetor.model.OuterConnector;
import com.facishare.open.outer.oa.connector.web.util.HubConfigCenter;
import com.facishare.rest.core.util.JacksonUtil;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class HubInfoManager {

    public HubInfo getHubInfo(String tenantId, String connectorApiName) {
        List<HubInfo> hubInfoList = this.getHubInfoList();
        HubInfo hubInfo = hubInfoList.stream()
                .filter(v -> v.supportInvoke(tenantId, connectorApiName))
                .findFirst()
                .orElseGet(null);
        return hubInfo;
    }

    public List<HubInfo> getHubInfoList() {
        //从配置中心读取hub地址
        String hubInfoStr = HubConfigCenter.hubInfo;
        if (hubInfoStr == null) {
            return Collections.emptyList();
        }
        List<HubInfo> configHubInfoList = JacksonUtil.fromJson(hubInfoStr, new com.fasterxml.jackson.core.type.TypeReference<List<HubInfo>>() {
        });
        //按排序
        configHubInfoList.sort(Comparator.comparingInt(v -> v.getOrder()));
        if (CollUtil.isNotEmpty(configHubInfoList)) {
            //初始化一下supportConnectorApiNames
            configHubInfoList.forEach(v -> v.getSupportConnectorApiNames());
        }
        return configHubInfoList;
    }
    public List<String> getHubInfoModuleList() {
        List<HubInfo> configHubInfoList = this.getHubInfoList();
        if(CollectionUtils.isEmpty(configHubInfoList)){
            return Lists.newArrayList();
        }
        List<String> result=Lists.newArrayList();
        for(HubInfo hubInfo:configHubInfoList){
            if(CollectionUtils.isNotEmpty(hubInfo.getOuterConnectors())){
                List<String> modules = hubInfo.getOuterConnectors().stream()
                        .filter(v -> StringUtils.isNotBlank(v.getModuleCode())).map(v -> v.getModuleCode()).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(modules)){
                    result.addAll(modules);
                }
            }
        }
        return result;
    }

    public List<OuterConnector> getFreeOuterConnector(String tenantId) {
        List<OuterConnector> result=Lists.newArrayList();
        List<HubInfo> configHubInfoList = this.getHubInfoList();
        if(CollectionUtils.isEmpty(configHubInfoList)){
            return result;
        }
        //dev_开头的就是免费的
        configHubInfoList=configHubInfoList.stream().filter(v-> StrUtil.startWith(v.getName(),"dev_")).collect(Collectors.toList());
        for(HubInfo hubInfo:configHubInfoList){
            if(CollectionUtils.isNotEmpty(hubInfo.getOuterConnectors())){
                if(CollectionUtils.isNotEmpty(hubInfo.getTenantIds())&&!hubInfo.getTenantIds().contains(tenantId)){
                    continue;
                }
                List<OuterConnector> connectors = hubInfo.getOuterConnectors();
                if(CollectionUtils.isNotEmpty(connectors)){
                    result.addAll(connectors);
                }
            }
        }
        return result;
    }

    public List<OuterConnector> getOuterConnectorByModuleCode(String tenantId,String moduleCode) {
        List<HubInfo> configHubInfoList = this.getHubInfoList();
        if(CollectionUtils.isEmpty(configHubInfoList)){
            return Lists.newArrayList();
        }
        List<OuterConnector> result=Lists.newArrayList();
        for(HubInfo hubInfo:configHubInfoList){
            if(CollectionUtils.isNotEmpty(hubInfo.getOuterConnectors())){
                if(CollectionUtils.isNotEmpty(hubInfo.getTenantIds())&&!hubInfo.getTenantIds().contains(tenantId)){
                    continue;
                }
                List<OuterConnector> connectors = hubInfo.getOuterConnectors().stream()
                        .filter(v -> StringUtils.isBlank(v.getModuleCode())&&v.getModuleCode().equals(moduleCode)).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(connectors)){
                    result.addAll(connectors);
                }
            }
        }
        return result;
    }
}
