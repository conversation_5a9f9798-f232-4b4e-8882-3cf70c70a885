package com.facishare.open.oa.base.dbproxy.pg.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.facishare.open.oa.base.dbproxy.exception.OaDbException;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.mapper.OuterOaEnterpriseBindMapper;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaEnterpriseBindParams;
import com.facishare.open.outer.oa.connector.common.api.enums.BindStatusEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.facishare.open.outer.oa.connector.i18n.I18NStringEnum;
import com.fxiaoke.api.IdGenerator;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
public class OuterOaEnterpriseBindManager {
    @Resource
    private OuterOaEnterpriseBindMapper outerOaEnterpriseBindMapper;

    /**
     * 查询企业绑定关系
     *
     * 根据id查询企业绑定关系
     *
     * @param id 绑定关系ID
     * @return 企业绑定关系实体
     */
    public OuterOaEnterpriseBindEntity getEntityById(String id) {
        return outerOaEnterpriseBindMapper.selectById(id);
    }

    public String getDcIdByEaAndAppId(ChannelEnum channel, String fsEa, String appId) {
        LambdaQueryWrapper<OuterOaEnterpriseBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OuterOaEnterpriseBindEntity::getChannel, channel);
        wrapper.eq(OuterOaEnterpriseBindEntity::getFsEa, fsEa);
        wrapper.eq(OuterOaEnterpriseBindEntity::getAppId, appId);

        OuterOaEnterpriseBindEntity entity = outerOaEnterpriseBindMapper.selectOne(wrapper);
        return Objects.isNull(entity) ? null : entity.getId();
    }

    /**
     * 查询企业绑定关系
     *
     * @param channel
     * @param fsEa
     * @param outEa
     * @param appId
     * @return
     */
    public OuterOaEnterpriseBindEntity getEntity(ChannelEnum channel, String fsEa, String outEa, String appId) {
        LambdaQueryWrapper<OuterOaEnterpriseBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OuterOaEnterpriseBindEntity::getChannel, channel);
        wrapper.eq(OuterOaEnterpriseBindEntity::getFsEa, fsEa);
        if (StringUtils.isNotEmpty(outEa)) {
            wrapper.eq(OuterOaEnterpriseBindEntity::getOutEa, outEa);
        }
        if(StringUtils.isNotEmpty(appId)){
            wrapper.eq(OuterOaEnterpriseBindEntity::getAppId, appId);
        }

        OuterOaEnterpriseBindEntity entity = outerOaEnterpriseBindMapper.selectOne(wrapper);

        return entity;
    }

    /**
     * 根据fsEa查询企业绑定关系
     */
    public List<OuterOaEnterpriseBindEntity> queryByFsEa(ChannelEnum channel, String fsEa) {
        LambdaQueryWrapper<OuterOaEnterpriseBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OuterOaEnterpriseBindEntity::getChannel, channel)
                .eq(OuterOaEnterpriseBindEntity::getFsEa, fsEa)
                .eq(OuterOaEnterpriseBindEntity::getBindStatus, BindStatusEnum.normal);
        return outerOaEnterpriseBindMapper.selectList(wrapper);
    }

    /**
     * 插入企业绑定关系
     *
     * @param entity
     * @return
     */
    public Integer insert(OuterOaEnterpriseBindEntity entity) {
        if (StringUtils.isEmpty(entity.getId())) {
            entity.setId(IdGenerator.get());
        }
        return outerOaEnterpriseBindMapper.insert(entity);
    }

    /**
     * 通过id更新企业绑定关系
     *
     * @param entity
     * @return
     */
    public Integer updateById(OuterOaEnterpriseBindEntity entity) {
        return outerOaEnterpriseBindMapper.updateById(entity);
    }

    /**
     * 通过企业ea更新企业绑定状态为normal
     *
     * @param ea 企业账号
     * @return 更新的记录数
     */
    public Integer updateByEaNormal(String ea) {
        return updateByEa(ea, null, BindStatusEnum.normal);
    }
    
    /**
     * 通过企业ea和渠道更新企业绑定状态为normal
     *
     * @param ea 企业账号
     * @param channel 渠道，如果为null则更新所有渠道
     * @return 更新的记录数
     */
    public Integer updateByEaNormal(String ea, ChannelEnum channel) {
        return updateByEa(ea, channel, BindStatusEnum.normal);
    }
    
    /**
     * 通过企业ea和渠道更新企业绑定状态
     *
     * @param ea 企业账号
     * @param channel 渠道，如果为null则更新所有渠道
     * @param status 要更新的状态
     * @return 更新的记录数
     */
    public Integer updateByEa(String ea, ChannelEnum channel, BindStatusEnum status) {
        if (StringUtils.isEmpty(ea) || status == null) {
            return 0;
        }
        
        LambdaQueryWrapper<OuterOaEnterpriseBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OuterOaEnterpriseBindEntity::getFsEa, ea);
        if (channel != null) {
            wrapper.eq(OuterOaEnterpriseBindEntity::getChannel, channel);
        }
        
        OuterOaEnterpriseBindEntity updateEntity = new OuterOaEnterpriseBindEntity();
        updateEntity.setBindStatus(status);
        updateEntity.setUpdateTime(System.currentTimeMillis());
        
        return outerOaEnterpriseBindMapper.update(updateEntity, wrapper);
    }

    /**
     * 通过业务id更新企业绑定关系
     * @param entity
     * @return
     */
    public Integer updateByBusinessId(OuterOaEnterpriseBindEntity entity) {
        final LambdaQueryWrapper<OuterOaEnterpriseBindEntity> queryWrapper = new LambdaQueryWrapper<OuterOaEnterpriseBindEntity>()
        .eq(OuterOaEnterpriseBindEntity::getChannel, entity.getChannel())
        .eq(OuterOaEnterpriseBindEntity::getFsEa, entity.getFsEa())
        .eq(OuterOaEnterpriseBindEntity::getAppId, entity.getAppId());

        entity.setAppId(null);
        entity.setChannel(null);
        entity.setFsEa(null);
        entity.setId(null);
        return outerOaEnterpriseBindMapper.update(entity, queryWrapper);
    }

    public List<OuterOaEnterpriseBindEntity> getNormalEntitiesByFsEa(ChannelEnum channel, String fsEa) {
        LambdaQueryWrapper<OuterOaEnterpriseBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OuterOaEnterpriseBindEntity::getChannel, channel);
        wrapper.eq(OuterOaEnterpriseBindEntity::getFsEa, fsEa);
        wrapper.eq(OuterOaEnterpriseBindEntity::getBindStatus, BindStatusEnum.normal);

        return outerOaEnterpriseBindMapper.selectList(wrapper);
    }

    public List<OuterOaEnterpriseBindEntity> getEntitiesByFsEa(ChannelEnum channel, String fsEa) {
        LambdaQueryWrapper<OuterOaEnterpriseBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OuterOaEnterpriseBindEntity::getChannel, channel);
        wrapper.eq(OuterOaEnterpriseBindEntity::getFsEa, fsEa);

        List<OuterOaEnterpriseBindEntity> getEntitiesByFsEa = outerOaEnterpriseBindMapper.selectList(wrapper);

        return getEntitiesByFsEa;
    }

    public List<OuterOaEnterpriseBindEntity> getEntitiesNormalByChanelEnums(List<ChannelEnum> channel, String fsEa,String outerEa) {
        LambdaQueryWrapper<OuterOaEnterpriseBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(OuterOaEnterpriseBindEntity::getChannel, channel);
        if(StringUtils.isNotEmpty(outerEa)){
            wrapper.eq(OuterOaEnterpriseBindEntity::getOutEa, outerEa);
        }
        if(StringUtils.isNotEmpty(fsEa)){
            wrapper.eq(OuterOaEnterpriseBindEntity::getFsEa, fsEa);
        }
        wrapper.eq(OuterOaEnterpriseBindEntity::getBindStatus, BindStatusEnum.normal);
        List<OuterOaEnterpriseBindEntity> getEntitiesByFsEa = outerOaEnterpriseBindMapper.selectList(wrapper);

        return getEntitiesByFsEa;
    }



    public List<OuterOaEnterpriseBindEntity> getEntitiesByOuterEaChanelEnums(List<ChannelEnum> channel,  String fsEa,String outerEa,BindStatusEnum statusEnum) {
        LambdaQueryWrapper<OuterOaEnterpriseBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(OuterOaEnterpriseBindEntity::getChannel, channel);
        if(StringUtils.isNotEmpty(outerEa)){
            wrapper.eq(OuterOaEnterpriseBindEntity::getOutEa, outerEa);
        }
        if(StringUtils.isNotEmpty(fsEa)){
            wrapper.eq(OuterOaEnterpriseBindEntity::getFsEa, fsEa);
        }
        if(statusEnum != null){
            wrapper.eq(OuterOaEnterpriseBindEntity::getBindStatus, statusEnum);
        }
        List<OuterOaEnterpriseBindEntity> getEntitiesByFsEa = outerOaEnterpriseBindMapper.selectList(wrapper);

        return getEntitiesByFsEa;
    }

    public OuterOaEnterpriseBindEntity getEntityByFsEa(String fsEa, BindStatusEnum bindStatusEnum){
        OuterOaEnterpriseBindParams outerOaEnterpriseBindParams=OuterOaEnterpriseBindParams.builder().fsEa(fsEa).bindStatus(bindStatusEnum).build();
        List<OuterOaEnterpriseBindEntity> entities = this.getEntities(outerOaEnterpriseBindParams);
        if(CollectionUtils.isNotEmpty(entities)){
            return entities.get(0);
        }
        return null;
    }



    public List<OuterOaEnterpriseBindEntity> getEntitiesByFsOuterEA(ChannelEnum channel, String fsEa,String outEa) {
        LambdaQueryWrapper<OuterOaEnterpriseBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OuterOaEnterpriseBindEntity::getChannel, channel);
        wrapper.eq(OuterOaEnterpriseBindEntity::getFsEa, fsEa);
        wrapper.eq(OuterOaEnterpriseBindEntity::getOutEa, outEa);

        List<OuterOaEnterpriseBindEntity> getEntitiesByFsEa = outerOaEnterpriseBindMapper.selectList(wrapper);

        return getEntitiesByFsEa;
    }

    public List<OuterOaEnterpriseBindEntity> getEntitiesByAppId(ChannelEnum channel, String appId) {
        LambdaQueryWrapper<OuterOaEnterpriseBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OuterOaEnterpriseBindEntity::getChannel,channel);
        wrapper.eq(OuterOaEnterpriseBindEntity::getAppId,appId);
        wrapper.eq(OuterOaEnterpriseBindEntity::getBindStatus, BindStatusEnum.normal);

        List<OuterOaEnterpriseBindEntity> getEntitiesByAppId = outerOaEnterpriseBindMapper.selectList(wrapper);

        return getEntitiesByAppId;
    }

    public OuterOaEnterpriseBindEntity getByFsEaAndAppId(ChannelEnum channel, String fsEa, String appId) {
        // appId为空时，查看是否只有一个绑定关系, 如果有多个，则报错
        if (StringUtils.isEmpty(appId)) {
            final List<OuterOaEnterpriseBindEntity> entitiesByFsEa = getNormalEntitiesByFsEa(channel, fsEa);
            if (CollectionUtils.isEmpty(entitiesByFsEa)) {
                return null;
            } else if (entitiesByFsEa.size() > 1) {
                throw new OaDbException(I18NStringEnum.cxb002);
            } else {
                return entitiesByFsEa.get(0);
            }
        }
        LambdaQueryWrapper<OuterOaEnterpriseBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OuterOaEnterpriseBindEntity::getChannel,channel);
        wrapper.eq(OuterOaEnterpriseBindEntity::getFsEa,fsEa);
        wrapper.eq(OuterOaEnterpriseBindEntity::getAppId,appId);

        return outerOaEnterpriseBindMapper.selectOne(wrapper);
    }

    public List<OuterOaEnterpriseBindEntity> getEntitiesByOutEa(ChannelEnum channel, String outEa, String appId) {
        LambdaQueryWrapper<OuterOaEnterpriseBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OuterOaEnterpriseBindEntity::getChannel, channel);
        wrapper.eq(OuterOaEnterpriseBindEntity::getOutEa, outEa);
        if (StringUtils.isNotEmpty(appId)) {
            wrapper.eq(OuterOaEnterpriseBindEntity::getAppId, appId);
        }

        List<OuterOaEnterpriseBindEntity> getEntitiesByOutEa = outerOaEnterpriseBindMapper.selectList(wrapper);

        return getEntitiesByOutEa;
    }

    public List<OuterOaEnterpriseBindEntity> getEntitiesByAppId(ChannelEnum channel, String fsEa, String outEa, String appId) {
        LambdaQueryWrapper<OuterOaEnterpriseBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OuterOaEnterpriseBindEntity::getChannel,channel);
        wrapper.eq(OuterOaEnterpriseBindEntity::getFsEa,fsEa);
        wrapper.eq(OuterOaEnterpriseBindEntity::getOutEa,outEa);
        if (StringUtils.isNotEmpty(appId)) {
            wrapper.eq(OuterOaEnterpriseBindEntity::getAppId,appId);
        }
        return outerOaEnterpriseBindMapper.selectList(wrapper);
    }

    //delete
    public Integer deleteById(OuterOaEnterpriseBindEntity entity) {
        return outerOaEnterpriseBindMapper.deleteById(entity);
    }

    /**
     * 根据参数查询企业绑定关系列表
     *
     * @param params 查询参数
     * @return 企业绑定实体列表
     */
    public List<OuterOaEnterpriseBindEntity> getEntities(OuterOaEnterpriseBindParams params) {
        LambdaQueryWrapper<OuterOaEnterpriseBindEntity> wrapper = convertMapper(params);
        return outerOaEnterpriseBindMapper.selectList(wrapper);
    }
    /**
     * 根据参数查询企业绑定关系列表
     *
     * @param params 查询参数
     * @return 企业绑定实体列表
     */
    public OuterOaEnterpriseBindEntity getEntity(OuterOaEnterpriseBindParams params) {
        LambdaQueryWrapper<OuterOaEnterpriseBindEntity> wrapper = convertMapper(params);
        wrapper.orderByDesc(OuterOaEnterpriseBindEntity::getCreateTime);
        wrapper.last("limit 1");
        return outerOaEnterpriseBindMapper.selectOne(wrapper);
    }

    @NotNull
    private static LambdaQueryWrapper<OuterOaEnterpriseBindEntity> convertMapper(OuterOaEnterpriseBindParams params) {
        LambdaQueryWrapper<OuterOaEnterpriseBindEntity> wrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotEmpty(params.getId())) {
            wrapper.eq(OuterOaEnterpriseBindEntity::getId, params.getId());
        }
        if (StringUtils.isNotEmpty(params.getFsEa())) {
            wrapper.eq(OuterOaEnterpriseBindEntity::getFsEa, params.getFsEa());
        }
        if (StringUtils.isNotEmpty(params.getOutEa())) {
            wrapper.eq(OuterOaEnterpriseBindEntity::getOutEa, params.getOutEa());
        }
        if (StringUtils.isNotEmpty(params.getAppId())) {
            wrapper.eq(OuterOaEnterpriseBindEntity::getAppId, params.getAppId());
        }
        if (params.getBindType() != null) {
            wrapper.eq(OuterOaEnterpriseBindEntity::getBindType, params.getBindType());
        }
        if (params.getChannel() != null) {
            wrapper.eq(OuterOaEnterpriseBindEntity::getChannel, params.getChannel());
        }
        if (params.getBindStatus() != null) {
            wrapper.eq(OuterOaEnterpriseBindEntity::getBindStatus, params.getBindStatus());
        }
        return wrapper;
    }

    public OuterOaEnterpriseBindEntity selectEntityLimitOne(OuterOaEnterpriseBindParams outerOaEnterpriseBindParams){
        LambdaQueryWrapper<OuterOaEnterpriseBindEntity> wrapper = convertMapper(outerOaEnterpriseBindParams);
        wrapper.last("limit 1");
        OuterOaEnterpriseBindEntity outerOaEnterpriseBindEntity = outerOaEnterpriseBindMapper.selectOne(wrapper);
        return outerOaEnterpriseBindEntity;

    }

    /**
     * 批量插入或更新企业绑定关系 如果记录已存在（根据id）则更新，不存在则插入
     *
     * @param entities 需要插入或更新的实体列表
     * @return 受影响的记录数
     */
    public Integer batchUpsert(List<OuterOaEnterpriseBindEntity> entities) {
        if (entities == null || entities.isEmpty()) {
            return 0;
        }

        // 为没有id的实体生成id
        entities.forEach(entity -> {
            if (StringUtils.isEmpty(entity.getId())) {
                entity.setId(IdGenerator.get());
            }
            // 设置时间戳
            long now = System.currentTimeMillis();
            if (entity.getCreateTime() == null) {
                entity.setCreateTime(now);
            }
            entity.setUpdateTime(now);
        });

        return outerOaEnterpriseBindMapper.batchUpsert(entities);
    }

    /**
     * 批量插入或更新企业绑定关系（基于ID） 如果记录已存在（根据id）则更新，不存在则插入 当主键ID冲突时，只更新指定字段，保持ID不变
     *
     * @param entities 需要插入或更新的实体列表
     * @return 受影响的记录数
     */
    public Integer batchUpsertById(List<OuterOaEnterpriseBindEntity> entities) {
        if (entities == null || entities.isEmpty()) {
            return 0;
        }

        // 为没有id的实体生成id
        entities.forEach(entity -> {
            if (StringUtils.isEmpty(entity.getId())) {
                entity.setId(IdGenerator.get());
            }
            // 设置时间戳
            long now = System.currentTimeMillis();
            if (entity.getCreateTime() == null) {
                entity.setCreateTime(now);
            }
            entity.setUpdateTime(now);
        });

        return outerOaEnterpriseBindMapper.batchUpsertById(entities);
    }

    /**
     * 更新重复索引
     * @param fsEa 企业账号
     * @return 更新数量
     */
    public Integer updateRepeatIndex(String fsEa, ChannelEnum channel) {
        return outerOaEnterpriseBindMapper.updateRepeatIndex(fsEa, channel);
    }

    /**
     * 更新初始化状态
     * @param fsEa 企业账号
     * @return 更新数量
     */
    public Integer updateInitStatus(String fsEa, ChannelEnum channel) {
        return outerOaEnterpriseBindMapper.updateInitStatus(fsEa, channel);
    }

    public Integer deleteByBusinessId(String fsEa, String appKey, ChannelEnum channelEnum) {
        return outerOaEnterpriseBindMapper.deleteByBusinessId(fsEa, appKey, channelEnum);
    }

    public List<OuterOaEnterpriseBindEntity> getAllByChannel(ChannelEnum channelEnum) {
        return outerOaEnterpriseBindMapper.selectList(new LambdaQueryWrapper<OuterOaEnterpriseBindEntity>()
                .eq(OuterOaEnterpriseBindEntity::getChannel, channelEnum)
                .eq(OuterOaEnterpriseBindEntity::getBindStatus, BindStatusEnum.normal));
    }

    public String getAppIdByDcId(String enterpriseAccount, String dcId) {
        LambdaQueryWrapper<OuterOaEnterpriseBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OuterOaEnterpriseBindEntity::getChannel, ChannelEnum.dingding)
                .eq(OuterOaEnterpriseBindEntity::getFsEa, enterpriseAccount);
        if (StringUtils.isNotEmpty(dcId)) {
            wrapper.eq(OuterOaEnterpriseBindEntity::getId, dcId);
        }
        OuterOaEnterpriseBindEntity entity = outerOaEnterpriseBindMapper.selectOne(wrapper);
        if (entity == null) {
            throw new OaDbException(I18NStringEnum.cxb003);
        }
        return entity.getAppId();
    }

    /**
     * 根据fsEa和appId获取outEa，兼容appId为null的情况
     *
     * @param channel 渠道枚举
     * @param fsEa 纷享企业账号
     * @param appId 应用ID，可以为null
     * @return 外部企业账号，如果没有找到返回null
     */
    public String getOutEaByFsEaAndAppId(ChannelEnum channel, String fsEa, String appId) {
        OuterOaEnterpriseBindEntity bindEntity = getByFsEaAndAppId(channel, fsEa, appId);
        return bindEntity != null ? bindEntity.getOutEa() : null;
    }

    public Integer superUpdateData(String sqlStr) {
        return outerOaEnterpriseBindMapper.superUpdateSql(sqlStr);
    }

    public long getStatusCreateEnterprise(String fsEa,ChannelEnum channelEnum,String connectorKey){
        LambdaQueryWrapper<OuterOaEnterpriseBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OuterOaEnterpriseBindEntity::getChannel,channelEnum);
        wrapper.eq(OuterOaEnterpriseBindEntity::getFsEa,fsEa);
        wrapper.eq(OuterOaEnterpriseBindEntity::getBindStatus,BindStatusEnum.create);
        if(channelEnum!=ChannelEnum.standard_oa){
            return outerOaEnterpriseBindMapper.selectCount(wrapper);
        }else{
            List<OuterOaEnterpriseBindEntity> outerOaEnterpriseBindEntities = outerOaEnterpriseBindMapper.selectList(wrapper);
            if(CollectionUtils.isNotEmpty(outerOaEnterpriseBindEntities)){
                outerOaEnterpriseBindEntities=outerOaEnterpriseBindEntities.stream().filter(v->connectorKey.equals(v.getConnectParams().getStandard_oa().getApiName())).collect(Collectors.toList());
                return outerOaEnterpriseBindEntities.size();
            }else {
                return 0L;
            }

        }

    }
}
