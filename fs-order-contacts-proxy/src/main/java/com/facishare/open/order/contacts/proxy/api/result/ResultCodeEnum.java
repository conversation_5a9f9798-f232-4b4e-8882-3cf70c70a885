package com.facishare.open.order.contacts.proxy.api.result;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

@Getter
@ToString
@AllArgsConstructor
public enum ResultCodeEnum {
    /**
     * 成功
     **/
    SUCCESS(0, "成功"),

    /**
     * 参数不合法
     */
    PARAM_ILLEGAL(10000, "参数不合法"),
    SYSTEM_ERROR(10001, "系统错误"),
    CREATE_CUSTOM_FAILED(10002,"客户创建失败"),
    CREATE_ORDER_FAILED(10004,"订单创建失败"),
    EVENT_NOT_EXIST(10006,"事件不存在"),
    DEPT_NAME_IS_EXIST(201112001, "部门名称已存在"),
    EMPLOYEE_NAME_IS_EXIST(201112001, "人员名称已存在"),
    ;

    /**
     * 错误码
     */
    private final int code;
    /**
     * 错误信息
     */
    private final String msg;
}
