package com.facishare.open.erpdss.outer.oa.connector.base.outer.event.contacts;

import com.facishare.open.erpdss.outer.oa.connector.base.HandlerTemplate;
import com.facishare.open.erpdss.outer.oa.connector.base.context.TemplateResult;
import com.facishare.open.erpdss.outer.oa.connector.base.outer.event.app.AppVisibleRangeChangeEventHandlerTemplate;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

/**
 * 外部部门事件处理器模型
 * <AUTHOR>
 * @date 2024-08-20
 */

@Slf4j
public class DepEventHandlerTemplate extends HandlerTemplate {
    @Resource
    private AppVisibleRangeChangeEventHandlerTemplate appVisibleRangeChangeEventHandlerTemplate;

    @Override
    public TemplateResult execute(Object data) {
        return appVisibleRangeChangeEventHandlerTemplate.execute(data);
    }
}
