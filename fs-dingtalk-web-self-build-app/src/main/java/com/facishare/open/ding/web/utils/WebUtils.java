package com.facishare.open.ding.web.utils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 包装请求上下文数据的ThreadLocal 集合
 *
 * @dateTime 2018/7/17 14:59
 * <AUTHOR> y<PERSON><PERSON>@fxiaoke.com
 * @version 1.0
 */
public class WebUtils {

    private static ThreadLocal<HttpServletRequest> webRequest = null;

    private static ThreadLocal<HttpServletResponse> webResponse = null;
    
    private static ThreadLocal<String> webReqIP = null;
    
    private static ThreadLocal<String> reqURI = null;
    
    static {
        webRequest = new ThreadLocal<HttpServletRequest>();
        webResponse = new ThreadLocal<HttpServletResponse>();
        webReqIP = new ThreadLocal<String>();
        reqURI = new ThreadLocal<String>();
    }

    public static void setRequest(HttpServletRequest request) {
        webRequest.set(request);
    }

    public static HttpServletRequest getRequest() {
        return webRequest.get();
    }

    public static void setResponse(HttpServletResponse response) {
        webResponse.set(response);
    }

    public static HttpServletResponse getResponse() {
        return webResponse.get();
    }
    
    public static void setReqIP(String reqIP) {
        webReqIP.set(reqIP);
    }

    public static String getReqIP() {
        return webReqIP.get();
    }

    public static String getReqURI() {
        return reqURI.get();
    }

    public static void setReqURI(String reqURI) {
        WebUtils.reqURI.set(reqURI);
    }

    public static void clear() {
        webRequest.remove();
        webResponse.remove();
        webReqIP.remove();
        reqURI.remove();
    }

}
