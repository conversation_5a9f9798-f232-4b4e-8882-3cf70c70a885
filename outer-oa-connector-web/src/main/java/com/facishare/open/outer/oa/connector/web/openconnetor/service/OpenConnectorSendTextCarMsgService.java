package com.facishare.open.outer.oa.connector.web.openconnetor.service;


import com.facishare.open.erpdss.outer.oa.connector.base.context.TemplateResult;
import com.facishare.open.outer.oa.connector.common.api.result.Result;
import com.facishare.open.outer.oa.connector.web.openconnetor.model.OuterMsgModel;
import com.fxiaoke.message.extrnal.platform.model.arg.CreateTodoArg;
import com.fxiaoke.message.extrnal.platform.model.arg.SendTextCardMessageArg;

public interface OpenConnectorSendTextCarMsgService {
    /**
     * 过滤消息
     * @param context
     */
    Result<Void> filterTextCarMsg(String tenantId, String dataCenterId, String connectorApiName, SendTextCardMessageArg context);

    /**
     * 组装消息
     * @param context
     */
    Result<OuterMsgModel> buildTextCarMsg(String tenantId, String dataCenterId, String connectorApiName,SendTextCardMessageArg context);

    /**
     * 发送消息
     * @param context
     */
    Result<Void> sendTextCarMsg(String tenantId, String dataCenterId, String connectorApiName,OuterMsgModel context);
}
