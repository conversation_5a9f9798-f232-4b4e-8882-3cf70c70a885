package com.facishare.open.ding.provider.model.arg;

/**
 * Created by guoyd on 2019/7/24.
 */
public enum ValueToApiName {
    AccountObj(1,"AccountObj"),//客户
    SalesOrderObj(2,"SalesOrderObj"), //销售订单
    ContactObj(3,"ContactObj"),// 联系人
    StockObj(4,"StockObj"), //纷享库存
    InvoiceApplicationObj(5,"InvoiceApplicationObj"), //开票申请
    PaymentObj(6,"PaymentObj"), //回款
    ProductObj(7,"ProductObj"), //产品
    DeliveryNoteObj(8,"DeliveryNoteObj"), //发货单
    ErpWarehouseObj(9,"ErpWarehouseObj"), //ERP仓库
    EmpObj(10,"EmpObj"),//员工
    DepObj(11,"DepObj"),//部门
    ;

    private Integer value;
    private String apiName;

    ValueToApiName(Integer value,String apiName){
        this.value=value;
        this.apiName=apiName;
    }

    public static String getApiNameByValue(Integer valueKey){
        for(ValueToApiName valueToApiName:ValueToApiName.values()){
            if(valueToApiName.getValue().equals(valueKey)){
                return valueToApiName.getApiName();
            }
        }
        return null;
    }

    public String getApiName(){
        return apiName;
    }
    public  void setApiName(String apiName){
        this.apiName=apiName;
    }
    public Integer getValue(){
        return value;
    }
    public  void setValue(Integer value){
        this.value=value;
    }
}
