package com.facishare.open.oa.base.dbproxy.transfer.handler;

import com.facishare.transfer.handler.page.PageTransferHandler;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IConfig;
import com.google.common.util.concurrent.RateLimiter;
import org.springframework.beans.factory.InitializingBean;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/2/23 16:37:10
 */
public abstract class OaRateLimitPageTransferHandler<K, RV, CV>  extends PageTransferHandler<K, RV, CV> implements InitializingBean {
    protected static RateLimiter rateLimiter;

    @Override
    public void afterPropertiesSet() {
        ConfigFactory.getConfig("fs-erpdss-oa-base-config", "transfer-config", this::initRateLimiter, true);
    }

    @Override
    protected void checkAndTransfer(final int enterpriseId, final boolean transferAnyway, final Map<K, CV> targetData, final K k, final RV rv) throws Throwable {
        rateLimiter.acquire();
        super.checkAndTransfer(enterpriseId, transferAnyway, targetData, k, rv);
    }

    @Override
    public int getThreadNum() {
        return 3;
    }

    @Override
    public int tryNum() {
        return 1;
    }

    protected void initRateLimiter(final IConfig iConfig) {
        rateLimiter = RateLimiter.create(iConfig.getInt("ratelimit", 10000));
    }

    public abstract List<String> getAllTenantIds();

    public abstract List<String> getTenantIdsByTime(Long startTime);

    public void cleanCache() {
        return;
    }
}
