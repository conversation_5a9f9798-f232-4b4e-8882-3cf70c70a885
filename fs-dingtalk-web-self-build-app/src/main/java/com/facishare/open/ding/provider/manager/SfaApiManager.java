package com.facishare.open.ding.provider.manager;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.facishare.open.ding.provider.config.ConfigCenter;
import com.facishare.open.ding.provider.model.HttpResponseMessage;
import com.facishare.open.ding.provider.utils.OkHttp3MonitorUtilsFromProvider;
import com.facishare.open.outer.oa.connector.common.api.params.ObjectData;
import com.facishare.open.outer.oa.connector.common.api.result.Result;
import com.facishare.open.outer.oa.connector.common.api.result.ResultCodeEnum;
import com.fxiaoke.otherrestapi.function.arg.FunctionServiceExecuteArg;
import com.fxiaoke.otherrestapi.function.data.HeaderObj;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.rmi.RemoteException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@Slf4j
// IgnoreI18nFile
public class SfaApiManager {

    /**
     * CRM系统管理员身份
     **/
    private final int USER_SYSTEM = -10000;

    public Result<Object> executeCustomFunction(HeaderObj headerObj, FunctionServiceExecuteArg arg) throws RemoteException {
        Map<String, String> headerMap = getHeader(headerObj);

        String url = Joiner.on(StringUtils.EMPTY).join(ConfigCenter.PAAS_FUNCTION_URL, "/v1/function/currencyFunction");

//        ObjectData objectData = proxyHttpClient.postUrl(url, arg, headerMap, new TypeReference<ObjectData>() {
//        });
        HttpResponseMessage httpResponseMessage = OkHttp3MonitorUtilsFromProvider.sendOkHttp3Post(url, headerMap, JSONObject.toJSONString(arg));
        String content = httpResponseMessage.getContent();
        if (Strings.isNullOrEmpty(content)) {
            return Result.newError(httpResponseMessage.getStatusCode(),httpResponseMessage.getMessage());
        }
        ObjectData objectData = ObjectData.convert(JSONObject.parseObject(content));
        log.debug("CustomFunctionServiceImpl.currencyFunction,objectData={}", objectData);

        if (objectData == null) {
            return Result.newError(ResultCodeEnum.FUNCTION_ERROR.getCode(), "调用函数失败");
        }

        String errCode = objectData.getString("errCode");
        String errMessage = objectData.getString("errMessage");
        Map<String, Object> resultMap = objectData.getMap("result");

        if (resultMap == null)
            return Result.newError(ResultCodeEnum.FUNCTION_ERROR.getCode(), "调用函数失败");

        Boolean success = MapUtils.getBoolean(resultMap, "success");
        String errorInfo = MapUtils.getString(resultMap, "errorInfo");

        if (success == false) {
            List<String> items = Splitter.on(":::").splitToList(errorInfo);
            if (CollectionUtils.isNotEmpty(items) && items.size() == 2) {
                String code = "0";
                try {
                    code = items.get(0);
                } catch (Exception e) {
                    code = errCode;
                }
                return Result.newError(Integer.parseInt(code), items.get(1));
            }
        }

        if (success != null && success == false) {
            return Result.newError(Integer.parseInt(errCode), errMessage);
        }
        return new Result<>(resultMap.get("functionResult"));
    }

    private Map<String, String> getHeader(HeaderObj headerObj) {
        Map<String, String> headerMap = new HashMap<>();
        headerObj.forEach((key, value) -> {
            headerMap.put(key, value == null ? "" : value.toString());
        });
        return headerMap;
    }
}
