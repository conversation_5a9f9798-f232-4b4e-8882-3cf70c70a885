package com.facishare.open.oa.base.dbproxy.pg.entity;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.facishare.open.outer.oa.connector.common.api.admin.BaseConnectorVo;
import com.facishare.open.outer.oa.connector.common.api.annotation.SecurityField;
import com.facishare.open.outer.oa.connector.common.api.annotation.SecurityObj;
import com.facishare.open.outer.oa.connector.common.api.enums.BindStatusEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.BindTypeEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.facishare.open.outer.oa.connector.common.api.result.OuterOAConnectSettingResult;
import com.facishare.open.outer.oa.connector.common.api.utils.ConnectorVoUtils;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

@Data
@TableName("outer_oa_enterprise_bind")
@SecurityObj(value = OuterOaEnterpriseBindEntity.class)
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class OuterOaEnterpriseBindEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId
    private String id; // 数据中心id

    private ChannelEnum channel; // 渠道

    private String fsEa; // 纷享企业 ea

    private String outEa; // 外部企业 ea

    private String appId; // 外部应用 appId

    @SecurityField
    private String connectInfo; // 连接参数

    /**
     * 绑定类型
     */
    private BindTypeEnum bindType;
    /**
     * 绑定状态
     */
    private BindStatusEnum bindStatus;

    private Long createTime; // 创建时间

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateTime; // 修改时间

    /**
     * 亲测，钉钉的  转不了，不知道依赖了什么
     */
    @Deprecated
    public OuterOAConnectSettingResult.ConnectParams getConnectParams(){
        OuterOAConnectSettingResult.ConnectParams connectParams = new OuterOAConnectSettingResult.ConnectParams();
        BaseConnectorVo connectorVo;
        try {
            if(this.getChannel()==null|| StringUtils.isBlank(this.getConnectInfo())){
                return null;
            }
            // 1. 获取对应的ConnectorVo类
            Class<? extends BaseConnectorVo> connectorClass = this.getChannel().getClassName();
            // 2. 将String类型的connectInfo反序列化为对应的ConnectorVo
            connectorVo = JSONObject.parseObject(this.getConnectInfo(), connectorClass);

            // 3. 将ConnectorVo设置到connectParams中
            ConnectorVoUtils.setConnectorVoToConnectParams(connectParams, connectorVo, this.getChannel());
        } catch (Exception e) {
            return null;
        }
        return connectParams;
    }
}
