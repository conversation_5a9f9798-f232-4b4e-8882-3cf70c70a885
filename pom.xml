<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.fxiaoke.common</groupId>
        <artifactId>fxiaoke-parent-pom</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <groupId>com.facishare.open</groupId>
    <artifactId>fs-open-feishu-gateway</artifactId>
    <packaging>pom</packaging>
    <version>1.0-SNAPSHOT</version>

    <dependencies>

        <!-- Utils -->
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
            <version>4.1</version>
        </dependency>

        <!-- GSon -->
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>transmittable-thread-local</artifactId>
            <version>2.14.2</version>
        </dependency>
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>commons-codec</groupId>
                <artifactId>commons-codec</artifactId>
                <version>1.10</version>
            </dependency>

            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-core</artifactId>
                <version>5.8.5</version>
            </dependency>

            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-fsi-proxy</artifactId>
                <version>4.0.0-SNAPSHOT</version>
            </dependency>

            <!-- 消息服务 -->
            <dependency>
                <groupId>com.facishare.open</groupId>
                <artifactId>fs-open-msg-api</artifactId>
                <version>0.0.18-SNAPSHOT</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.facishare.open</groupId>
                        <artifactId>fs-open-common-storage</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.facishare</groupId>
                        <artifactId>fs-qixin-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.github.colin-lee</groupId>
                        <artifactId>mongo-spring-support</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>ch.qos.logback</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>commons-lang</groupId>
                        <artifactId>commons-lang</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.github.colin-lee</groupId>
                        <artifactId>rpc-trace</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- 引入elastic-job -->
            <dependency>
                <groupId>com.dangdang</groupId>
                <artifactId>elastic-job-lite-core</artifactId>
                <version>2.1.5</version>
            </dependency>

            <dependency>
                <groupId>com.dangdang</groupId>
                <artifactId>elastic-job-lite-spring</artifactId>
                <version>2.1.5</version>
            </dependency>

            <dependency>
                <groupId>com.fxiaoke</groupId>
                <artifactId>logconfig-core</artifactId>
                <version>3.0.0-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.github.colin-lee</groupId>
                <artifactId>mongo-spring-support</artifactId>
                <version>${mongo-spring-support.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-fileupload</groupId>
                <artifactId>commons-fileupload</artifactId>
                <version>1.3.1</version>
            </dependency>
            <dependency>
                <groupId>com.facishare.open</groupId>
                <artifactId>fs-open-common-result</artifactId>
                <version>0.0.6</version>
            </dependency>
            <dependency>
                <groupId>com.facishare.open</groupId>
                <artifactId>fs-open-common-storage</artifactId>
                <version>0.0.1</version>
            </dependency>
            <dependency>
                <groupId>com.facishare.open</groupId>
                <artifactId>fs-open-common-utils</artifactId>
                <version>0.0.2</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>log4j-over-slf4j</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- Add okhttp-bom -->
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp-bom</artifactId>
                <version>4.9.3</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!--            junit5-->
            <dependency>
                <groupId>org.junit</groupId>
                <artifactId>junit-bom</artifactId>
                <version>5.8.1</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!-- Mockito -->
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-core</artifactId>
                <version>3.12.4</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-inline</artifactId>
                <version>3.12.4</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-junit-jupiter</artifactId>
                <version>3.12.4</version>
                <scope>test</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <modules>
        <module>outer-oa-connector-common-api</module>
        <module>outer-oa-connector-base</module>
        <!--        <module>feishu-sync-provider</module>-->
        <module>fs-feishu-web</module>
        <!--        <module>fs-order-contacts-proxy-api</module>-->
        <module>fs-order-contacts-proxy</module>
        <module>aliyun-market-provider</module>
        <module>outer-oa-connector-i18n</module>

        <!--钉钉三方应用模块 begin-->
        <!--        <module>fs-dingtalk-common</module>-->
        <!--        <module>fs-dingtalk-api</module>-->
        <module>fs-dingtalk-web</module>
        <!--        <module>fs-dingtalk-provider</module>-->
        <!--        <module>fs-dingtalk-cloud</module>-->
        <!--钉钉三方应用模块 end-->
        <module>fs-huawei-kit-web</module>
        <module>fs-qywx-web</module>
        <module>fs-dingtalk-web-self-build-app</module>
        <module>oa-base-db-proxy</module>
        <module>outer-oa-connector-web</module>
    </modules>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <java.version>1.8</java.version>
        <spring-framework.version>4.3.21.RELEASE</spring-framework.version>
        <open-common.version>0.0.1</open-common.version>
        <open-common-result.version>0.0.3</open-common-result.version>
        <com.fxiaoke.syncdata.version>1.0.0-SNAPSHOT</com.fxiaoke.syncdata.version>
        <mongo-java-driver.version>3.10.1</mongo-java-driver.version>
        <fs-fsi-proxy.version>3.0.0-SNAPSHOT</fs-fsi-proxy.version>
        <hutool.version>5.8.5</hutool.version>
        <powermock.version>2.0.2</powermock.version>
        <!--以下两项覆盖父POM版本-->
        <dispatcher-support.version>6.0.1-SNAPSHOT</dispatcher-support.version>
    </properties>

    <repositories>
        <repository>
            <id>release</id>
            <name>Release Repository</name>
            <url>http://maven.firstshare.cn/artifactory/libs-release</url>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>snapshot</id>
            <name>Snapshot Repository</name>
            <url>http://maven.firstshare.cn/artifactory/libs-snapshot</url>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </snapshots>
        </repository>
    </repositories>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <argLine>${argLine}</argLine>
                    <properties>
                        <property>
                            <name>surefire.timeout</name>
                            <value>90</value>
                        </property>
                    </properties>
                    <systemPropertyVariables>
                        <config.mode>localNoUpdate</config.mode> <!-- 指定系统属性 -->
                    </systemPropertyVariables>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
