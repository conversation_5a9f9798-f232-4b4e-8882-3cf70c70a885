package com.facishare.open.feishu.web.controller.outer.oaconnector;

import com.alibaba.fastjson.JSON;
import com.facishare.open.feishu.syncapi.arg.*;
import com.facishare.open.feishu.syncapi.enums.CustomFunctionTypeEnum;
import com.facishare.open.feishu.syncapi.info.EmployeeBindInfo;
import com.facishare.open.outer.oa.connector.common.api.info.FsServiceAuthInfo;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.result.ResultCodeEnum;
import com.facishare.open.feishu.syncapi.service.EmployeeBindService;
import com.facishare.open.feishu.syncapi.service.ExternalCalendarService;
import com.facishare.open.feishu.syncapi.service.FeishuAppService;
import com.facishare.open.feishu.syncapi.service.ServiceAuthService;
import com.facishare.open.feishu.web.utils.MD5Helper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.security.NoSuchAlgorithmException;

/**
 * <AUTHOR>
 * @Date: 20240221
 * @Desc:
 */
@Slf4j
@RestController
@RequestMapping("oaconnector/customfunction/common")
// IgnoreI18nFile
public class CustomFunctionCommonController {
    @Resource
    private FeishuAppService feishuAppService;
    @Resource
    private EmployeeBindService employeeBindService;
    @Resource
    private ServiceAuthService serviceAuthService;
    @Resource
    private ExternalCalendarService externalCalendarService;

    @RequestMapping(value = "/execute", method = RequestMethod.POST)
    @ResponseBody
    public Result<String> customFunctionExecuteLogic(HttpServletRequest request,
            @RequestBody CustomFunctionCommonArg arg) {
        // 鉴权
        // 获取业务key和secret
        String fsService = request.getHeader("fs-service");
        String fsKey = request.getHeader("fs-key");
        String fsSecret = request.getHeader("fs-secret");
        if (StringUtils.isAnyEmpty(fsService, fsKey, fsSecret)) {
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL.getCode(), "当前业务没有权限访问当前URL"); // ignorei18n
        }

        // 加密后校验
        String fsServiceEncryption;
        String fsKeyEncryption;
        String fsSecretEncryption;
        try {
            fsServiceEncryption = MD5Helper.getStringMD5(fsService);
            fsKeyEncryption = MD5Helper.getStringMD5(fsKey);
            fsSecretEncryption = MD5Helper.getStringMD5(fsSecret);
        } catch (NoSuchAlgorithmException e) {
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL.getCode(), "权限校验失败"); // ignorei18n
        }
        com.facishare.open.feishu.syncapi.result.Result<FsServiceAuthInfo> serviceAuthResult = serviceAuthService
                .getServiceAuth(fsServiceEncryption);
        if (!serviceAuthResult.isSuccess() || ObjectUtils.isEmpty(serviceAuthResult.getData())) {
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL.getCode(), "当前业务没有权限访问当前URL，请输入正确的服务名"); // ignorei18n
        }

        if (serviceAuthResult.getData().getStatus() != 0) {
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL.getCode(), "当前业务已被停用"); // ignorei18n
        }

        if (!(fsKeyEncryption.equals(serviceAuthResult.getData().getFsKey())
                && fsSecretEncryption.equals(serviceAuthResult.getData().getFsSecret()))) {
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL.getCode(), "当前业务没有权限访问当前URL，请输入正确的业务key和secret"); // ignorei18n
        }

        String channel = request.getHeader("channel");
        if (ObjectUtils.isEmpty(arg) || StringUtils.isAnyEmpty(channel, arg.getParams(), arg.getType())) {
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }
        if (CustomFunctionTypeEnum.auth_tenant_access_token.name().equals(arg.getType())) {
            // 授权
            QueryTenantAccessTokenArg tenantAccessTokenArg = JSON.parseObject(arg.getParams(),
                    QueryTenantAccessTokenArg.class);
            if (StringUtils.isAnyEmpty(tenantAccessTokenArg.getAppId(), tenantAccessTokenArg.getOutEa())) {
                return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
            }
            return feishuAppService.getTenantAccessToken(channel, tenantAccessTokenArg);
        } else if (CustomFunctionTypeEnum.query_employee_bind.name().equals(arg.getType())) {
            // 查询人员
            QueryEmployeeBindArg queryEmployeeBindArg = JSON.parseObject(arg.getParams(), QueryEmployeeBindArg.class);
            if (StringUtils.isAnyEmpty(queryEmployeeBindArg.getFsEa(), queryEmployeeBindArg.getOutEa())) {
                return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
            }
            if (StringUtils.isAllEmpty(queryEmployeeBindArg.getOutUserId(), queryEmployeeBindArg.getFsUserId())) {
                return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
            }
            Result<EmployeeBindInfo> employeeBindInfoResult = employeeBindService.queryEmployeeBind(channel,
                    queryEmployeeBindArg);
            if (!employeeBindInfoResult.isSuccess() || ObjectUtils.isEmpty(employeeBindInfoResult.getData())) {
                return Result.newError(employeeBindInfoResult.getCode(), employeeBindInfoResult.getMsg());
            }

            return Result.newSuccess(JSON.toJSONString(employeeBindInfoResult.getData()));
        } else if (CustomFunctionTypeEnum.update_employee_bind.name().equals(arg.getType())) {
            // 更新人员
            UpdateEmployeeBindArg updateEmployeeBindArg = JSON.parseObject(arg.getParams(),
                    UpdateEmployeeBindArg.class);
            if (StringUtils.isAnyEmpty(updateEmployeeBindArg.getFsEa(), updateEmployeeBindArg.getOutEa(),
                    updateEmployeeBindArg.getOutUserId(), updateEmployeeBindArg.getFsUserId())) {
                return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
            }
            Result<Integer> updateEmployeeBindResult = employeeBindService.updateEmployeeBind(channel,
                    updateEmployeeBindArg);
            if (!updateEmployeeBindResult.isSuccess() || ObjectUtils.isEmpty(updateEmployeeBindResult.getData())) {
                return Result.newError(updateEmployeeBindResult.getCode(), updateEmployeeBindResult.getMsg());
            }

            return Result.newSuccess(String.valueOf(updateEmployeeBindResult.getData()));
        } else if (CustomFunctionTypeEnum.insert_employee_bind.name().equals(arg.getType())) {
            // 插入人员
            InsertEmployeeBindArg insertEmployeeBindArg = JSON.parseObject(arg.getParams(),
                    InsertEmployeeBindArg.class);
            if (StringUtils.isAnyEmpty(insertEmployeeBindArg.getFsEa(), insertEmployeeBindArg.getOutEa(),
                    insertEmployeeBindArg.getOutUserId(), insertEmployeeBindArg.getFsUserId())) {
                return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
            }
            Result<Integer> insertEmployeeBindResult = employeeBindService.insertEmployeeBind(channel,
                    insertEmployeeBindArg);
            if (!insertEmployeeBindResult.isSuccess() || ObjectUtils.isEmpty(insertEmployeeBindResult.getData())) {
                return Result.newError(insertEmployeeBindResult.getCode(), insertEmployeeBindResult.getMsg());
            }

            return Result.newSuccess(String.valueOf(insertEmployeeBindResult.getData()));
        } else if (CustomFunctionTypeEnum.query_calendar_bind.name().equals(arg.getType())) {
            // 查询日历id
            queryCalendarBindArg queryCalendarBindArg = JSON.parseObject(arg.getParams(), queryCalendarBindArg.class);
            if (ObjectUtils.isEmpty(queryCalendarBindArg) || StringUtils.isEmpty(queryCalendarBindArg.getFsEa())) {
                return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
            }
            return externalCalendarService.queryExternalCalendarId(queryCalendarBindArg.getFsEa());
        } else if (CustomFunctionTypeEnum.query_calendar_event_bind.name().equals(arg.getType())) {
            // 查询日程id
            queryCalendarEventBindArg queryCalendarEventBindArg = JSON.parseObject(arg.getParams(),
                    queryCalendarEventBindArg.class);
            if (ObjectUtils.isEmpty(queryCalendarEventBindArg) || StringUtils
                    .isAnyEmpty(queryCalendarEventBindArg.getFsEa(), queryCalendarEventBindArg.getObjectId())) {
                return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
            }
            return externalCalendarService.queryExternalCalendarEventId(queryCalendarEventBindArg.getFsEa(),
                    queryCalendarEventBindArg.getObjectId());
        } else {
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }
    }
}
