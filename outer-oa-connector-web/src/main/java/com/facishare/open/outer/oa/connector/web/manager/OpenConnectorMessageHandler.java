package com.facishare.open.outer.oa.connector.web.manager;

import com.facishare.converter.EIEAConverter;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEnterpriseBindManager;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.facishare.open.outer.oa.connector.web.msg.template.*;
import com.facishare.open.outer.oa.connector.web.openconnetor.model.*;
import com.fxiaoke.message.extrnal.platform.model.arg.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Component
public class OpenConnectorMessageHandler {
    @Resource
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;
    @Resource
    private OpenConnectorSendTextMsgTemplate openConnectorSendTextMsgTemplate;
    @Resource
    private OpenConnectorSendTextCarMsgTemplate openConnectorSendTextCarMsgTemplate;
    @Resource
    private OpenConnectorCreateTodoTemplate openConnectorCreateTodoTemplate;
    @Resource
    private OpenConnectorDealTodoTemplate openConnectorDealTodoTemplate;
    @Resource
    private OpenConnectorDeleteTodoTemplate openConnectorDeleteTodoTemplate;
    @Resource
    private EIEAConverter eieaConverter;

    //文本消息
    public void dealSendTextMessageHandler(SendTextMessageArg sendTextMessageArg) {
        String fsEa = sendTextMessageArg.getEa();
        // 获取企业微信企业ID
        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntitiesByFsEa(ChannelEnum.standard_oa, fsEa);
        if (CollectionUtils.isEmpty(enterpriseBindEntities)) {
            return;
        }
        Integer tenantId=eieaConverter.enterpriseAccountToId(fsEa);

        for (OuterOaEnterpriseBindEntity enterpriseBindEntity : enterpriseBindEntities) {
            try {
                OuterSendTextMessageArg sendTextMessagePushArg=new OuterSendTextMessageArg();
                sendTextMessagePushArg.setCrmArg(sendTextMessageArg);
                sendTextMessagePushArg.setOuterOaEnterpriseBindEntity(enterpriseBindEntity);
                sendTextMessagePushArg.setTenantId(tenantId.toString());
                sendTextMessagePushArg.setDcId(enterpriseBindEntity.getId());
                sendTextMessagePushArg.setApiName(enterpriseBindEntity.getConnectParams().getStandard_oa().getApiName());
                // 发送文本消息
                openConnectorSendTextMsgTemplate.execute(sendTextMessagePushArg);
            } catch (Exception e) {
                log.info("OpenConnectorMessageHandler.dealSendTextMessageHandler.error={}", e.getMessage());
            }
        }
    }

    //卡片消息
    public void dealSendTextCardMessageHandler(SendTextCardMessageArg sendTextCardMessageArg) {
        String fsEa = sendTextCardMessageArg.getEa();
        // 获取企业微信企业ID
        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntitiesByFsEa(ChannelEnum.standard_oa, fsEa);
        if (CollectionUtils.isEmpty(enterpriseBindEntities)) {
            return;
        }
        Integer tenantId=eieaConverter.enterpriseAccountToId(fsEa);
        for (OuterOaEnterpriseBindEntity enterpriseBindEntity : enterpriseBindEntities) {
            try {
                OuterSendTextCardMessageArg sendTextCardMessagePushArg = new OuterSendTextCardMessageArg();
                sendTextCardMessagePushArg.setCrmArg(sendTextCardMessageArg);
                sendTextCardMessagePushArg.setOuterOaEnterpriseBindEntity(enterpriseBindEntity);
                sendTextCardMessagePushArg.setTenantId(tenantId.toString());
                sendTextCardMessagePushArg.setDcId(enterpriseBindEntity.getId());
                sendTextCardMessagePushArg.setApiName(enterpriseBindEntity.getConnectParams().getStandard_oa().getApiName());
                // 发送卡片消息
                openConnectorSendTextCarMsgTemplate.execute(sendTextCardMessagePushArg);
            } catch (Exception e) {
                log.info("OpenConnectorMessageHandler.dealSendTextCardMessageHandler.error={}", e.getMessage());
            }
        }
    }

    // 创建待办
    public void dealCreateTodoHandler(CreateTodoArg createTodoArg) {
        String fsEa = createTodoArg.getEa();
        // 获取企业微信企业ID
        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntitiesByFsEa(ChannelEnum.standard_oa, fsEa);
        if (CollectionUtils.isEmpty(enterpriseBindEntities)) {
            return;
        }
        Integer tenantId=eieaConverter.enterpriseAccountToId(fsEa);
        for (OuterOaEnterpriseBindEntity enterpriseBindEntity : enterpriseBindEntities) {
            try {
                OuterCreateTodoArg createTodoPushArg = new OuterCreateTodoArg();
                createTodoPushArg.setCrmArg(createTodoArg);
                createTodoPushArg.setOuterOaEnterpriseBindEntity(enterpriseBindEntity);
                createTodoPushArg.setTenantId(tenantId.toString());
                createTodoPushArg.setDcId(enterpriseBindEntity.getId());
                createTodoPushArg.setApiName(enterpriseBindEntity.getConnectParams().getStandard_oa().getApiName());
                openConnectorCreateTodoTemplate.execute(createTodoPushArg);
            } catch (Exception e) {
                log.info("OpenConnectorMessageHandler.dealCreateTodoHandler.error={}", e.getMessage());
            }
        }
    }

    // 处理待办
    public void dealDealTodoHandler(DealTodoArg dealTodoArg) {
        String fsEa = dealTodoArg.getEa();
        // 获取企业微信企业ID
        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntitiesByFsEa(ChannelEnum.standard_oa, fsEa);
        if (CollectionUtils.isEmpty(enterpriseBindEntities)) {
            return;
        }
        Integer tenantId=eieaConverter.enterpriseAccountToId(fsEa);
        for (OuterOaEnterpriseBindEntity enterpriseBindEntity : enterpriseBindEntities) {
            try {
                OuterDealTodoArg dealTodoPushArg = new OuterDealTodoArg();
                dealTodoPushArg.setCrmArg(dealTodoArg);
                dealTodoPushArg.setOuterOaEnterpriseBindEntity(enterpriseBindEntity);
                dealTodoPushArg.setTenantId(tenantId.toString());
                dealTodoPushArg.setDcId(enterpriseBindEntity.getId());
                dealTodoPushArg.setApiName(enterpriseBindEntity.getConnectParams().getStandard_oa().getApiName());
                openConnectorDealTodoTemplate.execute(dealTodoPushArg);
            } catch (Exception e) {
                log.info("OpenConnectorMessageHandler.dealTodoHandler.error={}", e.getMessage());
            }
        }
    }

    // 删除待办
    public void dealDeleteTodoHandler(DeleteTodoArg deleteTodoArg) {
        String fsEa = deleteTodoArg.getEa();
        // 获取企业微信企业ID
        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntitiesByFsEa(ChannelEnum.standard_oa, fsEa);
        if (CollectionUtils.isEmpty(enterpriseBindEntities)) {
            return;
        }
        Integer tenantId=eieaConverter.enterpriseAccountToId(fsEa);
        for (OuterOaEnterpriseBindEntity enterpriseBindEntity : enterpriseBindEntities) {
            try {
                OuterDeleteTodoArg deleteTodoPushArg = new OuterDeleteTodoArg();
                deleteTodoPushArg.setCrmArg(deleteTodoArg);
                deleteTodoPushArg.setOuterOaEnterpriseBindEntity(enterpriseBindEntity);
                deleteTodoPushArg.setTenantId(tenantId.toString());
                deleteTodoPushArg.setDcId(enterpriseBindEntity.getId());
                deleteTodoPushArg.setApiName(enterpriseBindEntity.getConnectParams().getStandard_oa().getApiName());
                openConnectorDeleteTodoTemplate.execute(deleteTodoPushArg);
            } catch (Exception e) {
                log.info("OpenConnectorMessageHandler.deleteTodoHandler.error={}", e.getMessage());
            }
        }
    }
}
