package com.facishare.open.huawei.kit.web.service

import com.facishare.open.huawei.kit.web.handler.ApplicationSyncKitEventHandler
import com.facishare.open.huawei.kit.web.handler.AuthSyncKitEventHandler
import com.facishare.open.huawei.kit.web.handler.TenantSyncKitEventHandler
import com.facishare.open.huawei.kit.web.info.AuthSyncInfo
import com.facishare.open.huawei.kit.web.info.HuaweiOrderInfo
import com.facishare.open.huawei.kit.web.info.SingleOrgSyncInfo
import com.facishare.open.huawei.kit.web.result.result.Result
import com.facishare.open.huawei.kit.web.service.HuaweiContactsService
import com.facishare.open.huawei.kit.web.templateData.KitVerifyTemplateData
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification

import javax.annotation.Resource

@ContextConfiguration(locations = "classpath:spring-test/fsHuaweiKitWebAppContext-test.xml")
class HuaweiContactsServiceTest extends Specification {
    @Resource
    private HuaweiContactsService huaweiContactsService;
    @Resource
    private AuthSyncKitEventHandler authSyncKitEventHandler;
    @Resource
    private TenantSyncKitEventHandler tenantSyncKitEventHandler;
    @Resource
    private ApplicationSyncKitEventHandler applicationSyncKitEventHandler;

    def "syncEmployee"() {
        expect:
        // 创建 User 对象
        AuthSyncInfo.User user = new AuthSyncInfo.User();
        user.setUserName("459bbbec25d24749b2eb83be37b602a82");
        user.setName("张三");
        user.setOrgCode("123456789");
        user.setPosition("系统管理员");
        user.setRole("admin");
        user.setEnable("true");
        user.setMobile("+86-14489887714")

        // 创建 AuthSyncInfo 对象并填充信息
        AuthSyncInfo authSyncInfo = new AuthSyncInfo();
        authSyncInfo.setInstanceId("huaiweitest123456");
        authSyncInfo.setTenantId("68cbc86abc2018ab880d92f36422fa0e1");
        authSyncInfo.setAppId("ef2d3967707c4eec83c763e76ba8febc");
        authSyncInfo.setUserList(Arrays.asList(user));
        authSyncInfo.setFlag(1);
        authSyncInfo.setTestFlag(1);
        authSyncInfo.setTimeStamp("20240927102513819");

        Result<HuaweiOrderInfo> a = huaweiContactsService.syncEmployee(authSyncInfo)



//        def a = huaweiOrderService.saveOrder(huaweiOrderDataModel)
        print(a)
    }

    //生成一个同步部门的测试和用例 syncDepartment
    def "syncDepartment"() {
        expect:
        SingleOrgSyncInfo singleOrgSyncInfo = new SingleOrgSyncInfo();
        singleOrgSyncInfo.setOrgCode("1000")
        singleOrgSyncInfo.setOrgName("测试部门");
        singleOrgSyncInfo.setInstanceId("a4c9e35a-cc51-43ea-8833-643099a728d4");
        singleOrgSyncInfo.setTenantId("c31eff7367d5405eaf2d5c7d13ee2514");
        singleOrgSyncInfo.setFlag(1);
        singleOrgSyncInfo.setTestFlag(1);
        singleOrgSyncInfo.setTimeStamp("20240927102513819");
        def department = huaweiContactsService.syncDepartment(singleOrgSyncInfo)
        println department
    }

    def "createEmployee"() {
        expect:
        // 创建 User 对象
        AuthSyncInfo.User user = new AuthSyncInfo.User();
        user.setUserName("user123456");
        user.setName("张三");
        user.setOrgCode("123456789");
        user.setPosition("系统管理员");
        user.setRole("admin");
        user.setEnable("true");
        user.setMobile("10000000000")

        Result<HuaweiOrderInfo> a = huaweiContactsService.createEmployee("68cbc86abc2018ab880d92f36422fa0e1", user)
        print(a)
    }

    def "authSyncKitEventHandler"() {
        expect:
        KitVerifyTemplateData kitVerifyTemplateData = new KitVerifyTemplateData();
        Map<String, Object> isvProduceReq = new Gson().fromJson("{\"appId\":\"a459e1ebca06447e96687236f9f20af4\",\"flag\":1,\"instanceId\":\"a4c9e35a-cc51-43ea-8833-643099a728d4\",\"tenantId\":\"c31eff7367d5405eaf2d5c7d13ee2514\",\"testFlag\":0,\"timeStamp\":\"20241218143021561\",\"userList\":[{\"userName\":\"test\",\"name\":\"test1\",\"position\":\"\",\"orgCode\":\"00000001\",\"role\":\"admin\",\"enable\":\"true\",\"email\":\"<EMAIL>\",\"extension\":{\"entryDate\":\"\",\"workPlace\":\"\"}}]}", new TypeToken<Map<String, Object>>(){}.getType())
        kitVerifyTemplateData.setIsvProduceReq(isvProduceReq)
        Result<HuaweiOrderInfo> a = authSyncKitEventHandler.handle(kitVerifyTemplateData)
        print(a)
    }

    def "tenantSyncKitEventHandler"() {
        expect:
        KitVerifyTemplateData kitVerifyTemplateData = new KitVerifyTemplateData();
        Map<String, Object> isvProduceReq = new Gson().fromJson("{\"appId\":\"a459e1ebca06447e96687236f9f20af4\",\"instanceId\":\"a4c9e35a-cc51-43ea-8833-643099a728d4\",\"tenantId\":\"c31eff7367d5405eaf2d5c7d13ee2514\",\"domainName\":\"https://apaas-adumixzi.huaweiapaas.com\",\"name\":\"大风车\"}", new TypeToken<Map<String, Object>>(){}.getType())
        kitVerifyTemplateData.setIsvProduceReq(isvProduceReq)
        Result<HuaweiOrderInfo> a = tenantSyncKitEventHandler.handle(kitVerifyTemplateData)
        print(a)
    }

    def "applicationSyncKitEventHandler"() {
        expect:
        KitVerifyTemplateData kitVerifyTemplateData = new KitVerifyTemplateData();
        Map<String, Object> isvProduceReq = new Gson().fromJson("{\"appId\":\"a459e1ebca06447e96687236f9f20af4\",\"instanceId\":\"a4c9e35a-cc51-43ea-8833-643099a728d4\",\"tenantId\":\"c31eff7367d5405eaf2d5c7d13ee2514\",\"clientId\":\"BkQS64Mz9BflBZDiLGe2hgKGpe2fICk6\"}", new TypeToken<Map<String, Object>>(){}.getType())
        kitVerifyTemplateData.setIsvProduceReq(isvProduceReq)
        Result<HuaweiOrderInfo> a = applicationSyncKitEventHandler.handle(kitVerifyTemplateData)
        print(a)
    }
}
