package com.facishare.open.ding.outerInterface.impl;

import com.alibaba.fastjson.JSON;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.outer.oa.connector.common.api.result.OuterOAConnectSettingResult;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * <AUTHOR> (^_−)☆
 */
@ExtendWith(MockitoExtension.class)
class DingTalkSelfBuildOuterAbstractOASettingManagerTest {

    @InjectMocks
    private DingTalkSelfBuildOuterAbstractOASettingManager dingTalkSelfBuildOuterAbstractOASettingManager;

    @Test
    void checkIpValid_ConnectParamsIsNull() {
        boolean b = dingTalkSelfBuildOuterAbstractOASettingManager.checkIpValid(new OuterOaEnterpriseBindEntity(), "http://************:25775/dingtalkinner/");
        System.out.println(b);
        assertTrue(b);
    }

    @Test
    void checkIpValid_InnerIp() {
        // 创建测试需要的对象
        OuterOAConnectSettingResult settingResult = new OuterOAConnectSettingResult();

        // 构造DingConnectorVo对象
        com.facishare.open.outer.oa.connector.common.api.admin.DingConnectorVo connectorVo = new com.facishare.open.outer.oa.connector.common.api.admin.DingConnectorVo();
        connectorVo.setDataCenterId("testDcId");
        connectorVo.setClientIp("127.0.0.1");
        OuterOAConnectSettingResult.ConnectParams connectParams = new OuterOAConnectSettingResult.ConnectParams();
        connectParams.setDingding(connectorVo);
        // 设置settingResult
        settingResult.setConnectParams(connectParams);
        // 模拟依赖项的行为
        OuterOaEnterpriseBindEntity oldEntity = new OuterOaEnterpriseBindEntity();
        oldEntity.setConnectInfo(JSON.toJSONString(connectorVo));
        boolean b = dingTalkSelfBuildOuterAbstractOASettingManager.checkIpValid(oldEntity, "http://************:25775/dingtalkinner/");
        assertFalse(b);
    }
}