package com.facishare.open.qywx.web.utils;

import com.google.common.base.Joiner;
import lombok.extern.slf4j.Slf4j;
import okhttp3.FormBody;
import okhttp3.MediaType;
import okhttp3.Request;
import okhttp3.Request.Builder;
import okhttp3.RequestBody;
import org.apache.commons.collections4.MapUtils;

import java.util.Map;
import java.util.Map.Entry;

/**
 * <p>类的详细说明</p>
 * @dateTime 2018/7/13 15:09
 * @<NAME_EMAIL>
 * @version 1.0 
 */
@Slf4j
public class UrlUtils {

    /**
     * 构造Get Request
     * @param params
     * @param headers
     * @param url
     * @return
     */
    public static Request buildGetRequest(Map<String, Object> params, Map<String, String> headers, String url) {
        Request build = null;
        try {
            if (MapUtils.isNotEmpty(params)) {
                String path = Joiner.on("&").withKeyValueSeparator("=").join(params);
                url = Joiner.on("?").join(url, path);
            }

            Builder get = new Builder();
            buildHeader(get, headers);

            log.info("UrlUtil.buildGetRequest params={}, headers={}, url={}", params, headers, url);
            return get.url(url).build();
        } catch (Exception e) {
            log.error("UrlUtil.buildGetRequest error, params={}, headers={}, url={}", params, headers, url, e);
        }
        return build;
    }

    /**
     * 构造Form Post Request
     * @param params
     * @param headers
     * @param url
     * @return
     */
    public static Request buildFormPostRequest(Map<String, Object> params, Map<String, String> headers, String url) {
        Request build = null;
        try {
            FormBody.Builder builder = new FormBody.Builder();
            buildBuilder(builder, params);
            RequestBody body = builder.build();

            Builder post = new Builder().post(body);
            buildHeader(post, headers);
            build = post.url(url).build();

            log.info("UrlUtil.buildFormPostRequest params={}, headers={}, url={}", params, headers, url);
            return build;
        } catch (Exception e) {
            log.error("UrlUtil.buildFormPostRequest error, params={}, headers={}, url={}", params, headers, url, e);
        }
        return build;
    }

    /**
     * 构造json格式的Post Request
     * @param json
     * @param headers
     * @param url
     * @return
     */
    public static Request buildJsonPostRequest(String json, Map<String, String> headers, String url) {
        Request build = null;
        try {
            MediaType mediaType = MediaType.parse("application/json");
            RequestBody body = RequestBody.create(mediaType, json);

            Builder post = new Builder().post(body);
            buildHeader(post, headers);
            build = post.url(url).build();

            log.info("UrlUtil.buildJsonPostRequest json={}, headers={}, url={}", json, headers, url);
            return build;
        } catch (Exception e) {
            log.error("UrlUtil.buildJsonPostRequest error, json={}, headers={}, url={}", json, headers, url, e);
        }
        return build;
    }

    /**
     * 构造json格式的Put Request
     * @param json
     * @param headers
     * @param url
     * @return
     */
    public static Request buildJsonPutRequest(String json, Map<String, String> headers, String url) {
        Request build = null;
        try {
            MediaType mediaType = MediaType.parse("application/json");
            RequestBody requestBody = RequestBody.create(mediaType, json);

            Builder put = new Builder().put(requestBody);
            buildHeader(put, headers);
            build = put.url(url).build();

            log.info("UrlUtil.buildJsonPutRequest json={}, headers={}, url={}", json, headers, url);
            return build;
        } catch (Exception e) {
            log.error("UrlUtil.buildJsonPutRequest error, json={}, headers={}, url={}", json, headers, url, e);
        }
        return build;
    }

    /**
     * 构造Delete Request
     * @param headers
     * @param url
     * @return
     */
    public static Request buildDeleteRequest(Map<String, String> headers, String url) {
        Request build = null;
        try {
            Builder delete = new Builder().delete();
            buildHeader(delete, headers);
            build = delete.url(url).build();

            log.info("UrlUtil.buildJsonPutRequest headers={}, url={}", headers, url);
            return build;
        } catch (Exception e) {
            log.error("UrlUtil.buildDeleteRequest error, headers={}, url={}", headers, url, e);
        }
        return build;
    }

    /**
     * 构造Header
     * @param builder
     * @param headers
     */
    private static void buildHeader(Builder builder, Map<String, String> headers) {
        if (MapUtils.isNotEmpty(headers)) {
            for (Entry<String, String> entry : headers.entrySet()) {
                String key = entry.getKey();
                Object val = entry.getValue();
                builder.addHeader(key, val.toString());
            }
        }
    }

    /**
     * 构造Builder
     * @param builder
     * @param params
     */
    private static void buildBuilder(FormBody.Builder builder, Map<String, Object> params) {
        if (MapUtils.isNotEmpty(params)) {
            for (Entry<String, Object> entry : params.entrySet()) {
                String key = entry.getKey();
                Object val = entry.getValue();
                builder.add(key, val.toString());
            }
        }
    }

}
