<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans.xsd">

    <!-- message save mongo-->
    <bean id="oaBaseMongoStore" name="oaBaseMongoStore" class="com.github.mongo.support.MongoDataStoreFactoryBean">
        <property name="configName" value="fs-erpdss-oa-base-config"/>
        <property name="sectionNames" value="oaBaseMongo"/>
    </bean>
</beans>