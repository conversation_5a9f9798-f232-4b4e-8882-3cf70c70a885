package service;

import base.BaseAbstractTest;
import com.alibaba.fastjson.JSONObject;
import com.facishare.open.ding.api.result.BindFxEaResult;
import com.facishare.open.ding.api.result.BindFxUserResult;
import com.facishare.open.ding.api.result.PollingSyncResult;
import com.facishare.open.ding.api.service.ObjectMappingService;
import com.facishare.open.ding.api.service.PollingSyncService;
import com.facishare.open.ding.api.service.UserAppMappingService;
import com.facishare.open.ding.api.service.cloud.CloudDeptService;
import com.facishare.open.ding.api.vo.DeptVo;
import com.facishare.open.ding.api.vo.PollingSyncDataVo;
import com.facishare.open.ding.common.model.Dept;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.provider.utils.HttpUtils;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaDepartmentBindEntity;
import com.fxiaoke.message.extrnal.platform.api.ExternalMessageService;
import com.fxiaoke.message.extrnal.platform.model.arg.SendTextCardMessageArg;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.util.EntityUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.IOException;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2019-09-04 15:47
 */
@Slf4j
public class SyncMessageServiceTest extends BaseAbstractTest {
    @Autowired
    private ExternalMessageService messageService;
    @Autowired
    private ObjectMappingService objectMappingService;
    @Autowired
    private PollingSyncService pollingSyncService;
    @Autowired
    private CloudDeptService cloudDeptService;
    @Autowired
    private UserAppMappingService appMappingService;


    @Test
    public void testEmp() {
        appMappingService.removeEmp("dingfeca3fa3352c7d4ca39a90f97fcb1e09", 70480L, "290746633523071");
    }

//    @Test
//    public void fixEmp() {
//        String result = "{\"Message\":\"\",\"Success\":true,\"FileId\":\"ea21ac2d5f8a43988ec90d5523a2672e\",\"FileName\":\"IMG_4479.jpg\",\"FileSize\":4810681,\"Url\":\"\"}";
//        Map<String, Object> upload2AttachmentResult = Maps.newHashMap();
//        upload2AttachmentResult = com.alibaba.fastjson.JSONObject.parseObject(String.valueOf(result), HashMap.class);
//        boolean success = false;
//        success = (Boolean) upload2AttachmentResult.get("Success");
//        boolean result1 = success == true;
//        appMappingService.fixEmp(1080);
//    }


    @Test
    public void testDept() {
        Dept dept = new Dept();
        dept.setDeptOwner("");
        dept.setName("部门3");
        dept.setParentid(492173125L);
        dept.setId(491866427L);
        cloudDeptService.createDept(dept, "dingf45bdda251bf4b4d4ac5d6980864d335", 82432, "", "");
    }


    @Test
    public void testSync() {
        Result<PollingSyncResult> pollingSyncResultResult = pollingSyncService.queryPolling(1);
        PollingSyncDataVo pollingSyncDataVo = PollingSyncDataVo.builder().lastSyncTime(new Date()).eventLevel(1).id(2L).build();
        Result<Integer> result = pollingSyncService.updateSync(pollingSyncDataVo);
        log.info("");
    }


    @Test
    public void testSyncMessage() {
        SendTextCardMessageArg arg = new SendTextCardMessageArg();
        arg.setEi(71658);
        arg.setReceiverIds(Lists.newArrayList(1003));
        arg.setMessageContent("测试123");
        arg.setTitle("待办测试");
        arg.setUrl("https://www.ceshi112.com/FHH/EM0HXUL/SSO/Login?token=%s");
        messageService.sendTextCardMessage(arg);
    }

    @Test
    public void testGetBindFxEmp() {
        Result<List<BindFxUserResult>> result = objectMappingService.getBindEiAndUser(0, 5, "");
    }

    @Test
    public void testGetBindFxEa() {
        Result<List<BindFxEaResult>> result = objectMappingService.getBindEa(0, 5, "");

    }

    @Test
    public void testOkhttp() {
        String url = "http://172.31.101.246:15056/versionRegisterService/crmOrderToDetail";
        Map<String, Object> bodyMap = Maps.newHashMap();
        bodyMap.put("enterpriseAccount", "dingkncsqy");
        bodyMap.put("enterpriseName", "柯南测试企业");
        bodyMap.put("managerMobile", "***********");
        bodyMap.put("managerName", "柯南颖");
        bodyMap.put("outEid", "ding0c8ee222b9a38265f2c783f7214b6d69");
        String body = JSONObject.toJSON(bodyMap).toString();
        CloseableHttpResponse closeableHttpResponse = HttpUtils.httpPost(url, body, createHeader());

        Object entity = null;
        try {
            entity = EntityUtils.toString(closeableHttpResponse.getEntity(), "UTF-8");
        } catch (IOException e) {
            e.printStackTrace();
        }
        log.info("entity:{}", entity);
    }

    public static Map<String, String> createHeader() {
        Map<String, String> headers = Maps.newHashMapWithExpectedSize(1);
        headers.put("Content-Type", "application/json");
        return headers;
    }

    @Test
    public void updateDeptBind() {
        OuterOaDepartmentBindEntity deptVo = new OuterOaDepartmentBindEntity();
        deptVo.setFsEa("12345");
        deptVo.setFsDepId("23");
        deptVo.setOutEa("21");
        deptVo.setAppId("1001");
        deptVo.setDcId("123");
        deptVo.setOutEa("112");
        cloudDeptService.updateDeptBind(Lists.newArrayList(deptVo));
    }

    @Test
    public void queryDepartments() {
        int startPageNum = 1;
        for (int i = 0; i < 10; i++) {
            Integer page = ((startPageNum++) - 1) * 10;
            Result<List<OuterOaDepartmentBindEntity>> listResult = cloudDeptService.queryBindingDepartments(82379, page, 10, "");
            System.out.println(listResult);
        }
    }
}
