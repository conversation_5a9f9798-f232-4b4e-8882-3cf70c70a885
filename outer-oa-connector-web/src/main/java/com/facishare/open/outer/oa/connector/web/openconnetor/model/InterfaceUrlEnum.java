package com.facishare.open.outer.oa.connector.web.openconnetor.model;


import lombok.Getter;


/**
 * <AUTHOR>
 * @Date: 13:36 2025/5/14
 * @Desc:
 */
@Getter
public enum InterfaceUrlEnum {

    getOaConnectorIntro("获取连接器介绍", "getOaConnectorIntro"),
    getConnectorAuthTypeList("获取连接器授权方式","getConnectorAuthTypeList"),
    getOAuth2AuthUrl("获取oauth授权地址","getOAuth2AuthUrl"),
    processUserInputSystemParams("处理系统参数","processUserInputSystemParams"),
    getDoAuthUrlMsg("无身份登录地址", "getDoAuthUrlMsg"),
    getLoginAuthUrlMsg("带身份登录", "getLoginAuthUrlMsg"),
    dealWithCallBackEvent("处理回调事件","dealWithCallBackEvent"),
    //目前查：部门、人员
    queryOaMasterById("单条查询","queryOaMasterById"),
    queryOaMasterBatch("批量查询","queryOaMasterBatch"),

    filterCreateTodoMsg("过滤创建代办消息", "filterCreateTodoMsg"),
    buildCreateTodoMsg("转换创建代办消息", "buildCreateTodoMsg"),
    sendCreateTodoMsg("创建代办", "sendCreateTodoMsg"),

    filterDealTodoMsg("过滤处理代办消息", "filterDealTodoMsg"),
    buildDealTodoMsg("转换处理代办消息", "buildDealTodoMsg"),
    sendDealTodoMsg("处理代办", "sendDealTodoMsg"),

    filterDeleteTodoMsg("过滤删除代办消息", "filterDeleteTodoMsg"),
    buildDeleteTodoMsg("转换删除代办消息", "buildDeleteTodoMsg"),
    sendDeleteTodoMsg("删除代办", "sendDeleteTodoMsg"),

    filterTextMsg("过滤文本消息", "filterTextMsg"),
    buildTextMsg("转换文本消息", "buildTextMsg"),
    sendTextMsg("发送文本消息", "sendTextMsg"),

    filterTextCardMsg("过滤卡片消息", "filterTextCardMsg"),
    buildTextCardMsg("转换卡片消息", "buildTextCardMsg"),
    sendTextCardMsg("发送卡片消息", "sendTextCardMsg"),


    ;
    /**
     * apl方法名称
     */
    private String desc;
    /**
     * apl方法名称
     */
    private String methodName;


    InterfaceUrlEnum(String desc, String methodName) {
        this.desc=desc;
        this.methodName=methodName;
    }
}
