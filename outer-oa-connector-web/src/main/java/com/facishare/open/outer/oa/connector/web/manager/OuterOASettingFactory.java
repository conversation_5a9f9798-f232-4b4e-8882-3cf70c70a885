package com.facishare.open.outer.oa.connector.web.manager;

import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.OuterOaAppInfoTypeEnum;
import com.facishare.open.outer.oa.connector.common.api.outerInterface.OuterAbstractSettingService;
import com.facishare.open.outer.oa.connector.common.api.outerInterface.factory.OuterServiceDubboRestFactoryBean;
import com.facishare.open.outer.oa.connector.common.api.outerInterface.impl.DefaultOuterOASetting;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.AbstractMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 外部OA设置工厂类 使用Spring管理渠道实现类的生命周期
 */
@Slf4j
@Component
public class OuterOASettingFactory {

    private Map<SettingKey, OuterAbstractSettingService> implementations;
    @Autowired
    private List<OuterServiceDubboRestFactoryBean<?>> settingServices;

    @Autowired
    @Qualifier("defaultOuterOASetting")
    private DefaultOuterOASetting defaultImplementation;
    @Autowired
    private OpenConnectorSettingManager openConnectorSettingManager;

    @SuppressWarnings("unchecked")
    @PostConstruct
    public void init() {
        // Spring启动时自动注册所有实现类
        implementations = settingServices
                .stream().<Map.Entry<SettingKey, OuterAbstractSettingService>>flatMap(service -> {
                    List<ChannelEnum> channels = service.getChannelEnums();
                    if (channels == null || channels.isEmpty()) {
                        return Stream.empty();
                    }
                    return channels.stream().flatMap(channel -> service.getTypeEnums().stream()
                            .map(type -> new AbstractMap.SimpleEntry<>(
                                    new SettingKey(channel, type),
                                    (OuterAbstractSettingService) service
                                            .getObject())));
                }).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue,
                        (existing, replacement) -> {
                            log.warn("Duplicate implementation found for key: {}, keeping existing implementation",
                                    existing);
                            return existing;
                        }));
    }

    /**
     * 获取渠道对应的实现类 如果找不到对应实现，返回默认实现
     *
     * @param channel                渠道类型
     * @param outerOaAppInfoTypeEnum 应用类型
     * @return 实现类实例
     */
    public OuterAbstractSettingService getImplementation(ChannelEnum channel,
                                                         OuterOaAppInfoTypeEnum outerOaAppInfoTypeEnum) {
        if(ChannelEnum.standard_oa.equals(channel)){
            return openConnectorSettingManager;
        }
        SettingKey key = new SettingKey(channel, outerOaAppInfoTypeEnum);
        return Optional.ofNullable(implementations.get(key)).orElse(defaultImplementation);
    }

    @Data
    private static class SettingKey {
        private final ChannelEnum channel;
        private final OuterOaAppInfoTypeEnum appType;

        public SettingKey(ChannelEnum channel, OuterOaAppInfoTypeEnum appType) {
            this.channel = channel;
            this.appType = appType;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o)
                return true;
            if (o == null || getClass() != o.getClass())
                return false;
            SettingKey that = (SettingKey) o;
            return channel == that.channel && appType == that.appType;
        }

        @Override
        public int hashCode() {
            return Objects.hash(channel, appType);
        }

        @Override
        public String toString() {
            return "SettingKey{channel=" + channel + ", appType=" + appType + '}';
        }
    }
}