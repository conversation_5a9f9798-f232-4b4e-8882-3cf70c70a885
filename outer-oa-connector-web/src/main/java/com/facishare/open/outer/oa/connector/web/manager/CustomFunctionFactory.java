package com.facishare.open.outer.oa.connector.web.manager;


import com.facishare.open.outer.oa.connector.web.service.CommonExecuteService;
import com.facishare.open.outer.oa.connector.web.service.impl.BaseCommonExecuteServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;


@Component
public class CustomFunctionFactory {
    @Autowired
    private Map<String, CommonExecuteService> customFunctionCommonServiceFactory;

    public CommonExecuteService getHandlerByType(String type) {
        CommonExecuteService customFunctionCommonService = customFunctionCommonServiceFactory.get(type);
        return customFunctionCommonService == null ? new BaseCommonExecuteServiceImpl() : customFunctionCommonService;
    }


}
