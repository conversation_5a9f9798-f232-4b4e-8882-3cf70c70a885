package com.facishare.open.ding.web.controller;
import com.alibaba.fastjson.JSONObject;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiCallBackRegisterCallBackRequest;
import com.dingtalk.api.response.*;
import com.facishare.open.ding.api.vo.DingtalkVo;
import com.facishare.open.ding.api.vo.ProxyRequestVo;
import com.facishare.open.ding.common.model.Dept;
import com.facishare.open.ding.common.model.User;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.common.result.ResultCode;
import com.facishare.open.ding.web.constants.Constant;
import com.facishare.open.ding.web.dingding.DingRequestUtil;
import com.facishare.open.ding.web.dingding.DingUrl;
import com.facishare.open.ding.web.handler.DingRetryHandler;
import com.facishare.open.ding.web.modle.HttpResponseMessage;
import com.facishare.open.ding.web.utils.OkHttp3MonitorUtils;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.taobao.api.ApiException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2019-09-10 17:34
 */
@Slf4j
@Controller
@RequestMapping("/proxy")
public class ProxyController {
    private String proxyVersion = "1.1";

    /**
     * 注册回调接口
     * @param vo
     * @return
     */
    @RequestMapping(value = "/proxyRequest", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public Object proxyRequest(@RequestBody(required = false) ProxyRequestVo vo,
                                       HttpServletRequest request) {
        Gson gson = new Gson();
        String argJson = gson.toJson(vo.getData());
        HttpResponseMessage response = null;
        switch (vo.getType()){
            case ("POST"):
                response = DingRetryHandler.sendOkHttp3Post(vo.getUrl(), new HashMap<>(), argJson);
                break;
            case ("GET"):
                response = DingRetryHandler.sendOkHttp3Get(vo.getUrl(), new HashMap<>(), new HashMap<>());
                break;
        }
        if (response == null) {
            log.error("请求处理失败，参数:type={},url:{},arg:{}.", vo.getType(), vo.getUrl(), argJson);
            return Result.newError(ResultCode.CLIENT_POST_FAILED);
        }
        Object entity = JSONObject.parseObject(response.getContent());
        log.info("ProxyController.proxyRequest,Invoke={}", entity);
        return entity.toString();
    }

    @RequestMapping(value = "/index")
    public String test(HttpServletRequest request) {
        return "index";
    }


    /**
     * 注册回调接口
     * @param vo
     * @return
     */
    @RequestMapping(value = "/registCallBack", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public Object registCallBack(@RequestBody(required = false) DingtalkVo vo, HttpServletRequest request) {


        DingTalkClient client = new DefaultDingTalkClient(DingUrl.REGISTER_URL);
        OapiCallBackRegisterCallBackRequest callRequest = new OapiCallBackRegisterCallBackRequest();
        callRequest.setAesKey(Constant.ENCODING_AES_KEY);
        callRequest.setToken(Constant.TOKEN);
        callRequest.setCallBackTag(Constant.CALL_BACK_TAG);
        callRequest.setUrl(vo.getCallBackUrl());  //回调接口url
        OapiCallBackRegisterCallBackResponse response = null;
        String key = vo.getAppKey() + vo.getAppSecret();
        try {
            response = client.execute(callRequest, DingRequestUtil.getProxyToken(vo.getAppKey(), vo.getAppSecret()).get(key));
        } catch (ApiException e) {
            log.warn("regist call back failed, e={}", e);
            return Result.newError(ResultCode.DING_REGIST_FAILED);
        }
        if(response.getErrcode()==DingRequestUtil.DING_CALLBACK_REGIST){
            log.warn("register call back url was exist,errorCode:{},errMsg:{}",response.getErrcode(),response.getErrmsg());
            return Result.newError(ResultCode.DING_CALL_BACK_URL_EXIST);
        }
        if (response.getErrcode() != DingRequestUtil.DING_SUCCESS){
            log.warn("regist call back failed, response={}.", response);
            return Result.newError(ResultCode.DING_REGIST_FAILED);
        }
        return Result.newSuccess();
    }

    /**
     * 获取token,用于验证appkey和appsecret
     * @param vo
     * @return
     */
    @RequestMapping(value = "/getToken", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public Result<String> getToken(@RequestBody(required = false) DingtalkVo vo, HttpServletRequest request) {
        String key = vo.getAppKey() + vo.getAppSecret();
        String token = DingRequestUtil.getProxyToken(vo.getAppKey(), vo.getAppSecret()).get(key);
        return Result.newSuccess(token);
    }

    /**
     * 查询部门列表
     * @param vo
     * @return
     */
    @RequestMapping(value = "/queryDetpList", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public String queryDetpList(@RequestBody(required = false) DingtalkVo vo, HttpServletRequest request) {

        List<OapiDepartmentListResponse.Department> deptResponse = DingRequestUtil.queryDeptList(vo.getAppKey(), vo.getAppSecret(),vo.getDeptId().toString());
        if (CollectionUtils.isEmpty(deptResponse)){
            log.warn("query dept list failed, DingtalkVo={}.", vo);
            return null;
        }
        List<Dept> depts = Lists.newArrayList();
        for(OapiDepartmentListResponse.Department department : deptResponse){
            Dept dept = new Dept();
            dept.setId(department.getId());
            dept.setParentid(department.getParentid());
            dept.setName(department.getName());
            depts.add(dept);
        }
        return new Gson().toJson(depts);
    }

    /**
     * 查询部门下所有员工
     * @param vo
     * @return
     */
    @RequestMapping(value = "/queryDetpUsers", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public String queryDetpUsers(@RequestBody(required = false) DingtalkVo vo, HttpServletRequest request) {
        List<OapiUserListResponse.Userlist> deptUsers = DingRequestUtil.queryDeptUser(vo.getDeptId(), vo.getAppKey(), vo.getAppSecret());
        if (CollectionUtils.isEmpty(deptUsers)){
            log.warn("query dept list failed, deptUsers={}.", deptUsers);
            return null;
        }
        List<User> users = Lists.newArrayList();
        for(OapiUserListResponse.Userlist dingUser : deptUsers){
            User user = new User();
            user.setUnionid(dingUser.getUnionid());
            user.setUserid(dingUser.getUserid());
            user.setName(dingUser.getName());
            user.setMobile(dingUser.getMobile());
            user.setEmail(StringUtils.isEmpty(dingUser.getEmail()) ? null : dingUser.getEmail());
            user.setJobnumber(StringUtils.isEmpty(dingUser.getJobnumber()) ? null : dingUser.getJobnumber());
            users.add(user);
        }
        return new Gson().toJson(users);
    }

    @RequestMapping(value = "/getUser", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public String getUser(@RequestBody(required = false) DingtalkVo vo, HttpServletRequest request) {

        OapiUserGetResponse userGetResponse = DingRequestUtil.getUser(vo.getAppKey(), vo.getAppSecret(), vo.getUserId());
        if (Objects.isNull(userGetResponse)){
            log.warn("get user failed, userGetResponse={}.", userGetResponse);
            return null;
        }
        User user = new User();
        user.setUnionid(userGetResponse.getUnionid());
        user.setUserid(userGetResponse.getUserid());
        user.setName(userGetResponse.getName());
        user.setMobile(userGetResponse.getMobile());
        user.setEmail(StringUtils.isEmpty(userGetResponse.getEmail()) ? null : userGetResponse.getEmail());
        user.setJobnumber(StringUtils.isEmpty(userGetResponse.getJobnumber()) ? null : userGetResponse.getJobnumber());
        return new Gson().toJson(user);
    }

    @RequestMapping(value = "/getUserByCode", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public String getUserByCode(@RequestBody(required = false) DingtalkVo vo, HttpServletRequest request) {

        OapiSnsGetuserinfoBycodeResponse userGetResponse = DingRequestUtil.getUserByCode(vo.getRedirectAppId(), vo.getRedirectAppSecret(), vo.getCode());
        Gson gson = new Gson();
        String str = gson.toJson(userGetResponse.getUserInfo());
        log.info("getUserByCode vo:{},str={} response:{}", vo,str, JSONObject.toJSONString(userGetResponse));
        return str;
    }

    /**
     * 代理调用
     * 返回钉钉的状态和内容
     * @param vo
     * @return
     */
    @RequestMapping(value = "/proxyHttpRequest", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public Result<String> proxyHttpRequest(@RequestBody(required = false) ProxyRequestVo vo,
                               HttpServletRequest request) {
        Gson gson = new Gson();
        String argJson = gson.toJson(vo.getData());
        HttpResponseMessage response = null;
        Map<String, String> header = new Gson().fromJson(vo.getHeader(), new TypeToken<Map<String, String>>() {}.getType());
        switch (vo.getType()){
            case ("POST"):
                response = DingRetryHandler.sendOkHttp3Post(vo.getUrl(), header, argJson);
                break;
            case ("GET"):
                response = DingRetryHandler.sendOkHttp3Get(vo.getUrl(), header, new HashMap<>());
                break;
            case ("PUT"):
                response = DingRetryHandler.sendOkHttp3Put(vo.getUrl(), header, argJson);
                break;
        }
        if (response == null) {
            log.error("请求处理失败，参数:type={},url:{},arg:{}.", vo.getType(), vo.getUrl(), argJson);
            return Result.newError(ResultCode.CLIENT_POST_FAILED);
        }
        log.info("ProxyController.proxyHttpRequest,response={}", response);
        return Result.newSuccess(response.getStatusCode(), response.getMessage(), response.getContent());
    }

    /**
     * 检测代理服务是否正常
     * 钉钉核心服务会定时调用这个接口，检测钉钉客户代理服务是否正常，如果异常，会进行异常上报
     *
     * @return
     */
    @RequestMapping(value = "/proxyLive", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public Result<Map<String,String>> proxyLive(@RequestBody Map<String,String> body) {
        log.info("ProxyController.proxyLive,body={}",body);
        body.put("proxyVersion",proxyVersion);
        log.info("ProxyController.proxyLive,body2={}",body);
        return Result.newSuccess(body);
    }
}
