package com.facishare.open.outer.oa.connector.web.openconnetor;

import com.alibaba.fastjson.TypeReference;
import com.facishare.open.order.contacts.proxy.api.network.ProxyHttpClient;
import com.facishare.open.outer.oa.connector.common.api.result.Result;
import com.facishare.open.outer.oa.connector.common.api.result.ResultCodeEnum;
import com.facishare.open.outer.oa.connector.i18n.I18nUtil;
import com.facishare.open.outer.oa.connector.web.manager.HubInfoManager;

import com.facishare.open.outer.oa.connector.web.openconnetor.model.HubInfo;
import com.facishare.open.outer.oa.connector.web.openconnetor.model.InterfaceUrlEnum;
import com.facishare.open.outer.oa.connector.web.openconnetor.model.SyncResult;
import com.facishare.rest.core.util.JacksonUtil;
import com.github.trace.TraceContext;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.Request;
import okhttp3.RequestBody;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 开放连接器
 *
 * <AUTHOR>
 * @date 2025/5/14
 */
@Slf4j
@Component
public class OpenConnectorHubHandler {
    @Autowired
    private ProxyHttpClient proxyHttpClient;
    @Autowired
    private HubInfoManager hubInfoManager;

    private static final String X_FS_ENTERPRISE_ID = "x-fs-enterprise-id";
    private static final String X_FS_EI = "x-fs-ei";
    private static final String X_TENANT_ID = "x-tenant-id";
    private static final String X_FS_LOCALE = "x-fs-locale";
    private static final String X_FS_RPC_ID = "x-fs-rpc-Id";
    private static final String X_FS_PEER_NAME = "x-fs-peer-name";
    private static final String X_FS_TRACE_ID = "x-fs-trace-id";
    private static final String X_FS_DC_ID = "x-fs-dc-id";
    private static final String X_FS_API_NAME = "x-fs-api-name";
    private static final MediaType JSONT = MediaType.get("application/json;charset=utf-8");


    /**
     * 执行
     */
    public <T> Result<T> execute(final String tenantId,
                                 final String dataCenterId,
                                 final InterfaceUrlEnum interfaceUrl,
                                 final String connectorApiName,
                                 final Object requestBody,
                                 TypeReference<T> resType) {
        return execute(tenantId, dataCenterId, interfaceUrl, connectorApiName, requestBody, null, resType);
    }

    /**
     * 执行apl方法
     */
    public <T> Result<T> execute(final String tenantId,
                                 final String dataCenterId,
                                 final InterfaceUrlEnum interfaceUrl,
                                 final String connectorApiName,
                                 final Object requestBody,
                                 final Integer timeoutSecond,
                                 TypeReference<T> resType) {

        Result<String> apiResult = null;
        try {
            String requestStr = requestBody == null ? "{}" : JacksonUtil.toJson(requestBody);
            HubInfo hubInfo = hubInfoManager.getHubInfo(tenantId, connectorApiName);
            //远程执行
            apiResult = executeRemote(tenantId, dataCenterId, connectorApiName, hubInfo.getBaseUrl(), interfaceUrl, requestStr, timeoutSecond);
            if (!apiResult.isSuccess()) {
                return Result.copy(apiResult);
            }
            SyncResult syncResult = SyncResult.parseFromStr(apiResult.getData());
            if (syncResult == null) {
                return Result.newError(ResultCodeEnum.SYSTEM_ERROR);
            }
            if (!syncResult.isSuccess()) {
                return Result.newError(ResultCodeEnum.SYSTEM_ERROR, syncResult.getMessage());
            }
            if(resType!=null){
                T resultData = syncResult.parseData(resType);
                return Result.newSuccess(resultData);
            }else{
                return Result.newSuccess();
            }
        } catch (Exception e) {
            log.warn("execute e=", e);
            return Result.newError(ResultCodeEnum.SYS_ERROR);
        } finally {
            //todo
        }
    }


    private Result<String> executeRemote(String tenantId, String dataCenterId, String connectorApiName, String hubBaseUrl, InterfaceUrlEnum interfaceUrl, String requestStr, Integer timeoutSecond) {
        Request.Builder builder = new Request.Builder();
        builder.url(hubBaseUrl + "/hub/connector/oa/" + connectorApiName + "/" + interfaceUrl.getMethodName())
                .header(X_FS_EI, tenantId)
                .header(X_TENANT_ID, tenantId)
                .header(X_FS_ENTERPRISE_ID, tenantId)
                .header(X_TENANT_ID, tenantId)
                .header(X_FS_PEER_NAME, "outer-oa-connector-web")
                .header(X_FS_DC_ID, dataCenterId)
                .header(X_FS_API_NAME, connectorApiName)
                .post(RequestBody.create(requestStr, JSONT));
        if(I18nUtil.getLocaleFromTrace()!=null){
            builder.header(X_FS_LOCALE, I18nUtil.getLocaleFromTrace());
        }
        if(TraceContext.get().getRpcId()!=null){
            builder.header(X_FS_RPC_ID, TraceContext.get().getRpcId());
        }
        if(TraceContext.get().getTraceId()!=null){
            builder.header(X_FS_TRACE_ID, TraceContext.get().getTraceId());
        }
        String result = proxyHttpClient.simpleRequest(builder, timeoutSecond);
        return Result.newSuccess(result);
    }


}
