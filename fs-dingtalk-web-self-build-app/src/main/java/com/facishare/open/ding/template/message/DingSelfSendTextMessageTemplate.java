package com.facishare.open.ding.template.message;

import com.alibaba.fastjson.JSONObject;
import com.facishare.open.ding.api.result.DingEnterpriseResult;
import com.facishare.open.ding.api.service.ExternalMessageService;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.provider.arg.SendTextMessageContextArg;
import com.facishare.open.ding.provider.constants.OAMessageTag;
import com.facishare.open.ding.provider.enums.DingTodoTypeEnum;
import com.facishare.open.ding.provider.manager.DingEnterpriseManager;
import com.facishare.open.ding.provider.manager.DingtalkManager;
import com.facishare.open.ding.web.config.ConfigCenter;
import com.facishare.open.ding.web.utils.ObjectMapperUtils;
import com.facishare.open.erpdss.outer.oa.connector.base.context.MethodContext;
import com.facishare.open.erpdss.outer.oa.connector.base.context.TemplateResult;
import com.facishare.open.erpdss.outer.oa.connector.base.outer.msg.SendMsgHandlerTemplate;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEnterpriseBindManager;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.FunctionMsgTypeEnum;
import com.facishare.open.outer.oa.connector.common.api.function.FunctionMsgBase;
import com.fxiaoke.message.extrnal.platform.model.arg.SendTextMessageArg;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2024/12/12 15:20
 * @desc
 */
@Component
@Slf4j
public class DingSelfSendTextMessageTemplate extends SendMsgHandlerTemplate {
    @Autowired
    private DingEnterpriseManager dingEnterpriseManager;
    @Autowired
    private DingtalkManager dingtalkManager;
    @Autowired
    private ExternalMessageService externalMessageService;
    @Autowired
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;



    @Override
    protected void filterMsg(MethodContext context) {
        SendTextMessageContextArg sendTextMessageContextArg = context.getData();
        SendTextMessageArg sendTextMessageArg=sendTextMessageContextArg.getSendTextMessageArg();
            //TODO 多应用设计
        final DingEnterpriseResult dingEnterpriseResult = sendTextMessageContextArg.getDingEnterpriseResult();
        if(dingEnterpriseResult ==null){
            TemplateResult templateResult= TemplateResult.newErrorData(dingEnterpriseResult);
            context.setResult(templateResult);
        }
        String tenantId=String.valueOf(sendTextMessageArg.getEi());
        String functionName = ConfigCenter.functionMaps.get(tenantId);
        if(StringUtils.isBlank(functionName)){
            return;
        }
        FunctionMsgBase functionMsgBase=new FunctionMsgBase();
        functionMsgBase.setChannel(ChannelEnum.dingding.getEnumName());
        functionMsgBase.setFsEa(dingEnterpriseResult.getEa());
        functionMsgBase.setAppId(dingEnterpriseResult.getAppKey());
        functionMsgBase.setType(FunctionMsgTypeEnum.crmExternalMsgPush.getType());
        functionMsgBase.setOutEa(dingEnterpriseResult.getDingCorpId());
        functionMsgBase.setDataCenterId(outerOaEnterpriseBindManager.getDcIdByEaAndAppId(ChannelEnum.dingding, dingEnterpriseResult.getEa(), dingEnterpriseResult.getAppKey()));
        functionMsgBase.setEventType(OAMessageTag.TEXT_MSG_TAG);
        functionMsgBase.setData(JSONObject.toJSONString(sendTextMessageContextArg));
        Map<String, Object> objectMap = ObjectMapperUtils.objectToMap(functionMsgBase);
        Result<List<DingTodoTypeEnum>> booleanResult = dingtalkManager.filterMsg(functionName, objectMap, sendTextMessageArg.getEi());
        if(!booleanResult.isSuccess()){
            TemplateResult templateResult= TemplateResult.newErrorData(booleanResult.getData());
            context.setResult(templateResult);
        }
    }

    @Override
    public void buildMsg(MethodContext context) {

    }

    @Override
    public void sendMsg(MethodContext context) {
        SendTextMessageContextArg sendTextMessageContextArg = context.getData();
        SendTextMessageArg sendTextMessageArg = sendTextMessageContextArg.getSendTextMessageArg();
        externalMessageService.sendTextMessage(sendTextMessageArg);
    }




}
