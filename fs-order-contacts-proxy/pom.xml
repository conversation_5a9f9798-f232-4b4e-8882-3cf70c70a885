<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <artifactId>fs-open-feishu-gateway</artifactId>
    <groupId>com.facishare.open</groupId>
    <version>1.0-SNAPSHOT</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>
  <packaging>jar</packaging>

  <artifactId>fs-order-contacts-proxy</artifactId>
  <version>1.0.0-SNAPSHOT</version>

  <dependencies>
    <!-- mybatis核心包 -->
    <dependency>
      <groupId>org.mybatis</groupId>
      <artifactId>mybatis</artifactId>
      <version>3.5.10</version>
    </dependency>
    <!-- mybatis/spring包 -->
    <dependency>
      <groupId>org.mybatis</groupId>
      <artifactId>mybatis-spring</artifactId>
      <version>2.0.6</version>
    </dependency>
    <!-- baomidou -->

    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-aspects</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-beans</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-context</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-context-support</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-core</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-tx</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-web</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-webmvc</artifactId>
    </dependency>

    <dependency>
      <groupId>org.apache.httpcomponents</groupId>
      <artifactId>httpcore</artifactId>
      <version>4.4.5</version>
    </dependency>
    <dependency>
      <groupId>org.apache.httpcomponents</groupId>
      <artifactId>httpclient</artifactId>
      <version>4.3.4</version>
    </dependency>

    <dependency>
      <groupId>redis.clients</groupId>
      <artifactId>jedis</artifactId>
    </dependency>

    <dependency>
      <groupId>com.github.colin-lee</groupId>
      <artifactId>jedis-spring-support</artifactId>
    </dependency>

    <dependency>
      <groupId>com.github.colin-lee</groupId>
      <artifactId>config-core</artifactId>
    </dependency>

    <dependency>
      <groupId>com.github.colin-lee</groupId>
      <artifactId>spring-support</artifactId>
    </dependency>

    <dependency>
      <groupId>ch.qos.logback</groupId>
      <artifactId>logback-core</artifactId>
    </dependency>
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-api</artifactId>
    </dependency>
    <dependency>
      <groupId>ch.qos.logback</groupId>
      <artifactId>logback-classic</artifactId>
    </dependency>

    <dependency>
      <groupId>com.alibaba</groupId>
      <artifactId>dubbo</artifactId>
      <exclusions>
        <exclusion>
          <groupId>org.javassist</groupId>
          <artifactId>javassist</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <!--升级版本-->
    <dependency>
      <groupId>io.netty</groupId>
      <artifactId>netty-all</artifactId>
    </dependency>
    <dependency>
      <groupId>org.javassist</groupId>
      <artifactId>javassist</artifactId>
    </dependency>

    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-test</artifactId>
      <version>4.1.7.RELEASE</version>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>com.fxiaoke</groupId>
      <artifactId>logconfig-core</artifactId>
    </dependency>

    <dependency>
      <groupId>com.fxiaoke.common</groupId>
      <artifactId>metrics-oss</artifactId>
    </dependency>

    <dependency>
      <groupId>com.google.code.gson</groupId>
      <artifactId>gson</artifactId>
    </dependency>

<!--    文件服务器-->
    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-stone-sdk</artifactId>
      <version>1.0-SNAPSHOT</version>
    </dependency>
    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-fsc-api</artifactId>
      <version>1.0-SNAPSHOT</version>
      <exclusions>
        <exclusion>
          <artifactId>protostuff-api</artifactId>
          <groupId>com.dyuproject.protostuff</groupId>
        </exclusion>
      </exclusions>
    </dependency>

    <!-- 企信Api -->
    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-qixin-api</artifactId>
      <version>0.1.1-SNAPSHOT</version>
      <exclusions>
        <exclusion>
          <artifactId>commons-lang</artifactId>
          <groupId>commons-lang</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-warehouse-api</artifactId>
      <version>1.0-SNAPSHOT</version>
    </dependency>

    <!--Mybatis查询分页-->
    <dependency>
      <groupId>com.github.pagehelper</groupId>
      <artifactId>pagehelper</artifactId>
      <version>5.1.6</version>
      <exclusions>
        <exclusion>
          <artifactId>jsqlparser</artifactId>
          <groupId>com.github.jsqlparser</groupId>
        </exclusion>
      </exclusions>
    </dependency>

    <!--xxl-job框架-->
    <dependency>
      <groupId>com.fxiaoke.common</groupId>
      <artifactId>fs-job-core</artifactId>
      <version>1.0.0-SNAPSHOT</version>
      <exclusions>
        <exclusion>
          <artifactId>httpclient</artifactId>
          <groupId>org.apache.httpcomponents</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-restful-common</artifactId>
    </dependency>

    <!-- id生成器 -->
    <dependency>
      <groupId>com.robert.vesta</groupId>
      <artifactId>vesta-service</artifactId>
      <version>0.0.1-SNAPSHOT</version>
      <exclusions>
        <exclusion>
          <artifactId>commons-logging</artifactId>
          <groupId>commons-logging</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-qixin-api</artifactId>
      <version>0.1.1-SNAPSHOT</version>
    </dependency>
    <!--mq-->
<!--    <dependency>-->
<!--      <groupId>com.facishare</groupId>-->
<!--      <artifactId>fs-common-mq</artifactId>-->
<!--      <version>1.1.0-SNAPSHOT</version>-->
<!--      <scope>compile</scope>-->
<!--    </dependency>-->

    <dependency>
      <groupId>com.fxiaoke</groupId>
      <artifactId>fs-rocketmq-support</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.rocketmq</groupId>
      <artifactId>rocketmq-client</artifactId>
    </dependency>

    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-webhook-common</artifactId>
      <version>8.0-SNAPSHOT</version>
      <exclusions>
        <exclusion>
          <artifactId>fs-paas-dao-support</artifactId>
          <groupId>com.facishare</groupId>
        </exclusion>
        <exclusion>
          <artifactId>fs-id-generator</artifactId>
          <groupId>com.fxiaoke.api</groupId>
        </exclusion>
        <exclusion>
          <artifactId>commons-configuration</artifactId>
          <groupId>commons-configuration</groupId>
        </exclusion>
        <exclusion>
          <artifactId>commons-lang</artifactId>
          <groupId>commons-lang</groupId>
        </exclusion>
        <exclusion>
          <artifactId>httpclient</artifactId>
          <groupId>org.apache.httpcomponents</groupId>
        </exclusion>
        <exclusion>
          <artifactId>spring-test</artifactId>
          <groupId>org.springframework</groupId>
        </exclusion>
        <exclusion>
          <artifactId>fs-webhook-api</artifactId>
          <groupId>com.facishare</groupId>
        </exclusion>
        <exclusion>
          <artifactId>fs-open-common-result</artifactId>
          <groupId>com.facishare.open</groupId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-webhook-api</artifactId>
      <version>1.0-SNAPSHOT</version>
    </dependency>

    <dependency>
      <artifactId>fs-sms-api</artifactId>
      <groupId>com.facishare</groupId>
      <version>1.0.0-SNAPSHOT</version>
    </dependency>

    <dependency>
      <artifactId>fs-register-api</artifactId>
      <groupId>com.facishare</groupId>
      <version>1.0-SNAPSHOT</version>
    </dependency>

    <dependency>
      <groupId>com.facishare.open</groupId>
      <artifactId>fs-oauth-base-api</artifactId>
      <version>0.0.31-SNAPSHOT</version>
<!--      <exclusions>-->
<!--        <exclusion>-->
<!--          <artifactId>fs-open-common-result</artifactId>-->
<!--          <groupId>com.facishare.open</groupId>-->
<!--        </exclusion>-->
<!--      </exclusions>-->
    </dependency>

    <dependency>
      <groupId>com.fxiaoke</groupId>
      <artifactId>fs-crm-rest-api</artifactId>
      <version>2.0.8_erpdss-SNAPSHOT</version>
    </dependency>

    <dependency>
      <groupId>com.fxiaoke</groupId>
      <artifactId>fs-other-rest-api</artifactId>
      <version>1.0.6-SNAPSHOT</version>
    </dependency>
    <dependency>
      <groupId>com.fxiaoke</groupId>
      <artifactId>fs-paas-auth-rest-api</artifactId>
      <version>1.1.0-SNAPSHOT</version>
    </dependency>
    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-organization-api</artifactId>
    </dependency>
    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-dubbo-rest-plugin</artifactId>
    </dependency>
    <dependency>
      <groupId>com.fxiaoke</groupId>
      <artifactId>fs-enterpriserelation-rest-api</artifactId>
      <version>2.0.0-SNAPSHOT</version>
    </dependency>

    <!-- 查询license版本-->
    <dependency>
      <groupId>com.facishare.paas</groupId>
      <artifactId>fs-paas-license-api</artifactId>
    </dependency>
    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-plat-privilege-api</artifactId>
      <version>1.0-SNAPSHOT</version>
    </dependency>
<!--    <dependency>-->
<!--      <groupId>com.facishare</groupId>-->
<!--      <artifactId>fs-common-mq</artifactId>-->
<!--      <version>1.0.3-SNAPSHOT</version>-->
<!--      <exclusions>-->
<!--        <exclusion>-->
<!--          <artifactId>config-core</artifactId>-->
<!--          <groupId>com.github.colin-lee</groupId>-->
<!--        </exclusion>-->
<!--      </exclusions>-->
<!--    </dependency>-->

    <!--spock单元测试-->
    <dependency>
      <groupId>org.spockframework</groupId>
      <artifactId>spock-spring</artifactId>
      <version>1.3-groovy-2.4</version>
    </dependency>
    <dependency>
      <groupId>org.spockframework</groupId>
      <artifactId>spock-core</artifactId>
    </dependency>

    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-fsi-proxy</artifactId>
      <version>3.0.0-SNAPSHOT</version>
    </dependency>

    <!-- fs-order-contacts-proxy-api pom.xml-->
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-context</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-context-support</artifactId>
    </dependency>
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
    </dependency>
    <dependency>
      <groupId>com.github.colin-lee</groupId>
      <artifactId>rpc-trace</artifactId>
    </dependency>
    <dependency>
      <groupId>com.google.guava</groupId>
      <artifactId>guava</artifactId>
    </dependency>
    <dependency>
      <groupId>com.fxiaoke</groupId>
      <artifactId>fs-crm-rest-api</artifactId>
      <version>2.0.8_erpdss-SNAPSHOT</version>
    </dependency>
    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-organization-api</artifactId>
    </dependency>
    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-organization-adapter-api</artifactId>
    </dependency>
    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-common-mds-event</artifactId>
      <version>1.0.4-SNAPSHOT</version>
    </dependency>
    <!--拼音-->
    <dependency>
      <groupId>com.belerweb</groupId>
      <artifactId>pinyin4j</artifactId>
      <version>2.5.1</version>
    </dependency>
    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-plat-privilege-api</artifactId>
      <version>1.0-SNAPSHOT</version>
    </dependency>

    <dependency>
      <groupId>org.aspectj</groupId>
      <artifactId>aspectjweaver</artifactId>
      <scope>provided</scope>
    </dependency>

    <dependency>
      <groupId>com.facishare.open</groupId>
      <artifactId>outer-oa-connector-i18n</artifactId>
      <version>1.0.0-SNAPSHOT</version>
    </dependency>
    <!-- fs-order-contacts-proxy-api pom.xml-->

    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-paas-dao-support</artifactId>
      <version>1.0.4-wujw-SNAPSHOT</version>
      <exclusions>
        <exclusion>
          <artifactId>commons-lang</artifactId>
          <groupId>commons-lang</groupId>
        </exclusion>
        <exclusion>
          <artifactId>mongo-java-driver</artifactId>
          <groupId>org.mongodb</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-webhook-common</artifactId>
      <version>1.0-SNAPSHOT</version>
      <exclusions>
        <exclusion>
          <artifactId>fs-paas-dao-support</artifactId>
          <groupId>com.facishare</groupId>
        </exclusion>
        <exclusion>
          <artifactId>fs-id-generator</artifactId>
          <groupId>com.fxiaoke.api</groupId>
        </exclusion>
        <exclusion>
          <artifactId>commons-configuration</artifactId>
          <groupId>commons-configuration</groupId>
        </exclusion>
        <exclusion>
          <artifactId>commons-lang</artifactId>
          <groupId>commons-lang</groupId>
        </exclusion>
        <exclusion>
          <artifactId>httpclient</artifactId>
          <groupId>org.apache.httpcomponents</groupId>
        </exclusion>
        <exclusion>
          <artifactId>spring-test</artifactId>
          <groupId>org.springframework</groupId>
        </exclusion>
        <exclusion>
          <artifactId>fs-webhook-api</artifactId>
          <groupId>com.facishare</groupId>
        </exclusion>
        <exclusion>
          <artifactId>fs-open-common-result</artifactId>
          <groupId>com.facishare.open</groupId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-webhook-common</artifactId>
      <version>8.0-SNAPSHOT</version>
      <exclusions>
        <exclusion>
          <artifactId>fs-paas-dao-support</artifactId>
          <groupId>com.facishare</groupId>
        </exclusion>
        <exclusion>
          <artifactId>fs-id-generator</artifactId>
          <groupId>com.fxiaoke.api</groupId>
        </exclusion>
        <exclusion>
          <artifactId>commons-configuration</artifactId>
          <groupId>commons-configuration</groupId>
        </exclusion>
        <exclusion>
          <artifactId>commons-lang</artifactId>
          <groupId>commons-lang</groupId>
        </exclusion>
        <exclusion>
          <artifactId>httpclient</artifactId>
          <groupId>org.apache.httpcomponents</groupId>
        </exclusion>
        <exclusion>
          <artifactId>spring-test</artifactId>
          <groupId>org.springframework</groupId>
        </exclusion>
        <exclusion>
          <artifactId>fs-webhook-api</artifactId>
          <groupId>com.facishare</groupId>
        </exclusion>
        <exclusion>
          <artifactId>fs-open-common-result</artifactId>
          <groupId>com.facishare.open</groupId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-user-login-api</artifactId>
      <version>1.0-SNAPSHOT</version>
    </dependency>
    <dependency>
      <groupId>com.facishare.open</groupId>
      <artifactId>fs-open-common-result</artifactId>
    </dependency>
    <dependency>
      <groupId>com.facishare.open</groupId>
      <artifactId>fs-open-common-storage</artifactId>
      <exclusions>
        <exclusion>
          <artifactId>commons-pool</artifactId>
          <groupId>commons-pool</groupId>
        </exclusion>
        <exclusion>
          <artifactId>mybatis</artifactId>
          <groupId>org.mybatis</groupId>
        </exclusion>
        <exclusion>
          <artifactId>mybatis-spring</artifactId>
          <groupId>org.mybatis</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.facishare.open</groupId>
      <artifactId>fs-open-common-utils</artifactId>
      <exclusions>
        <exclusion>
          <groupId>org.slf4j</groupId>
          <artifactId>log4j-over-slf4j</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.apache.httpcomponents</groupId>
      <artifactId>httpclient</artifactId>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-deploy-plugin</artifactId>
        <version>2.7</version>
        <configuration>
          <skip>true</skip>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>3.8.1</version>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-war-plugin</artifactId>
        <configuration>
          <warName>fs-order-contacts-proxy</warName>
        </configuration>
      </plugin>
    </plugins>

  </build>

</project>
