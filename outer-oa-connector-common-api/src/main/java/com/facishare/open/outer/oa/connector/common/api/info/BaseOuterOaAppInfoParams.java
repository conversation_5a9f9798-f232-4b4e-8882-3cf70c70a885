package com.facishare.open.outer.oa.connector.common.api.info;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @create 2024/11/24 14:58
 *渠道链接参数
 * @desc
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
public abstract class BaseOuterOaAppInfoParams implements Serializable {
    private static final long serialVersionUID = -1L;
    private String appNumber;
}
