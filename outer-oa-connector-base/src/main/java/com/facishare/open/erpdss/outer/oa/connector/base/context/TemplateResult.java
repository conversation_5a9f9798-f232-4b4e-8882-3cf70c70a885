package com.facishare.open.erpdss.outer.oa.connector.base.context;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

@Data
public class TemplateResult implements Serializable {
    private int code;
    private String msg;
    private Object data;

    public TemplateResult() {

    }

    public TemplateResult(Object data) {
        this.data = data;
    }

    public static TemplateResult newInstance(int code, String msg, Object data) {
        TemplateResult result = new TemplateResult();
        result.setCode(code);
        result.setMsg(msg);
        result.setData(data);
        return result;
    }

    public static TemplateResult newSuccess() {
        return new TemplateResult();
    }

    public static TemplateResult newSuccess(Object data) {
        return new TemplateResult(data);
    }

    public static TemplateResult newError(int code, String msg) {
        TemplateResult result = new TemplateResult();
        result.setCode(code);
        result.setMsg(msg);
        return result;
    }

    public static TemplateResult newError(String msg) {
        TemplateResult result = new TemplateResult();
        result.setCode(-1);
        result.setMsg(msg);
        return result;
    }

    public boolean isSuccess() {
        return code == 0;
    }

    public boolean isError() {
        return code != 0;
    }

    public String getDataOrMsg() {
        if(isSuccess()) {
            if(data!=null) return data.toString();
        } else {
            if(StringUtils.isNotEmpty(msg)) return msg;
        }
        return "";
    }

    public static TemplateResult newErrorData(Object data) {
        TemplateResult result = new TemplateResult();
        result.setCode(-1);
        result.setData(data);
        return result;
    }
}
