package com.facishare.open.ding.web.controller;

//import com.facishare.open.k3.cloud.api.enums.CustomerImportStatusEnum;
//import com.facishare.open.k3.cloud.api.enums.HistoryDataImportStatusEnum;
//import com.facishare.open.k3.cloud.api.enums.ProductImportStatusEnum;
//import com.facishare.open.k3.cloud.api.enums.RateTypeEnum;
import com.facishare.open.ding.api.enums.SyncTypeEnum;
import com.facishare.open.ding.api.result.ConfigurationCreateResult;
import com.facishare.open.ding.api.result.ConfigurationGetResult;
import com.facishare.open.ding.api.result.ConfigurationUpdateResult;
import com.facishare.open.ding.api.service.AuthService;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.common.result.ResultCode;
import com.facishare.open.ding.web.arg.ConfigurationCreateArg;
import com.facishare.open.ding.web.arg.ConfigurationDeleteArg;
import com.facishare.open.ding.web.arg.ConfigurationGetArg;
import com.facishare.open.ding.web.arg.ConfigurationUpdateArg;
import com.facishare.open.ding.web.base.BaseController;
import java.time.LocalTime;
import java.time.format.DateTimeParseException;
import java.time.temporal.ChronoUnit;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Created by system on 2018/4/9.
 */
@Slf4j
@RestController
@RequestMapping("/configuration")
public class ConfigurationController extends BaseController {

    @Autowired
    private AuthService authService;

//    @Autowired
//    private ConfigurationService configurationService;

    private String defaultStartTimeString = "00:00";

    private String defaultEndTimeString = "23:59";

    @RequestMapping(value = "/get")
    public Result<ConfigurationGetResult> get(ConfigurationGetArg arg) {
        String fsUserAccount = arg.getFsUserId();
        Result<Boolean> appAdminResult = authService.isAppAdmin(fsUserAccount);
        if (!appAdminResult.isSuccess()) {
            return Result.newError(appAdminResult.getErrorCode(), appAdminResult.getErrorMessage());
        }

        if (!appAdminResult.getData()) {
            return Result.newError(ResultCode.NOT_APP_MANAGER);
        }

//        ConfigurationGetVo vo = BeanUtil.copy(arg, ConfigurationGetVo.class);
//        return configurationService.get(vo);
        return new Result<>();
    }

    @RequestMapping(value = "/create")
    public Result<ConfigurationCreateResult> create(ConfigurationCreateArg arg) {
        String fsUserAccount = arg.getFsUserId();
        Result<Boolean> appAdminResult = authService.isAppAdmin(fsUserAccount);
        if (!appAdminResult.isSuccess()) {
            return Result.newError(appAdminResult.getErrorCode(), appAdminResult.getErrorMessage());
        }

        if (!appAdminResult.getData()) {
            return Result.newError(ResultCode.NOT_APP_MANAGER);
        }

       /* if (arg.getProductImport() == null) {
            arg.setProductImport(ProductImportStatusEnum.DISABLE.getStatus());
        }

        if (arg.getCustomerImport() == null) {
            arg.setCustomerImport(CustomerImportStatusEnum.DISABLE.getStatus());
        }

        if (HistoryDataImportStatusEnum.isInvalid(arg.getHistoryDataImport())) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }

        if (ProductImportStatusEnum.isInvalid(arg.getProductImport())) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }

        if (CustomerImportStatusEnum.isInvalid(arg.getCustomerImport())) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }

        if (arg.getHistoryDataImport() == HistoryDataImportStatusEnum.ENABLE.getStatus()
            && arg.getCustomerImport() == CustomerImportStatusEnum.DISABLE.getStatus()
            && arg.getProductImport() == ProductImportStatusEnum.DISABLE.getStatus()) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }

        if (SyncTypeEnum.isInvalid(arg.getSyncType())) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }

        if (arg.getSyncType() == SyncTypeEnum.MANUAL_SYNC.getType() && arg.getRateType() != null) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }

        if (arg.getSyncType() == SyncTypeEnum.TIMING_SYNC.getType()) {
            if (RateTypeEnum.isInvalid(arg.getRateType())) {
                return Result.newError(ResultCode.PARAMS_ERROR);
            }
            if (isInvalidStartEndTime(arg.getStartTime(), arg.getEndTime())) {
                return Result.newError(ResultCode.PARAMS_ERROR);
            }
        }*/

//        ConfigurationCreateVo vo = BeanUtil.copy(arg, ConfigurationCreateVo.class);
//        return configurationService.create(vo);
        return new Result<>();
    }

    @RequestMapping(value = "/update")
    public Result<ConfigurationUpdateResult> update(ConfigurationUpdateArg arg) {
        String fsUserAccount = arg.getFsUserId();
        Result<Boolean> appAdminResult = authService.isAppAdmin(fsUserAccount);
        if (!appAdminResult.isSuccess()) {
            return Result.newError(appAdminResult.getErrorCode(), appAdminResult.getErrorMessage());
        }

        if (!appAdminResult.getData()) {
            return Result.newError(ResultCode.NOT_APP_MANAGER);
        }

        if (arg.getId() == null || arg.getId() <= 0) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }

        if (SyncTypeEnum.isInvalid(arg.getSyncType())) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }

        if (arg.getSyncType() == SyncTypeEnum.MANUAL_SYNC.getType() && arg.getRateType() != null) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }

        if (arg.getSyncType() == SyncTypeEnum.TIMING_SYNC.getType()) {
            String startTime = arg.getStartTime();
            String endTime = arg.getEndTime();
            /*if (RateTypeEnum.isInvalid(arg.getRateType())) {
                return Result.newError(ResultCode.PARAMS_ERROR);
            }*/

            if (isInvalidStartEndTime(startTime, endTime)) {
                return Result.newError(ResultCode.PARAMS_ERROR);
            }
        }

//        ConfigurationUpdateVo vo = BeanUtil.copy(arg, ConfigurationUpdateVo.class);
//        return configurationService.update(vo);
        return new Result<>();
    }
//
//    @RequestMapping(value = "/delete")
//    public Result<Void> delete(ConfigurationDeleteArg arg) {
//
//        //return configurationService.delete(arg.getId());
//        return new Result<>();
//    }

    /**
     *
     * @param startTime
     * @param endTime
     * @return
     */
    private boolean isInvalidStartEndTime(String startTime, String endTime) {
        if (StringUtils.isBlank(startTime)) {
            return true;
        }
        if (StringUtils.isBlank(endTime)) {
            return true;
        }
        try {
            LocalTime startLocalTime = LocalTime.parse(startTime);
            LocalTime endLocalTime = LocalTime.parse(endTime);

            LocalTime startDefaultTime = LocalTime.parse(defaultStartTimeString);
            LocalTime endDefaultTime = LocalTime.parse(defaultEndTimeString);

            if (!endLocalTime.isAfter(startLocalTime)) {
                return true;
            }
            if (startLocalTime.isBefore(startDefaultTime)) {
                return true;
            }
            if (endDefaultTime.isAfter(endDefaultTime)) {
                return true;
            }
            if (!endLocalTime.isAfter(startLocalTime)) {
                return true;
            }

            long minutes = ChronoUnit.MINUTES.between(startLocalTime, endLocalTime);
            if (minutes < 3 * 60) { // 起止时间间隔必须大于或等于3小时
                return true;
            }
        } catch (DateTimeParseException pe) {
            log.warn("date time parse error, startTime={} endTime={}", startTime, endTime, pe);
            return true;
        }

        return false;
    }

}
