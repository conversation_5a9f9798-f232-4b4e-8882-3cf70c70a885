package com.facishare.open.outer.oa.connector.common.api.result;

import com.facishare.open.outer.oa.connector.i18n.I18NStringEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

@Getter
@ToString
@AllArgsConstructor
public enum ResultCodeEnum {
    /**
     * 成功
     **/
    SUCCESS(0, "0", "成功", I18NStringEnum.s1.getI18nKey()),

    /**
     * 参数不合法
     */
    PARAM_ILLEGAL(100, null, "参数不合法", I18NStringEnum.s2.getI18nKey()),
    SYSTEM_ERROR(101, null, "系统错误", I18NStringEnum.s3.getI18nKey()),
    OUT_SPEED_LIMITER(103, null, "外部服务限速，请稍后重试", I18NStringEnum.s4.getI18nKey()),
    BIND_ENTERPRISE_ERROR(104, null, "企业绑定失败，请检查参数是否正确", I18NStringEnum.s5.getI18nKey()),
    CREATE_CUSTOM_FAILED(200, null, "客户创建失败", I18NStringEnum.s6.getI18nKey()),
    CREATE_ORDER_FAILED(201, null, "订单创建失败", I18NStringEnum.s7.getI18nKey()),
    ORDER_INFO_NOT_EXISTS(202, null, "不存在订单信息", I18NStringEnum.s8.getI18nKey()),
    TICKET_NOT_EXISTS(210, null, "ticket不存在", I18NStringEnum.s9.getI18nKey()),
    TICKET_EXPIRED(211, null, "ticket已过期", I18NStringEnum.s10.getI18nKey()),
    CRM_APP_NOT_INSTALLED(220, null, "CRM应用未安装", I18NStringEnum.s11.getI18nKey()),
    FS_EA_HAS_BOUND(221, null, "飞书企业已经和当前纷享企业绑定，请勿重复绑定", I18NStringEnum.s12.getI18nKey()),
    SAVE_CORP_BIND_FAILED(222, null, "保存纷享和飞书企业绑定信息失败", I18NStringEnum.s13.getI18nKey()),
    APP_ADMIN_NOT_BIND(223, null, "查询不到已绑定的应用管理员", I18NStringEnum.s14.getI18nKey()),
    FS_EA_NOT_BIND(224, null, "企业未绑定", I18NStringEnum.s15.getI18nKey()),
    UNBIND_FAILED(225, null, "解绑失败", I18NStringEnum.s16.getI18nKey()),
    GET_CRM_INSTALLER_FAILED(226, null, "获取CRM安装人员失败", I18NStringEnum.s17.getI18nKey()),
    CONNECTOR_CONNECT_INFO_NOT_EXISTS(227, null, "连接器连接信息不存在", I18NStringEnum.s18.getI18nKey()),
    CONNECTOR_CONNECT_INFO_EXISTS(228, null, "连接器连接信息已存在", I18NStringEnum.s19.getI18nKey()),
    NO_UNBIND_FS_EMPLOYEE(229, null, "没有未绑定的纷享员工", I18NStringEnum.s20.getI18nKey()),
    EMPLOYEE_NO_BIND_INFO(230, null, "员工没有绑定关系", I18NStringEnum.s21.getI18nKey()),
    EMPLOYEE_HAS_BIND(231, null, "员工已绑定，无需重新绑定", I18NStringEnum.s22.getI18nKey()),
    OUT_EA_NOT_BIND(232, null, "第三方企业未绑定", I18NStringEnum.s23.getI18nKey()),
    IDENTITY_CHECK_ERROR(240, null, "身份校验失败，cookie不存在", I18NStringEnum.s24.getI18nKey()),
    MESSAGE_SEND_ERROR(250, null, "消息发送失败", I18NStringEnum.s25.getI18nKey()),
    FUNCTION_ERROR(260, null, "自定义函数调用失败", I18NStringEnum.s26.getI18nKey()),

    GET_TEMPLATE_ERROR(300, null, "拉取模板失败", I18NStringEnum.s27.getI18nKey()),
    CREATE_TEMPLATE_ERROR(301, null, "创建模板失败", I18NStringEnum.s28.getI18nKey()),
    UPDATE_TEMPLATE_ERROR(302, null, "更新模板失败", I18NStringEnum.s29.getI18nKey()),
    DELETE_TEMPLATE_ERROR(303, null, "删除模板失败", I18NStringEnum.s30.getI18nKey()),
    GET_ENTERPRISE_PHONE_ERROR(304, null, "获取企业下的手机号码失败", I18NStringEnum.s31.getI18nKey()),
    SEND_MESSAGE_ERROR(305, null, "发送消息失败", I18NStringEnum.s32.getI18nKey()),
    GET_FILE_META_DATA_FAILED(306, null, "获取文件元数据失败", I18NStringEnum.s33.getI18nKey()),
    IMAGE_OVERSIZE(307, null, "图片大小超过5M", I18NStringEnum.s34.getI18nKey()),
    AUDIO_OVERSIZE(308, null, "音频大小超过16M", I18NStringEnum.s35.getI18nKey()),
    VIDEO_OVERSIZE(309, null, "视频大小超过16M", I18NStringEnum.s36.getI18nKey()),
    DOCUMENT_OVERSIZE(310, null, "文档大小超过100M", I18NStringEnum.s37.getI18nKey()),
    STICKER_OVERSIZE(311, null, "贴纸大小超过100KB", I18NStringEnum.s38.getI18nKey()),
    WHATSAPP_HAS_BOUND(312, null, "whatsapp账号已被绑定", I18NStringEnum.s39.getI18nKey()),
    GET_NX_BALANCE_ERROR(313, null, "查询牛信云余额失败", I18NStringEnum.s40.getI18nKey()),
    UPLOAD_FILE_ERROR(314, null, "上传文件失败", I18NStringEnum.s41.getI18nKey()),
    UPLOAD_TEMPLATE_FILE_ERROR(315, null, "上传模板文件失败", I18NStringEnum.s42.getI18nKey()),
    ONE_FS_EA_CANNOT_BIND_SAME_OUT_EA(316, null, "同一个纷享企业，不同的飞书连接器，不能绑定同一个飞书企业", I18NStringEnum.s43.getI18nKey()),
    OUT_EA_IS_AUTO_BIND_CANNOT_MANUAL_BIND(317, null, "当前绑定的飞书企业已经和别的纷享企业存在自动绑定关系，请先解绑原自动绑定关系，再执行反绑定操作;fsEa=%s",
            I18NStringEnum.s44.getI18nKey()),
    FEISHU_CONNECTOR_INIT_FAILED(318, null, "当前绑定的飞书企业已经和别的纷享企业存在自动绑定关系，请先解绑原自动绑定关系，再执行反绑定操作",
            I18NStringEnum.s45.getI18nKey()),
    FEISHU_NOT_PRIVILEGE_ERROR(319, "41050", "指定的成员无权限", I18NStringEnum.s46.getI18nKey()),
    CRM_USER_UPPER_LIMIT_INITED(320, "44", "贵企业总账号已达上限", I18NStringEnum.s47.getI18nKey()),
    CRM_USER_ACCOUNT_CREATE_ERROR(321, null, "该员工创建失败", I18NStringEnum.s48.getI18nKey()),
    CALL_OUT_INTERFACE_FAILED(322, null, "调用外部系统接口失败", I18NStringEnum.s49.getI18nKey()),
    FEISHU_INTERNAL_APP_NOT_SUPPORT(323, null, "飞书自建应用没有绑定", I18NStringEnum.s136.getI18nKey()),
    FEISHU_APP_SECRET_ERROR(324, null, "飞书应用信息不对", I18NStringEnum.s137.getI18nKey()),
    FEISHU_TENANT_OUT_EA_ERROR(325, null, "飞书企业信息不对", I18NStringEnum.s138.getI18nKey()),
    NOT_SUPPORT_SWITCH_AUTH_RULES(326, null, "人员存在绑定，不允许切换到自动模式", I18NStringEnum.s138.getI18nKey()),

    /**
     * whatsapp_nx
     */
    MISSING_COMMON_PARAMETERS(400, "1001", null, null), PARAMETER_ERROR(401, "1002", null, null),
    INVALID_SIGNATURE(402, "1003", null, null), TIMESTAMP_HAS_EXPIRED(403, "1004", null, null),
    INSUFFICIENT_PERMISSIONS(404, "1005", null, null), FAILURE(450, "-1", null, null),
    ACCOUNT_STATUS_ABNORMAL(451, "1100", null, null), INSUFFICIENT_BALANCE(452, "1102", null, null),
    REQUEST_PARAMETER_ERROR(453, "9000", null, null), SYSTEM_BUSINESS_ERROR(454, "9001", null, null),
    PHONE_NUMBER_ERROR(455, "9002", null, null), ABNORMAL_APPLICATION_STATUS(456, "9003", null, null),
    APPLICATION_NOT_QUOTATION(457, "9004", null, null), WHATSAPP_PHONE_NOT_BIND(458, "10003", null, null),
    WHATSAPP_PHONE_NOT_BIND2(459, "9006", null, null), NX_APP_KEY_NOT_EXISTS(460, "9008", null, null),

    GET_CRM_CALENDAR_ERROR(600, null, "查询纷享日程失败", I18NStringEnum.s50.getI18nKey()),
    CREATE_CALENDAR_ERROR(601, null, "创建日历失败", I18NStringEnum.s51.getI18nKey()),
    CREATE_CALENDAR_EVENT_ERROR(602, null, "创建日程失败", I18NStringEnum.s52.getI18nKey()),
    ADD_CALENDAR_EVENT_ATTENDEE_ERROR(603, null, "添加日程参与人失败", I18NStringEnum.s53.getI18nKey()),
    UPDATE_CALENDAR_EVENT_ERROR(603, null, "更新日程失败", I18NStringEnum.s54.getI18nKey()),
    DELETE_CALENDAR_EVENT_ATTENDEE_ERROR(604, null, "删除日程参与人失败", I18NStringEnum.s55.getI18nKey()),
    DELETE_CALENDAR_EVENT_ERROR(605, null, "更新日程失败", I18NStringEnum.s56.getI18nKey()),

    UNBIND_FAILED_WITH_AUTO_BIND_EA(606, null, "没有自动绑定的飞书企业，不支持解绑。请到原手动绑定的企业进行解绑，路径：集成平台->连接器->飞书连接器",
            I18NStringEnum.s57.getI18nKey()),

    DATA_LIST_CANNOT_EMPTY(610, null, "数据列表不可为空", I18NStringEnum.s58.getI18nKey()),
    NPATH_FILE_DOWNLOAD_FAILED(611, null, "NPATH文件下载失败", I18NStringEnum.s59.getI18nKey()),

    CREATE_APPROVAL_TEMPLATE_ERROR(612, null, "创建审批模板失败", I18NStringEnum.s60.getI18nKey()),
    CREATE_APPROVAL_TODO_ERROR(613, null, "创建审批失败", I18NStringEnum.s61.getI18nKey()),
    DEAL_APPROVAL_TODO_ERROR(614, null, "处理审批失败", I18NStringEnum.s62.getI18nKey()),
    DELETE_APPROVAL_TODO_ERROR(615, null, "删除审批失败", I18NStringEnum.s63.getI18nKey()),
    EXTERNAL_DEAL_TODO_ERROR(616, null, "处理失败，请到纷享销客crm处理", I18NStringEnum.s64.getI18nKey()),
    CRM_USER_QUERY_ERROR(617, null, "纷享员工查询失败", I18NStringEnum.s130.getI18nKey()),
    CRM_DEAL_TODO_ERROR(618, null, "此节点无法直接审批，请到CRM处理", I18NStringEnum.s131.getI18nKey()),
    EMPLOYEE_NEED_SETTING_RULES(619, null, "批量绑定需要先设置绑定规则", I18NStringEnum.s132.getI18nKey()),

    OUTER_EMPDATA_NOT_EXISTS(710, null, "外部数据不存在", I18NStringEnum.s170.getI18nKey()),

    /**
     * 系统错误
     */
    SYS_ERROR(999, null, "系统错误", I18NStringEnum.s3.getI18nKey()),

    /**
     * 无可用License
     */
    LICENSE_NO_AVAILABLE(700, null, "OA连接器已过期", I18NStringEnum.s171.getI18nKey()),

    /**
     * 部门数据无绑定信息
     */
    DEPARTMENT_NO_BIND_INFO(720, null, "部门无绑定信息", I18NStringEnum.s172.getI18nKey()),

    /**
     * 外部OA部门数据不存在
     */
    OUTER_DEPTDATA_NOT_EXISTS(730, null, "外部OA部门数据不存在", I18NStringEnum.s173.getI18nKey()),

    SUPPORT_ISV_REDIRECT_URL(731, null, "现在只支持isv跳转", I18NStringEnum.s174.getI18nKey()),

    TASK_IS_PROCESSING(800, null, "任务处理中,请稍候", I18NStringEnum.cxb006.getI18nKey()),
    PREVIOUS_TASK_IS_PROCESSED(801, null, "前面有任务在处理中,完成后请刷新页面", I18NStringEnum.cxb007.getI18nKey()),
    DING_SB_HAS_ISV_BIND(802, null, "该企业已绑定钉钉isv应用", I18NStringEnum.cxb008.getI18nKey()),
    DING_SB_APP_HAS_BIND(803, null, "该钉钉应用已绑定其他企业", I18NStringEnum.cxb009.getI18nKey()),

    NEED_OUTER_OA_EMP_NAME(732, null, "避免外部数据字段都是空，需要必须选择映射OA系统人员【姓名】", I18NStringEnum.s178.getI18nKey()),

    WAIT_FEW_MINUTES_SAVE(733, null, "由于更新了人员字段映射，后台还在重新拉取全部人员数据，请稍后重试~", I18NStringEnum.s179.getI18nKey()),
    ASYNC_ALERT(734, null, "执行超时，请稍后重试", I18NStringEnum.s180.getI18nKey()),

    EMPLOYEE_CREATE_ERROR(735, null, "批量同步员工成功%s条，失败%s.失败原因：%s", I18NStringEnum.s182.getI18nKey()),

    OUTER_OA_HAS_MORE_CREATE(736, null, "存在未保存设置的连接器", I18NStringEnum.s182.getI18nKey()),
    DING_UNSUPPORTED_INNER_IP(737, null, "不允许使用内网IP", I18NStringEnum.kniip.getI18nKey()),
    DING_NOT_FOUND_USER_DATA(738, null, "无法找到该员工，请确认员工Id和应用权限范围", I18NStringEnum.knfuser.getI18nKey()),

    //czx用 start  900
    QYWX_DEPARTMENT_ID_ERROR(900, null, "企微部门id格式填写错误", I18NStringEnum.c001.getI18nKey()),

    QYWX_AGENT_ID_ERROR(901, null, "企微应用agentId未填写或填写错误", I18NStringEnum.c002.getI18nKey()),

    QYWX_APP_NOT_FOUND_ERROR(902, null, "未找到对应的企微应用信息", I18NStringEnum.c003.getI18nKey()),

    QYWX_APP_DISABLED_ERROR(903, null, "当前企微应用已停用，请重新启用", I18NStringEnum.c004.getI18nKey()),

    QYWX_APP_ALREADY_BOUND_ERROR(904, null, "当前企业已绑定相同的企微应用", I18NStringEnum.c005.getI18nKey()),

    QYWX_BIND_OTHER_APP_ERROR(905, null, "当前企业已经绑定了其他类型的企微应用，请先在企微连接器解绑其他类型应用的绑定关系", I18NStringEnum.c006.getI18nKey()),
    //czx用 end  1000

    ;
    /**
     * 错误码
     */
    private final int code;
    /**
     * 外部错误码
     */
    private final String outCode;
    /**
     * 错误信息
     */
    private final String msg;
    /**
     * 多语词条
     */
    private String i18nKey;
    /**
     * 错误码映射关系
     */
    private static final Map<String, ResultCodeEnum> codeMap = new HashMap<>();

    static {
        for (ResultCodeEnum codeEnum : ResultCodeEnum.values()) {
            if (StringUtils.isNotEmpty(codeEnum.getOutCode())) {
                codeMap.put(codeEnum.getOutCode(), codeEnum);
            }
        }
    }

    public static Integer getCodeByOutCode(String outCode, Integer defaultCode) {
        ResultCodeEnum codeEnum = codeMap.get(outCode);
        if (codeEnum == null) {
            return defaultCode;
        }
        return codeEnum.code;
    }
}
