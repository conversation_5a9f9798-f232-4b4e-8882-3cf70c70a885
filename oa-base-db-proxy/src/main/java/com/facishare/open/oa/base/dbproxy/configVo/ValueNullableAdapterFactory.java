package com.facishare.open.oa.base.dbproxy.configVo;

import com.google.gson.Gson;
import com.google.gson.TypeAdapter;
import com.google.gson.TypeAdapterFactory;
import com.google.gson.reflect.TypeToken;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;

import java.io.IOException;

/**
 * <AUTHOR> (^_−)☆
 * @date 2022/4/18
 */
public class ValueNullableAdapterFactory implements TypeAdapterFactory {

    private final Class<?> aClass;
    public ValueNullableAdapterFactory(Class<?> cls) {
        aClass = cls;
    }

    @Override
    public <T> TypeAdapter<T> create(Gson gson, TypeToken<T> type) {
        Class<? super T> rawType = type.getRawType();
        if (rawType != aClass) {
            return null;
        }
        TypeAdapter<T> delegateAdapter = gson.getDelegateAdapter(ValueNullableAdapterFactory.this, type);
        boolean temp = gson.serializeNulls();

        return new TypeAdapter<T>() {
            @Override
            public void write(JsonWriter out, T value) throws IOException {
                try {
                    out.setSerializeNulls(true);
                    delegateAdapter.write(out, value);
                }finally {
                    out.setSerializeNulls(temp);
                }
            }

            @Override
            public T read(JsonReader in) throws IOException {
                return delegateAdapter.read(in);
            }

        };
    }
}
