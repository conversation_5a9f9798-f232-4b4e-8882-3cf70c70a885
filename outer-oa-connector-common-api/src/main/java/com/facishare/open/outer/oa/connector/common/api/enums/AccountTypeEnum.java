package com.facishare.open.outer.oa.connector.common.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 消息提醒类型枚举
 */
@Getter
@AllArgsConstructor
public enum AccountTypeEnum {

    /**
     * 人员
     */
    EMP_BIND("PersonnelObj", "人员绑定"),

    

    /**
     * 部门绑定
     */
    DEPT_BIND("DepartmentObj", "部门绑定"),
    ;



    /**
     * 类型编码
     */
    private final String code;

    /**
     * 类型描述
     */
    private final String desc;

    /**
     * 根据code获取枚举
     *
     * @param code 类型编码
     * @return 对应的枚举值，如果不存在返回null
     */
    public static AccountTypeEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (AccountTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}