package com.facishare.open.ding.provider.manager;

import com.alibaba.fastjson.JSON;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.facishare.converter.EIEAConverter;
import com.facishare.open.ding.api.result.BindFxEaResult;
import com.facishare.open.ding.api.result.DingEnterpriseResult;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.common.result.ResultCode;
import com.facishare.open.ding.common.utils.BeanUtil;
import com.facishare.open.ding.provider.entity.DingEnterprise;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaAppInfoEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaAppInfoManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaConfigInfoManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEnterpriseBindManager;
import com.facishare.open.oa.base.dbproxy.pg.mapper.OuterOaAppInfoMapper;
import com.facishare.open.outer.oa.connector.common.api.admin.DingConnectorVo;
import com.facishare.open.outer.oa.connector.common.api.enums.*;
import com.facishare.open.outer.oa.connector.common.api.params.DingtalkAppInfoParams;
import com.facishare.open.outer.oa.connector.common.api.result.SettingsResult;
import com.facishare.open.outer.oa.connector.i18n.I18NStringManager;
import com.fxiaoke.api.IdGenerator;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>类的详细说明</p>
 *
 * @version 1.0
 * @dateTime 2018-07-12 11:08
 *
 */
@Slf4j
@Component
public class DingEnterpriseManager {

    @Autowired
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;

    @Autowired
    private OuterOaAppInfoMapper outerOaAppInfoMapper;
    @Autowired
    private OuterOaAppInfoManager outerOaAppInfoManager;

    @Autowired
    private EIEAConverter eieaConverter;

    @Autowired
    private I18NStringManager i18NStringManager;

    @Autowired
    private OuterOaConfigInfoManager outerOaConfigInfoManager;

    private static final Integer BIND = 1;

    public static final Integer NOTBIND = 0;

    public Result<DingEnterpriseResult> queryEnterpriseByEa(String ea, String appId) {
        if (StringUtils.isBlank(ea)) {
            log.warn("queryKcEnterpriseByEa param illegal, ea=[{}].", ea);
            return Result.newError(ResultCode.PARAMS_ERROR);
        }
        OuterOaEnterpriseBindEntity entity = outerOaEnterpriseBindManager.getByFsEaAndAppId(ChannelEnum.dingding, ea, appId);
        if (ObjectUtils.isEmpty(entity)) {
            return Result.newSuccess();
        }
        DingEnterprise result = convertToDingEnterprise(entity);
        DingEnterpriseResult dingEnterpriseResult = BeanUtil.copy(result, DingEnterpriseResult.class);
        dingEnterpriseResult.setIsBind(BIND);
        return Result.newSuccess(dingEnterpriseResult);
    }

    /**
     * 查询企业是否已帮定
     * 获取不到时候，DingEnterpriseResult为空
     * @param ei
     * @return
     */
    public Result<DingEnterpriseResult> queryEnterpriseInfoByEi(Integer ei, String appId) {
        if (Objects.isNull(ei)) {
            log.warn("queryKcEnterpriseByEi param illegal, ei=[{}].", ei);
            return Result.newError(ResultCode.PARAMS_ERROR);
        }
        OuterOaEnterpriseBindEntity entity = outerOaEnterpriseBindManager.getByFsEaAndAppId(ChannelEnum.dingding, eieaConverter.enterpriseIdToAccount(ei), appId);
        if (Objects.isNull(entity)) {
            return Result.newError(ResultCode.ENTERPRISE_NOT_BIND);
        }
        DingEnterpriseResult dingEnterpriseResult = BeanUtil.copy(convertToDingEnterprise(entity), DingEnterpriseResult.class);
        return Result.newSuccess(dingEnterpriseResult);
    }

    /**
     * 查询企业是否已帮定
     * 无论有没有值，DingEnterpriseResult都不为空
     * @param ei
     * @return
     */
    public Result<DingEnterpriseResult> queryEnterpriseByEi(Integer ei, String appId) {
        if (Objects.isNull(ei)) {
            log.warn("queryKcEnterpriseByEi param illegal, ei=[{}].", ei);
            return Result.newError(ResultCode.PARAMS_ERROR);
        }
        OuterOaEnterpriseBindEntity dingEnterpriseResult = outerOaEnterpriseBindManager.getByFsEaAndAppId(ChannelEnum.dingding, eieaConverter.enterpriseIdToAccount(ei), appId);
        if (Objects.isNull(dingEnterpriseResult)) {
            DingEnterpriseResult result = new DingEnterpriseResult();
            result.setIsBind(NOTBIND);
            return Result.newSuccess(result);
        }
        DingEnterpriseResult result = BeanUtil.copy(convertToDingEnterprise(dingEnterpriseResult), DingEnterpriseResult.class);
        result.setIsBind(BIND);
        return Result.newSuccess(result);
    }

    /**
     * 通过钉钉corpId查询绑定企业信息
     * @param corpId
     * @return
     */
    public Result<List<DingEnterprise>> queryEnterpriseByDingCorpId(String corpId, String appId) {
        if (StringUtils.isEmpty(corpId)) {
            log.warn("queryKcEnterpriseByEi param illegal, corpId=[{}].", corpId);
            return Result.newError(ResultCode.PARAMS_ERROR);
        }
        List<OuterOaEnterpriseBindEntity> entities = outerOaEnterpriseBindManager.getEntitiesByOutEa(ChannelEnum.dingding, corpId, appId);
        if (CollectionUtils.isEmpty(entities)) {
            log.warn("the enterprise not bind, corpId={} appId={}.", corpId, appId);
            return Result.newError(ResultCode.ENTERPRISE_NOT_BIND);
        }
        List<DingEnterprise> dingEnterpriseResult = convertToDingEnterpriseAndDecrypt(entities);
        return Result.newSuccess(dingEnterpriseResult);
    }

    /**
     * 通过钉钉appKey查询绑定企业信息
     * @param appKey
     * @return
     */
    public Result<List<DingEnterprise>> queryEnterPriseByAppKey(String appKey) {
        log.info("appeky queryEnterPriseByAppKey :{}", appKey);
        log.info("appkey:{},equals:{}", StringUtils.isEmpty(appKey), appKey.equals("null"));
        if (StringUtils.isEmpty(appKey)) {
            log.info("queryKcEnterpriseByEi param illegal, appKey=[{}].", appKey);
            return Result.newError(ResultCode.PARAMS_ERROR);
        }
        List<OuterOaEnterpriseBindEntity> entities = outerOaEnterpriseBindManager.getEntitiesByAppId(ChannelEnum.dingding, appKey);
        if (CollectionUtils.isEmpty(entities)) {
            log.warn("the enterprise not bind, appKey=[{}].", appKey);
            return Result.newError(ResultCode.ENTERPRISE_NOT_BIND);
        }
        List<DingEnterprise> dingEnterpriseResult = convertToDingEnterpriseAndDecrypt(entities);
        return Result.newSuccess(dingEnterpriseResult);
    }

    @Transactional
    public Result<Integer> saveEnterprise(DingEnterprise enterprise) {
        if (Objects.isNull(enterprise)) {
            log.warn("saveEnterprise param illegal, enterprise=[{}].", enterprise);
            return Result.newError(ResultCode.PARAMS_ERROR);
        }

        final String appId = enterprise.getAppKey();

        // 保存应用信息
        final OuterOaAppInfoEntity appInfoEntity = convert2AppInfoEntity(enterprise, appId);
        appInfoEntity.setId(IdGenerator.get());

        // 保存企业绑定信息
        OuterOaEnterpriseBindEntity bindEntity = convertToBindEntity(enterprise);
        bindEntity.setId(IdGenerator.get());

        // 同时保存两个表
        outerOaAppInfoManager.upsert(appInfoEntity);
        Integer count = outerOaEnterpriseBindManager.insert(bindEntity);

        return Result.newSuccess(count);
    }

    @NotNull
    private static OuterOaAppInfoEntity convert2AppInfoEntity(DingEnterprise enterprise, String appId) {
        OuterOaAppInfoEntity appInfoEntity = new OuterOaAppInfoEntity();
        appInfoEntity.setAppType(OuterOaAppInfoTypeEnum.selfBuild);
        appInfoEntity.setStatus(OuterOaAppInfoStatusEnum.normal);
        appInfoEntity.setCreateTime(Objects.nonNull(enterprise.getCreateTime()) ? enterprise.getCreateTime().getTime() : System.currentTimeMillis());
        appInfoEntity.setUpdateTime(Objects.nonNull(enterprise.getUpdateTime()) ? enterprise.getUpdateTime().getTime() : System.currentTimeMillis());

        appInfoEntity.setChannel(ChannelEnum.dingding);
        appInfoEntity.setAppId(appId);
        appInfoEntity.setOutEa(enterprise.getDingCorpId());

        final String jsonString = convert2AppInfo(enterprise);
        appInfoEntity.setAppInfo(jsonString);
        return appInfoEntity;
    }

    @NotNull
    private static String convert2AppInfo(DingEnterprise enterprise) {
        DingtalkAppInfoParams appInfo = new DingtalkAppInfoParams();
        appInfo.setAppSecret(enterprise.getAppSecret());
        appInfo.setToken(enterprise.getToken());
        appInfo.setRedirectAppId(enterprise.getRedirectAppId());
        appInfo.setRedirectAppSecret(enterprise.getRedirectAppSecret());
        appInfo.setClientIp(enterprise.getClientIp());
        appInfo.setAgentId(enterprise.getAgentId());
        return JSON.toJSONString(appInfo);
    }

    public Result<Integer> deleteEnterprise(DingEnterprise enterprise) {
        if (Objects.isNull(enterprise)) {
            log.warn("deleteEnterprise param illegal, enterprise=[{}].", enterprise);
            return Result.newError(ResultCode.PARAMS_ERROR);
        }

        // appInfo可能还绑定了其他crm企业, 所以不能直接删除

        // 删除企业绑定信息
        Integer count = outerOaEnterpriseBindManager.deleteByBusinessId(enterprise.getEa(), enterprise.getAppKey(), ChannelEnum.dingding);

        return Result.newSuccess(count);
    }

    public Result<DingEnterpriseResult> queryKcEnterprise(String ea, String appId) {
        if (StringUtils.isBlank(ea)) {
            log.warn("queryKcEnterprise param illegal, ea=[{}].", ea);
            return Result.newError(ResultCode.PARAMS_ERROR);
        }
        OuterOaEnterpriseBindEntity entity = outerOaEnterpriseBindManager.getByFsEaAndAppId(ChannelEnum.dingding, ea, appId);
        if (ObjectUtils.isEmpty(entity)) {
            return Result.newSuccess();
        }
        return Result.newSuccess(BeanUtil.copy(convertToDingEnterprise(entity), DingEnterpriseResult.class));
    }

    @Transactional
    public Result<Integer> updateEnterprise(DingEnterprise dingEnterprise) {
        if (dingEnterprise == null) {
            log.warn("updateEnterprise param illegal, dingEnterprise=[{}].", dingEnterprise);
            return Result.newError(ResultCode.PARAMS_ERROR);
        }
        final String appId = dingEnterprise.getAppKey();

        // 更新应用信息
        outerOaAppInfoManager.batchUpsertInfos(Lists.newArrayList(convert2AppInfoEntity(dingEnterprise, appId)));

        // 更新企业绑定信息
        OuterOaEnterpriseBindEntity entity = convertToBindEntity(dingEnterprise);
        entity.setId(String.valueOf(dingEnterprise.getId()));

        Integer count = outerOaEnterpriseBindManager.updateById(entity);

        return Result.newSuccess(count);
    }


    public Result<List<BindFxEaResult>> queryBindFxEa(Integer offset, Integer limit, String appId) {
        LambdaQueryWrapper<OuterOaEnterpriseBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OuterOaEnterpriseBindEntity::getChannel, ChannelEnum.dingding);
        if (StringUtils.isNotEmpty(appId)) {
            wrapper.eq(OuterOaEnterpriseBindEntity::getAppId, appId);
        }
        // ORDER BY create_time LIMIT #{offset}, #{limit}
        wrapper.orderBy(true, true, OuterOaEnterpriseBindEntity::getCreateTime)
                .last("limit " + limit + " offset " + offset);
        List<OuterOaEnterpriseBindEntity> entities = outerOaEnterpriseBindManager.getEntitiesByAppId(ChannelEnum.dingding, appId);
        List<BindFxEaResult> results = entities.stream()
                .map(entity -> {
                    BindFxEaResult result = new BindFxEaResult();
                    result.setEa(entity.getFsEa());
                    return result;
                })
                .collect(Collectors.toList());
        return Result.newSuccess(results);
    }

    //修改回调地址使用
    public Result<Integer> updateCallBack(DingEnterprise dingEnterprise) {
        if (dingEnterprise == null) {
            log.warn("updateCallBack param illegal, dingEnterprise=[{}].", dingEnterprise);
            return Result.newError(ResultCode.PARAMS_ERROR);
        }
        final String appId = dingEnterprise.getAppKey();

        // 更新企业绑定信息
        OuterOaEnterpriseBindEntity entity = convertToBindEntity(dingEnterprise);
        Integer count = outerOaEnterpriseBindManager.updateByBusinessId(entity);

        return Result.newSuccess(count);
    }

    private List<DingEnterprise> convertToDingEnterpriseAndDecrypt(List<OuterOaEnterpriseBindEntity> entities) {
        return entities.stream()
                .map(this::convertToDingEnterprise)
                .collect(Collectors.toList());
    }

    private DingEnterprise convertToDingEnterprise(OuterOaEnterpriseBindEntity entity) {
        if (ObjectUtils.isEmpty(entity)) {
            return null;
        }
        DingEnterprise dingEnterprise = new DingEnterprise();
        // 基础字段映射
        dingEnterprise.setEa(entity.getFsEa());
        dingEnterprise.setEi(eieaConverter.enterpriseAccountToId(entity.getFsEa()));
        dingEnterprise.setAppKey(entity.getAppId());
        dingEnterprise.setDingCorpId(entity.getOutEa());

        final SettingsResult settingBindRules = outerOaConfigInfoManager.getSettingBindRules(entity.getId());

        // 从connectInfo中获取企业绑定相关字段
        String connectInfo = entity.getConnectInfo();
        if (StringUtils.isNotEmpty(connectInfo)) {
            DingConnectorVo info = JSON.parseObject(connectInfo, DingConnectorVo.class);
            // 企业绑定相关字段
            dingEnterprise.setCreateBy(info.getCreateBy());
            dingEnterprise.setUpdateBy(info.getUpdateBy());
            dingEnterprise.setIsCallback(info.getIsCallback());
            dingEnterprise.setDevModel(DingEnterprise.convert2DevModel(settingBindRules));
            dingEnterprise.setAlertStatus(DingEnterprise.convert2AlertStatus(info.isAlertConfig(), info.getAlertTypes()));
            dingEnterprise.setAllIndex(info.getAllIndex());
            dingEnterprise.setAutBind(DingEnterprise.convert2AutBind(settingBindRules));
            dingEnterprise.setTodoType(info.getTodoType());
            dingEnterprise.setTodoCreator(info.getTodoCreator());
            dingEnterprise.setTodoCreatorUnionId(info.getTodoCreatorUnionId());
        }

        // 从OuterOaAppInfoEntity获取应用信息
        OuterOaAppInfoEntity appInfoEntity = outerOaAppInfoMapper.getByAppIdAndChannel(ChannelEnum.dingding, OuterOaAppInfoTypeEnum.selfBuild, entity.getAppId());
        if (appInfoEntity != null) {
            DingtalkAppInfoParams appInfo = JSON.parseObject(appInfoEntity.getAppInfo(), DingtalkAppInfoParams.class);
            // 应用信息相关字段
            dingEnterprise.setAppSecret(appInfo.getAppSecret());
            dingEnterprise.setToken(appInfo.getToken());
            dingEnterprise.setRedirectAppId(appInfo.getRedirectAppId());
            dingEnterprise.setRedirectAppSecret(appInfo.getRedirectAppSecret());
            dingEnterprise.setClientIp(appInfo.getClientIp());
            dingEnterprise.setAgentId(appInfo.getAgentId());
        }

        // 设置时间字段
        dingEnterprise.setCreateTime(new Date(entity.getCreateTime()));
        dingEnterprise.setUpdateTime(new Date(entity.getUpdateTime()));

        return dingEnterprise;
    }

    private OuterOaEnterpriseBindEntity convertToBindEntity(DingEnterprise dingEnterprise) {
        if (ObjectUtils.isEmpty(dingEnterprise)) {
            return null;
        }
        OuterOaEnterpriseBindEntity entity = new OuterOaEnterpriseBindEntity();
        final BindTypeEnum bindTypeEnum = Objects.nonNull(dingEnterprise.getAutBind()) && dingEnterprise.getAutBind() == 1 ? BindTypeEnum.auto : BindTypeEnum.manual;
        entity.setBindType(bindTypeEnum);
        entity.setBindStatus(BindStatusEnum.normal);

        // 基础字段映射
        entity.setChannel(ChannelEnum.dingding);
        entity.setFsEa(StringUtils.isNotEmpty(dingEnterprise.getEa()) ? dingEnterprise.getEa() : eieaConverter.enterpriseIdToAccount(dingEnterprise.getEi()));
        entity.setOutEa(dingEnterprise.getDingCorpId());
        entity.setAppId(dingEnterprise.getAppKey());

        // 将企业绑定相关字段存储在connectInfo中
        final DingConnectorVo connectInfo = DingEnterprise.convert2ConnectInfo(dingEnterprise, i18NStringManager);

        entity.setConnectInfo(JSON.toJSONString(connectInfo));

        // 设置时间字段
        entity.setCreateTime(Objects.nonNull(dingEnterprise.getCreateTime()) ? dingEnterprise.getCreateTime().getTime() : System.currentTimeMillis());
        entity.setUpdateTime(Objects.nonNull(dingEnterprise.getUpdateTime()) ? dingEnterprise.getUpdateTime().getTime() : System.currentTimeMillis());

        return entity;
    }

    public Result<List<DingEnterprise>> findAllDingEnterprise() {
        List<OuterOaEnterpriseBindEntity> entities = outerOaEnterpriseBindManager.getAllByChannel(ChannelEnum.dingding);
        final List<DingEnterprise> enterpriseList = convertToDingEnterpriseAndDecrypt(entities);
        return Result.newSuccess(enterpriseList);
    }

    /**
     * 获取所有企业绑定信息，不解密
     * @return
     */
    public Result<List<DingEnterpriseResult>> getAllEnterpriseBindListNotDecrypt() {
        List<DingEnterpriseResult> totalList = new ArrayList<>();
        List<OuterOaEnterpriseBindEntity> entities = outerOaEnterpriseBindManager.getAllByChannel(ChannelEnum.dingding);
        if (!CollectionUtils.isEmpty(entities)) {
            for (OuterOaEnterpriseBindEntity entity : entities) {
                DingEnterprise dingEnterprise = convertToDingEnterprise(entity);
                DingEnterpriseResult dingEnterpriseResult = BeanUtil.copy(dingEnterprise, DingEnterpriseResult.class);
                totalList.add(dingEnterpriseResult);
            }
        }
        return Result.newSuccess(totalList);
    }

    public Result<Integer> deleteEnterpriseMappIng(String fsEa, String appId) {
        final Integer i = outerOaEnterpriseBindManager.deleteByBusinessId(fsEa, appId, ChannelEnum.dingding);

        return Result.newSuccess(i);
    }

    @Cached(name = "queryAllEnterpriseResult",expire = 60,key = "#ea",cacheType = CacheType.LOCAL)
    public Result<List<DingEnterpriseResult>> queryAllEnterpriseResult(String ea) {
        final List<OuterOaEnterpriseBindEntity> entitiesByFsEa = outerOaEnterpriseBindManager.getNormalEntitiesByFsEa(ChannelEnum.dingding, ea);
        return Result.newSuccess(entitiesByFsEa.stream()
                .filter(entity -> {
                    final DingConnectorVo connector = JSON.parseObject(entity.getConnectInfo(), DingConnectorVo.class);
                    return connector.getAppType() == OuterOaAppInfoTypeEnum.selfBuild;
                })
                .map(this::convertToDingEnterprise)
                .map(dingEnterprise -> BeanUtil.copy(dingEnterprise, DingEnterpriseResult.class))
                .collect(Collectors.toList()));
    }

    public Result<List<OuterOaEnterpriseBindEntity>> queryAllEnterpriseBind(Integer ei) {
        return queryAllEnterpriseBind(eieaConverter.enterpriseIdToAccount(ei));
    }

    public Result<List<OuterOaEnterpriseBindEntity>> queryAllEnterpriseBind(String ea) {
        final List<OuterOaEnterpriseBindEntity> normalEntitiesByFsEa = outerOaEnterpriseBindManager.getNormalEntitiesByFsEa(ChannelEnum.dingding, ea);
        final List<OuterOaEnterpriseBindEntity> collect = normalEntitiesByFsEa.stream()
                .filter(entity -> {
                    final DingConnectorVo connector = JSON.parseObject(entity.getConnectInfo(), DingConnectorVo.class);
                    return connector.getAppType() == OuterOaAppInfoTypeEnum.selfBuild;
                }).collect(Collectors.toList());
        return Result.newSuccess(collect);
    }
}
