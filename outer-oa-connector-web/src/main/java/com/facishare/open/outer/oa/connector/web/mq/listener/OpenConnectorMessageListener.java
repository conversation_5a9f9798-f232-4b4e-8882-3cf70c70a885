package com.facishare.open.outer.oa.connector.web.mq.listener;

import com.alibaba.fastjson.JSONObject;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.open.order.contacts.proxy.api.utils.TraceUtils;
import com.facishare.open.outer.oa.connector.web.manager.OpenConnectorMessageHandler;
import com.fxiaoke.message.extrnal.platform.model.ExternalEventTag;
import com.fxiaoke.message.extrnal.platform.model.arg.*;
import org.apache.rocketmq.client.consumer.listener.ConsumeOrderlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeOrderlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerOrderly;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;


/**
 * 监听crm消息事件
 * <AUTHOR>
 */
@Service("openConnectorMessageListener")
public class OpenConnectorMessageListener implements MessageListenerOrderly {
    @Resource
    private OpenConnectorMessageHandler openConnectorMessageHandler;

    @Override
    public ConsumeOrderlyStatus consumeMessage(List<MessageExt> list, ConsumeOrderlyContext consumeOrderlyContext) {
        for (MessageExt msg : list) {
            TraceUtils.initTraceId(msg.getMsgId() + "_" + msg.getQueueId());
            try {
                String receiveTags = msg.getTags();
                switch (receiveTags) {
                    case ExternalEventTag.CreateTodo:
                        CreateTodoArg createTodoArg = JSONObject.parseObject(msg.getBody(), CreateTodoArg.class);
                        LogUtils.info("openConnectorMessageListener.consumeMessage.createTodoArg={}", createTodoArg);
                        openConnectorMessageHandler.dealCreateTodoHandler(createTodoArg);
                        break;
                    case ExternalEventTag.DealTodo:
                        DealTodoArg dealTodoArg
                                = JSONObject.parseObject(msg.getBody(), DealTodoArg.class);
                        LogUtils.info("openConnectorMessageListener.consumeMessage.dealTodoArg={}", dealTodoArg);
                        openConnectorMessageHandler.dealDealTodoHandler(dealTodoArg);
                        break;
                    case ExternalEventTag.DeleteTodo:
                        DeleteTodoArg deleteTodoArg
                                = JSONObject.parseObject(msg.getBody(), DeleteTodoArg.class);
                        LogUtils.info("openConnectorMessageListener.consumeMessage.deleteTodoArg={}", deleteTodoArg);
                        openConnectorMessageHandler.dealDeleteTodoHandler(deleteTodoArg);
                        break;
                    case ExternalEventTag.TextMsg:
                        SendTextMessageArg sendTextMessageArg
                                = JSONObject.parseObject(msg.getBody(), SendTextMessageArg.class);
                        LogUtils.info("openConnectorMessageListener.consumeMessage.sendTextMessageArg={}", sendTextMessageArg);
                        openConnectorMessageHandler.dealSendTextMessageHandler(sendTextMessageArg);
                        break;
                    case ExternalEventTag.CardMsg:
                        SendTextCardMessageArg sendTextCardMessageArg
                                = JSONObject.parseObject(msg.getBody(), SendTextCardMessageArg.class);
                        LogUtils.info("openConnectorMessageListener.consumeMessage.sendTextCardMessageArg={}", sendTextCardMessageArg);
                        openConnectorMessageHandler.dealSendTextCardMessageHandler(sendTextCardMessageArg);
                        break;
                    default:
                        break;
                }
            } catch (Exception e) {
                LogUtils.error("openConnectorMessageListener.consumeMessage consume failed.", e);
            }
        }
        return ConsumeOrderlyStatus.SUCCESS;
    }
}
