package com.facishare.open.ding.provider.utils;

import com.facishare.open.ding.provider.model.result.SyncResult;
import lombok.Data;

/**
 * @<NAME_EMAIL>
 * @ClassName: EmptyException
 * @Description: EmptyException
 * @datetime 2019/3/8 19:19
 * @Version 1.0
 */
@Data
public class EmptyException extends RuntimeException{

    private SyncResult syncResult;

    public EmptyException(){}

    public EmptyException(SyncResult syncResult) {
        this.syncResult = syncResult;
    }

    @Override
    public String getMessage() {
        return syncResult.getMessage();
    }
}
