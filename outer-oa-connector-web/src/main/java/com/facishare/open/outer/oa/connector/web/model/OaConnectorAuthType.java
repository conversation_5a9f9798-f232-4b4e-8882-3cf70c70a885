package com.facishare.open.outer.oa.connector.web.model;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/5/27
 */
@AllArgsConstructor
@Getter
public enum OaConnectorAuthType {
    API_KEY("API Key"),

    SESSION_AUTH("Session Auth"),

    BASIC_AUTH("Basic Auth"),

    OAUTH2("OAuth 2.0 (Authorization Code)"),

    OAUTH2_CLIENT("OAuth 2.0 (Client Credentials)"),

    BEARER_TOKEN("Bearer Token"),

    /**
     * 自定义授权
     */
    CUSTOM_AUTH("Custom Auth"),

    OAUTH1("OAuth 1.0"),
    ;
    /**
     * 名称
     */
    private final String label;


    public static List<SelectOption> toSelectOptions(Collection<OaConnectorAuthType> authTypes){
        if (authTypes==null){
            return new ArrayList<>();
        }
        List<SelectOption> result = authTypes.stream().map(v -> SelectOption.of(v.name(), v.getLabel())).collect(Collectors.toList());
        return result;
    }
}
