package com.facishare.open.outer.oa.connector.web.result;

import com.facishare.open.outer.oa.connector.common.api.params.CepArg;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 系统字段
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SystemFieldResult extends CepArg implements Serializable {
      /**
       * 字段apiname
       */
      private String fieldApiName;
      /**
       * 字段标签
       */
      private String fieldLabel;

      /**
       * 字段类型
       */
      private String fieldType;

}
