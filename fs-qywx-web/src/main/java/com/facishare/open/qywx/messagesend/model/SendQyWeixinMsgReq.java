package com.facishare.open.qywx.messagesend.model;

import com.facishare.open.qywx.messagesend.enums.QyWeixinMsgType;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created by fengyh on 2018/7/23.
 */
@Data
public class SendQyWeixinMsgReq<T> implements Serializable  {
    /**
     * 以下面的企业微信文档中给出的图文消息为例，分解到下面的字段中。
     *
     * {
     "touser" : "UserID1|UserID2|UserID3",
     "msgtype" : "news",
     "agentid" : 1,
     "news" : {
     "articles" : [
     {
     "title" : "中秋节礼品领取",
     "description" : "今年中秋节公司有豪礼相送",
     "url" : "URL",
     "picurl" : "http://res.mail.qq.com/node/ww/wwopenmng/images/independent/doc/test_pic_msg1.png",
     "btntxt":"更多"
     }
     ]
     }
     }
     * */
    private static final long serialVersionUID = 5107622286818611284L;
    private QyWeixinMsgType type; // "news"
    private List<String> toUserList; //这里传纷享内部员工账号列表，因为发送消息的业务不知道外部账号
    private String fsEnterpriseAccount; //纷享平台上的企业账号
    private String appId; //我们的应用在企业微信平台上的appid
    /**
     "news" : {
     "articles" : [
     {
     "title" : "中秋节礼品领取",
     "description" : "今年中秋节公司有豪礼相送",
     "url" : "URL",
     "picurl" : "http://res.mail.qq.com/node/ww/wwopenmng/images/independent/doc/test_pic_msg1.png",
     "btntxt":"更多"
     }
     ]
     }
     * */
    private T msgContent;

    private String sourceMsg; //crm推送的原始数据
}
