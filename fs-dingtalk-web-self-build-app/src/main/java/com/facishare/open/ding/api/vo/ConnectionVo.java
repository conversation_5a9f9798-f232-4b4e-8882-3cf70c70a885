package com.facishare.open.ding.api.vo;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Pattern;
import java.io.Serializable;

/**
 * <p>连接cloud的参数</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2018/7/17 10:03
 */
@Data
public class ConnectionVo implements Serializable {
    /** 纷享ea **/
    private String ea;

    /** 纷享ei **/
    private Integer ei;

    /** 纷享企业名称 **/
    private String enterpriseName;

    /** 纷享员工Id **/
    private Integer employeeId;

    /** 钉钉corpId **/
    private String dingCorpId;

    private String agentId;


    /** 应用ID **/
    @NotEmpty(message ="应用appKey不能为空")
    private String appKey;

    /** 应用secret **/
    @NotEmpty(message ="应用appKey不能为空")
    @Pattern(regexp="^[^\\*]+$",message = "appSecret的*号信息需要重新填写正确的")
    private String appSecret;

    /** 免登录应用ID **/
    @NotEmpty(message ="移动应用appKey不能为空")
    private String redirectAppId;

    /** 免登录应用secret **/
    @NotEmpty(message ="移动应用appSecret不能为空")
    @Pattern(regexp="^[^\\*]+$",message = "移动应用的appSecret的*号信息需要重新填写正确的")
    private String redirectAppSecret;

    //@NotEmpty(message = "clientIp不能为空")
    private String clientIp;

    //1：模式是只同步员工，2：是单向维护通讯录
    private Integer devModel;

    // 0 状态标识关闭  1：标识开启
    private Integer alertStatus;
    /**
     * 待办推送的位置  0：工作通知 1：钉钉待办 2：都推送
     */
    private Integer todoType;
    /**
     * 待办创建者ID，可以使用虚拟员工
     */
    private String todoCreator;
    /**
     * 待办创建者ID，可以使用虚拟员工
     */
    private String todoCreatorUnionId;
}
