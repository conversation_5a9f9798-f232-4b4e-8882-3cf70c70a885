package com.facishare.open.ding.provider.mongodb;

import com.facishare.open.ding.provider.mongodb.common.MyBasicMongodbDao;
import com.facishare.open.ding.provider.mongodb.entity.RateControlPolicyDo;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.mongodb.morphia.Key;
import org.mongodb.morphia.query.Query;
import org.mongodb.morphia.query.UpdateOperations;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>类的详细说明</p>
 * @dateTime 2018/7/11 11:53
 * @<NAME_EMAIL>
 * @version 1.0 
 */
@Repository
public class RateControlPolicyDao extends MyBasicMongodbDao<RateControlPolicyDo> {

    @Override
    protected Class<RateControlPolicyDo> getEntityClazz() {
        return RateControlPolicyDo.class;
    }

    public Key<RateControlPolicyDo> saveRateControlPolicyDo(RateControlPolicyDo rateControlPolicyDo) {
        return this.mongoDBTemplate.save(rateControlPolicyDo);
    }

    public boolean updateRateControlPolicyDo(RateControlPolicyDo rateControlPolicyDo) {
        Query<RateControlPolicyDo> query = createQuery();
        query.field("_id").equal(rateControlPolicyDo.getId());
        
        UpdateOperations<RateControlPolicyDo> update = createUpdateOperations();
        update.set("policyBusiness", rateControlPolicyDo.getPolicyBusiness());
        update.set("policySerial", rateControlPolicyDo.getPolicySerial());
        update.set("status", rateControlPolicyDo.isStatus());
        update.set("paramList", rateControlPolicyDo.getParamList() == null ? Lists.newArrayList() : rateControlPolicyDo.getParamList());
//        update.set("blackList", rateControlPolicyDo.getBlackList() == null ? Lists.newArrayList() : rateControlPolicyDo.getBlackList());
//        update.set("whiteList", rateControlPolicyDo.getWhiteList() == null ? Lists.newArrayList() : rateControlPolicyDo.getWhiteList());
//        update.set("vipPolicyList", rateControlPolicyDo.getVipPolicyList() == null ? Lists.newArrayList() : rateControlPolicyDo.getVipPolicyList());
//        update.set("controlList", rateControlPolicyDo.getControlList() == null ? Lists.newArrayList() : rateControlPolicyDo.getControlList());

        return this.mongoDBTemplate.update(query, update, false).getUpdatedExisting();
    }

    public RateControlPolicyDo findById(ObjectId objId) {
        Query<RateControlPolicyDo> query = createQuery();
        query.field("_id").equal(objId);
        return this.mongoDBTemplate.findOne(query);
    }

    public RateControlPolicyDo findByPolicySerial(String policySerial) {
        Query<RateControlPolicyDo> query = createQuery();
        query.field("policySerial").equal(policySerial);
        return this.mongoDBTemplate.findOne(query);
    }

    public List<RateControlPolicyDo> findByCondition(String policySerial, String policyBusiness) {
        Query<RateControlPolicyDo> query = createQuery();
        if (StringUtils.isNotBlank(policySerial)) {
            query.field("policySerial").equal(policySerial);
        }
        if (StringUtils.isNotBlank(policyBusiness)) {
            query.field("policyBusiness").equal(policyBusiness);
        }
        
        return this.mongoDBTemplate.find(query).asList();
    }

}
