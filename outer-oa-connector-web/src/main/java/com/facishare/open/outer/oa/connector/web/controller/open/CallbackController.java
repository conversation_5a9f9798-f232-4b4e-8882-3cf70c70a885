package com.facishare.open.outer.oa.connector.web.controller.open;

import com.facishare.open.outer.oa.connector.common.api.result.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * 开放连接器外部接口
 *
 * <AUTHOR>
 * @date 2025-05-29
 */
@Controller
@RequestMapping("erpdss/open/oa/noAuth")
@Slf4j
public class CallbackController {


    @GetMapping("/standardConnector/callback")
    @ResponseBody
    public String home() {
        return "<!DOCTYPE html>\n" +
                "<html>\n" +
                "  <body>\n" +
                "    <script>\n" +
                "      var queryParams = (location.search || \"\").split(\"?\")[1];\n" +
                "      window.opener.postMessage({ queryParams }, \"*\");\n" +
                "      window.close();\n" +
                "    </script>\n" +
                "  </body>\n" +
                "</html>";
    }

}
