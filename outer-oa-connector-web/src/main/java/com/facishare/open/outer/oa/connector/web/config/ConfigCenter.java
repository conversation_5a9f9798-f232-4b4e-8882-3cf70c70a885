package com.facishare.open.outer.oa.connector.web.config;


import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;

import java.util.Set;

@Slf4j
public class ConfigCenter {

    public static String SSO_REDIRECT_URL;
    public static String crm_domain;
    /**
     * 接收告警企业
     */
    public static String NOTIFICATION_EA = "fszdbd2575";
    /**
     * erp数据同步appId，这里共用一下
     */
    public static String ERP_SYNC_DATA_APP_ID = "FSAID_9897f5";
    /**
     * 消息体最长数量
     */
    public static Integer NOTICE_MAX_SIZE = 500;
    /**
     * 文件预览路径，%s为带ext的npath
     */
    public static String PREVIEW_FILE_PATH = "https://www.ceshi112.com/FSC/EM/File/GetByPath?path=%s";
    /**
     * 接收告警人
     */
    public static Set<String> NOTIFICATION_MEMBERS = Sets.newHashSet();
    static {
        ConfigFactory.getInstance().getConfig("fs-feishu-config", config -> {
            SSO_REDIRECT_URL=config.get("SSO_REDIRECT_URL","/FHH/EM0HXUL/SSO/Login?token=%s");
            crm_domain=config.get("crm_domain","https://www.ceshi112.com");
            NOTIFICATION_EA = config.get("NOTIFICATION_EA", NOTIFICATION_EA);
            ERP_SYNC_DATA_APP_ID = config.get("ERP_SYNC_DATA_APP_ID", ERP_SYNC_DATA_APP_ID);
            PREVIEW_FILE_PATH = config.get("PREVIEW_FILE_PATH", PREVIEW_FILE_PATH);
        });
    }
}
