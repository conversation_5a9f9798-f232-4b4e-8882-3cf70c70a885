package com.facishare.open.order.contacts.proxy.api.service;

import com.facishare.open.order.contacts.proxy.api.arg.FsEmpArg;
import com.facishare.open.order.contacts.proxy.api.data.EmpRoleData;
import com.facishare.open.order.contacts.proxy.api.result.Result;
import com.facishare.organization.adapter.api.permission.model.BatchGetRoleCodesByEmployeeIds;
import com.facishare.organization.api.model.employee.EmployeeDto;
import com.facishare.organization.api.model.employee.result.FindEmployeeDtoByFullNameResult;
import com.facishare.organization.api.model.employee.result.GetEmployeesDtoByNameResult;
import com.facishare.privilege.api.module.user.UserRoleVo;
import com.fxiaoke.crmrestapi.common.data.ObjectData;

import java.util.List;

/**
 * 纷享员工服务代理
 * <AUTHOR>
 * @date 20220731
 */
public interface FsEmployeeServiceProxy {
    /**
     * 创建员工
     * @param arg
     * @return
     */
    Result<ObjectData> create(FsEmpArg arg,List<String> roleCodeList, String mainRoleCode);

    /**
     * 更新员工
     * @param arg
     * @return
     */
    Result<Void> update(FsEmpArg arg,List<String> roleCodeList, String mainRoleCode);

    /**
     * 员工查询接口
     * @param ei
     * @param empId
     * @return
     */
    Result<List<ObjectData>> list(String ei,String empId);

    /**
     * 详情接口
     * @param ei
     * @param empId
     * @return
     */
    Result<ObjectData> detail(String ei,String empId);

    /**
     * 获取员工角色列表
     * @param ei
     * @param empId
     * @return
     */
    Result<List<EmpRoleData>> getRoleListByUserId(String ei, String empId);

    /**
     * 判断员工是否拥有指定的角色
     * @param empId
     * @param roleCodeList
     * @return
     */
    Result<Boolean> hasRoleCode(String ei, String empId, List<String> roleCodeList);

    /**
     * 批量停用员工
     * @param ei
     * @param empIdList
     * @return
     */
    Result<Void> bulkStop(String ei,List<String> empIdList);

    /**
     * 批量启用员工
     * @param ei
     * @param empIdList
     * @return
     */
    Result<Void> bulkResume(String ei,List<String> empIdList,List<String> roleCodeList, String mainRoleCode);

    /**
     * 批量重置员工主属部门
     * @param ei
     * @param empIdList
     * @param mainDepId
     * @return
     */
    Result<Void> bulkResetMainDepartment(String ei,List<String> empIdList,String mainDepId);

    /**
     * 启用或停用员工
     */
    Result<Void> toggle(String ei,String id,boolean enable, List<String> roleCodeList, String mainRoleCode);

    /**
     * 获取当前部门下所有员工，包括子部门下的员工，不包括停用的员工，因为停用的员工，主部门会变为 待分配
     * @param ei
     * @return
     */
    Result<List<ObjectData>> listAll(Integer ei, String depId);

    /**
     * 获取当前部门下所有员工，不包括子部门下的员工
     * @param ei
     * @return
     */
    Result<List<ObjectData>> listByDepId(Integer ei, String depId);

    /**
     * 批量添加人员角色
     * @param ei
     * @param empIds
     * @param roleCodeList
     * @param mainRoleCode
     * @return
     */
    Result<Void> batchAddUserRole(Integer ei, List<String> empIds,List<String> roleCodeList, String mainRoleCode);

    /**
     * 批量移除人员角色
     * @param ei
     * @param empIds
     * @param roleCode
     * @return
     */
    Result<Void> batchDeleteUserRole(Integer ei, List<String> empIds, String roleCode);

    /**
     * 添加管理员角色
     * @param ei
     * @param empIdList
     * @return
     */
    Result<Void> addManagerRole(Integer ei, List<Integer> empIdList);

    Result<List<EmployeeDto>> batchGetEmployeeDto(int ei, List<Integer> userList);

    Result<List<EmployeeDto>> batchGetAllEmployeeDto(int ei, List<Integer> removedUserIdList);

    Result<FindEmployeeDtoByFullNameResult> getEmployeesByFullName(Integer ei, String name);

    Result<GetEmployeesDtoByNameResult> getEmployeesByName(Integer ei, String name);

    Result<List<UserRoleVo>> batchGetUserRoles(Integer ei, List<String> userIds);

    Result<BatchGetRoleCodesByEmployeeIds.Result> batchGetEmployeeRoleCodes(Integer ei, List<Integer> employeeIds);

    Result<List<EmployeeDto>> getAllEmployees(Integer ei);

    Result<EmployeeDto> getEmployeesDtoByEnterpriseAndMobile(Integer ei, String mobile);
}
