package com.facishare.open.huawei.kit.web.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.facishare.open.outer.oa.connector.common.api.enums.BindStatusEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.BindTypeEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("tb_enterprise_bind")
public class EnterpriseBindEntity implements Serializable {
    @TableId(type = IdType.AUTO)
    private Integer id;
    /**
     * 来源
     */
    private ChannelEnum channel;
    /**
     * 纷享EA
     */
    private String fsEa;
    /**
     * 目标企业EA
     */
    private String outEa;
    /**
     * 目标企业appKey
     */
    private String appKey;
    /**
     * 目标企业accessKey
     */
    private String accessKey;
    /**
     * 目标企业accessSecret
     */
    private String accessSecret;
    /**
     * 牛信云appKey
     */
    private String nxAppKey;
    /**
     * 牛信云secretKey
     */
    private String nxSecretKey;
    /**
     * 绑定类型
     */
    private BindTypeEnum bindType;
    /**
     * 绑定状态
     */
    private BindStatusEnum bindStatus;
    /**
     * 扩展字段
     * isFirstTrial:留资使用的字段，判断是否是第一次进入crm
     */
    private String extend;
    /**
     * 自动绑定账号 1：关闭, 2：开启
     */
    private Integer autBind;
    /**
     * 企业域名
     */
    private String domain;

    private Date createTime;
    private Date updateTime;

    /**
     * 统一基座增加的字段
     */
    private String connectParams;

    private String appId;
}
