package com.facishare.open.huawei.kit.web.controller.outer;

import com.facishare.open.erpdss.outer.oa.connector.base.context.TemplateResult;
import com.facishare.open.huawei.kit.web.arg.MigrateEnterpriseArg;
import com.facishare.open.huawei.kit.web.handler.HuaweiEventHandlerManager;
import com.facishare.open.huawei.kit.web.result.Result;
import com.facishare.open.huawei.kit.web.service.ServiceAuthService;
import com.facishare.open.huawei.kit.web.service.ToolsService;
import com.facishare.open.huawei.kit.web.templateData.KitVerifyTemplateData;
import com.facishare.open.order.contacts.proxy.api.arg.CreateCrmOrderArg;
import com.facishare.open.order.contacts.proxy.api.arg.CreateCustomerArg;
import com.facishare.open.order.contacts.proxy.api.service.FsOrderServiceProxy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Slf4j
@RestController
@RequestMapping(value="/huaweikit2/tools")
public class HuaweiToolsController {
    @Resource
    private HuaweiEventHandlerManager huaweiEventHandlerManager;

    @Resource
    private ServiceAuthService serviceAuthService;
    @Resource
    private ToolsService toolsService;

    @Autowired
    private FsOrderServiceProxy fsOrderServiceProxy;


    @PostMapping(value = "reSyncEvent")
    @ResponseBody
    public Result reSyncEvent(@RequestBody KitVerifyTemplateData kitVerifyTemplateData) {
        TemplateResult templateResult = huaweiEventHandlerManager.handle(kitVerifyTemplateData.getType(), kitVerifyTemplateData);
        return (Result) templateResult.getData();
    }

    @RequestMapping(value = "/updateData",method =RequestMethod.POST)
    @ResponseBody
    public com.facishare.open.huawei.kit.web.result.result.Result<Integer> updateData(@RequestBody String data) {
        return serviceAuthService.updateData(new String(Base64.decodeBase64(data.getBytes())));
    }

    @RequestMapping(value = "/refreshOrderAndCustomer",method =RequestMethod.GET)
    @ResponseBody
    public com.facishare.open.huawei.kit.web.result.result.Result<Void> refreshOrderAndCustomer(@RequestParam("instanceId") String instanceId, @RequestParam("orderId") String orderId) {
        return toolsService.refreshOrderAndCustomer(instanceId, orderId);
    }

    @RequestMapping(value = "/createCrmCustomer2",method =RequestMethod.POST)
    @ResponseBody
    public Result createCrmCustomer2(@RequestBody CreateCustomerArg arg) {
        com.facishare.open.order.contacts.proxy.api.result.Result<String> result = fsOrderServiceProxy.createCustomer2(arg);
        return Result.newSuccess();
    }

    @RequestMapping(value = "/createCrmOrder2",method =RequestMethod.POST)
    @ResponseBody
    public Result createCrmOrder2(@RequestBody CreateCrmOrderArg arg) {
        com.facishare.open.order.contacts.proxy.api.result.Result<Void> result = fsOrderServiceProxy.createCrmOrder2(arg);
        return Result.newSuccess();
    }

    /**
     * 迁移企业绑定信息
     * @return 结果
     */
    @RequestMapping(value = "/migrateEnterpriseBind", method = RequestMethod.POST)
    @ResponseBody
    public com.facishare.open.huawei.kit.web.result.result.Result<Void> migrateEnterpriseBind(@RequestBody MigrateEnterpriseArg migrateEnterpriseArg) {
        return toolsService.migrateEnterpriseBind(migrateEnterpriseArg.getOutEaList());
    }
}