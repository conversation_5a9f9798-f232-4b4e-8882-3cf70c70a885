package com.facishare.open.outer.oa.connector.web.test.manager;

import com.alibaba.druid.sql.visitor.functions.Bin;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.facishare.open.oa.base.dbproxy.manager.ObjectDataManager;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEmployeeBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEmployeeDataEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEmployeeBindManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEmployeeDataManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEnterpriseBindManager;
import com.facishare.open.oa.base.dbproxy.pg.mapper.OuterOaEmployeeBindMapper;
import com.facishare.open.oa.base.dbproxy.pg.mapper.OuterOaEmployeeDataMapper;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaEmployeeBindParams;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaEmployeeDataParams;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaEnterpriseBindParams;
import com.facishare.open.outer.oa.connector.common.api.admin.BaseConnectorVo;
import com.facishare.open.outer.oa.connector.common.api.enums.BindStatusEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.BindTypeEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.OuterOaAppInfoTypeEnum;
import com.facishare.open.outer.oa.connector.common.api.outerInterface.OuterAbstractSettingService;
import com.facishare.open.outer.oa.connector.common.api.result.OuterOAConnectSettingResult;
import com.facishare.open.outer.oa.connector.common.api.result.Result;
import com.facishare.open.outer.oa.connector.common.api.result.ResultCodeEnum;
import com.facishare.open.outer.oa.connector.common.api.utils.ConnectorVoUtils;
import com.facishare.open.outer.oa.connector.web.manager.LicenseManager;
import com.facishare.open.outer.oa.connector.web.manager.OuterOASettingFactory;
import com.facishare.open.outer.oa.connector.web.test.BaseTest;
import com.fxiaoke.api.IdGenerator;
import com.fxiaoke.crmrestapi.result.IncrementUpdateResult;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static org.junit.Assert.*;

@Slf4j
public class LicenseManagerIntegrationTest extends BaseTest {

    @Autowired
    private LicenseManager licenseManager;
    @Autowired
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;
    @Autowired
    private OuterOaEmployeeBindMapper outerOaEmployeeBindMapper;
    @Autowired
    private OuterOaEmployeeBindManager outerOaEmployeeBindManager;
    @Autowired
    private OuterOaEmployeeDataMapper outerOaEmployeeDataMapper;
    @Autowired
    private OuterOASettingFactory outerOASettingFactory;
    @Autowired
    private ObjectDataManager objectDataManager;

    @Test
    public void testCheckLicense() {
        ChannelEnum channelEnum=ChannelEnum.feishu;
        OuterAbstractSettingService implementation = outerOASettingFactory
                .getImplementation(channelEnum, OuterOaAppInfoTypeEnum.selfBuild);
        Result<Boolean> routeNewPage = implementation.routeNewPage("91450", channelEnum);

        // 使用测试环境的企业账号进行测试
        Result<Void> result = licenseManager.checkLicense(TEST_FS_EA,ChannelEnum.feishu);

        // 验证结果
        assertNotNull("结果不应为空", result);
        // 由于测试环境可能没有有效的license，这里我们验证返回的错误码是否符合预期
        if (!result.isSuccess()) {
            assertEquals("无可用License时应返回LICENSE_NO_AVAILABLE错误码", ResultCodeEnum.LICENSE_NO_AVAILABLE.getCode(),
                    result.getCode());
        }
    }

    @Test
    public void testCheckOpenConnectorLicense() {
        Result<Void> result = licenseManager.checkLicense("91351_sandbox",ChannelEnum.standard_oa);
        System.out.println("");
    }

    @Test
    public void testCheckLicenseWithInvalidEa() {
        String dataCenterId = "67e6862d20c3d0b0e330910d";
        String tenantId = "93384";
        OuterOaEnterpriseBindEntity bindEntity = outerOaEnterpriseBindManager.getEntityById(dataCenterId);

        // OuterOaAppInfoParams appInfoParams = buildAppInfoParams(bindEntity);
        // OuterOaAppInfoEntity appInfoEntity =
        // outerOaAppInfoManager.getEntity(appInfoParams);
        // if (appInfoEntity == null) {
        // log.error("App info not found for channel: {}, outEa: {}",
        // bindEntity.getChannel(),
        // bindEntity.getOutEa());
        // return Result.newError(ResultCodeEnum.APP_ADMIN_NOT_BIND);
        // }
        OuterOAConnectSettingResult result = buildConnectSettingResult(bindEntity);
        log.info("convert result:{}", result);
    }

    private OuterOAConnectSettingResult buildConnectSettingResult(OuterOaEnterpriseBindEntity bindEntity) {
        OuterOAConnectSettingResult result = new OuterOAConnectSettingResult();
        OuterOAConnectSettingResult.ConnectParams connectParams = new OuterOAConnectSettingResult.ConnectParams();
        BaseConnectorVo connectorVo = new BaseConnectorVo();
        try {
            // 1. 获取对应的ConnectorVo类
            Class<? extends BaseConnectorVo> connectorClass = bindEntity.getChannel().getClassName();
            if (connectorClass == null) {
                log.warn("No connector class found for channel: {}, appType: {}", bindEntity.getChannel());
                return result;
            }
            // 2. 将String类型的connectInfo反序列化为对应的ConnectorVo
            connectorVo = JSONObject.parseObject(bindEntity.getConnectInfo(), connectorClass);
            if (connectorVo == null) {
                log.warn("Failed to parse connectInfo for channel: {}, appType: {}", bindEntity.getChannel());
                return result;
            }
            // 3. 将ConnectorVo设置到connectParams中
            ConnectorVoUtils.setConnectorVoToConnectParams(connectParams, connectorVo, bindEntity.getChannel());
        } catch (Exception e) {
            log.error("Error processing connector info: channel={}, appType={}, error={}", bindEntity.getChannel(),
                    e.getMessage(), e);
        }
        // 4. 设置其他必要信息
        result.setConnectParams(connectParams);
        result.setChannelEnum(bindEntity.getChannel());
        result.setOuterOaAppInfoTypeEnum(connectorVo.getAppType());
        return result;
    }


    @Test
    public void testQueryDataJOIN(){
        OuterOaEmployeeBindParams outerOaEmployeeBindParams=OuterOaEmployeeBindParams.builder().outEa("100d08b69448975d")
                .fsEa("91449").outEmpId("ou_98edcf880b9b275a32d96fee73bbb0a1")
                .build();
        List<OuterOaEmployeeBindEntity> entities = outerOaEmployeeBindManager.getEntities(outerOaEmployeeBindParams);
        OuterOaEnterpriseBindEntity entityById = outerOaEnterpriseBindManager.getEntityById("67e6862d20c3d0b0e330910d");
        List<Map<String, Object>> maps1 = outerOaEmployeeBindManager.queryUnboundEmployees(entityById.getChannel(), entityById.getOutEa(), entityById.getFsEa(), entityById.getAppId(), 100, 0);
        List<Map<String, Object>> maps = outerOaEmployeeBindMapper.queryEmployeeBindByDcId("67e6862d20c3d0b0e330910d",null,null);
    }

    @Test
    public void testQueryData(){
        String outEa="100d08b69448975d";
        String outUserId="ou_98edcf880b9b275a32d96fee73bbb0a1";
        String appId="cli_a3ddeb52763b100c";
        OuterOaEnterpriseBindParams outerOaEnterpriseBindParams=OuterOaEnterpriseBindParams.builder().outEa(outEa).appId(appId).id(null).build();
        List<OuterOaEnterpriseBindEntity> entities = outerOaEnterpriseBindManager.getEntities(outerOaEnterpriseBindParams);
        // 使用 Stream API 查找匹配项
        String finalFsEa="91449";
        List<OuterOaEnterpriseBindEntity> normalEnterpriseBindList =entities.stream()
                .filter(v -> v.getBindStatus().equals(BindStatusEnum.normal))
                .collect(Collectors.toList());
        Optional<OuterOaEnterpriseBindEntity> matchingMapping = normalEnterpriseBindList.stream()
                .filter(mapping -> mapping.getFsEa().equals(finalFsEa))
                .findFirst();

        OuterOaEnterpriseBindEntity outerOaEnterpriseBindEntity=OuterOaEnterpriseBindEntity.builder()
                .fsEa("test_0001")
                .createTime(System.currentTimeMillis())
                .updateTime(System.currentTimeMillis())
                .id(IdGenerator.get())
                .bindStatus(BindStatusEnum.create)
                .bindType(BindTypeEnum.auto)
                .channel(ChannelEnum.feishu)
                .build();
        Integer i = outerOaEnterpriseBindManager.batchUpsert(Lists.newArrayList(outerOaEnterpriseBindEntity));
        outerOaEnterpriseBindEntity.setId(IdGenerator.get());
        Integer COUNT = outerOaEnterpriseBindManager.batchUpsert(Lists.newArrayList(outerOaEnterpriseBindEntity));

        OuterOaEmployeeDataParams outerOaEmployeeBindParams=OuterOaEmployeeDataParams.builder().outEa("106114c88acad75d")
                .fsEa("91449").appId("cli_a77e28c84186500e").channel(ChannelEnum.feishu)
//                .text2("陈宗鑫chenzx")
                .build();
        Page<OuterOaEmployeeDataEntity> page = new Page<>(1, 1);
        IPage<OuterOaEmployeeDataEntity> outerOaEmployeeDataEntityIPage = outerOaEmployeeDataMapper.queryUnboundEmployeesByField( page,outerOaEmployeeBindParams);
        System.out.printf("outerOaEmployeeDataEntityIPage");
    }

    @Test
    public  void testUpdate(){
        OuterOaEnterpriseBindEntity entityById = outerOaEnterpriseBindManager.getEntityById("6807834eb313f03d2f995aa1");
        Result<IncrementUpdateResult> incrementUpdateResultResult = objectDataManager.updateEmpData(entityById, "163809405336384802");

    }


}