package com.facishare.open.outer.oa.connector.web.manager;

import com.alibaba.fastjson.JSON;
import com.facishare.open.oa.base.dbproxy.manager.ObjectDataManager;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaConfigInfoEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaDeptDataEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEmployeeBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaConfigInfoManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaDeptDataManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEmployeeBindManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEmployeeDataManager;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaEmployeeBindParams;
import com.facishare.open.order.contacts.proxy.api.consts.GlobalValue;
import com.facishare.open.order.contacts.proxy.api.service.FsEmployeeServiceProxy;
import com.facishare.open.outer.oa.connector.common.api.enums.*;
import com.facishare.open.outer.oa.connector.common.api.info.SettingAccountRulesModel;
import com.facishare.open.outer.oa.connector.common.api.object.QywxEmployeeObject;
import com.facishare.open.outer.oa.connector.common.api.object.StandardDepartmentObject;
import com.facishare.open.outer.oa.connector.common.api.object.StandardEmployeeObject;
import com.facishare.open.outer.oa.connector.common.api.result.Result;
import com.facishare.open.outer.oa.connector.common.api.result.ResultCodeEnum;
import com.facishare.open.outer.oa.connector.common.api.result.SystemFieldMappingResult;
import com.facishare.open.outer.oa.connector.web.model.admin.OuterCallBackEventMsg;
import com.facishare.open.outer.oa.connector.web.openconnetor.OpenConnectorManager;
import com.facishare.open.outer.oa.connector.web.openconnetor.model.*;
import com.facishare.rest.core.util.JacksonUtil;
import com.fxiaoke.api.IdGenerator;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.result.ActionAddResult;
import com.fxiaoke.crmrestapi.result.IncrementUpdateResult;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class OpenConnectorDataManager {
    @Autowired
    private OpenConnectorManager openConnectorManager;
    @Autowired
    private OuterOaEmployeeDataManager outerOaEmployeeDataManager;
    @Autowired
    private OuterOaDeptDataManager outerOaDeptDataManager;
    @Resource
    private OuterOaConfigInfoManager outerOaConfigInfoManager;
    @Resource
    private OuterOaEmployeeBindManager outerOaEmployeeBindManager;
    @Resource
    private FsEmployeeServiceProxy fsEmployeeServiceProxy;
    @Autowired
    private ObjectDataManager objectDataManager;

    public Result<Void> refreshOuterEmpData(OuterOaEnterpriseBindEntity bind, Integer tenantId, Boolean needSync) {
        String dataCenterId = bind.getId();
        String connectorApiName = bind.getConnectParams().getStandard_oa().getApiName();
        String outEa = bind.getOutEa();
        String appId = bind.getAppId();
        Set<String> empIds = Sets.newHashSet();
        Long now = System.currentTimeMillis();
        Integer i = 0, offset = 0, limit = 200;
        TimeFilterArg arg = new TimeFilterArg();
        arg.setStartTime(0L);
        arg.setEndTime(now);
        arg.setLimit(limit);
        arg.setObjAPIName(OpenConnectorObjApiName.employee);
        while (i < 200) {//最多两百页
            arg.setOffset(offset);
            Result<StdListData> result = openConnectorManager.queryOaMasterBatch(tenantId.toString(), dataCenterId, connectorApiName, arg);
            if (!result.isSuccess()) {
                return Result.copy(result);
            }
            if (result.getData().getDataList() == null) {
                break;
            }
            StdListData stdListData = result.getData();
            List<StdData> dataList = stdListData.getDataList();
            List<StandardEmployeeObject> employeeData = dataList.stream().filter(v -> v.getMasterFieldVal() != null).map(v -> {
                StandardEmployeeObject object = OuterObjectData.change2EmployeeObject(v.getMasterFieldVal());
                return object;
            }).collect(Collectors.toList());
            empIds.addAll(employeeData.stream().map(StandardEmployeeObject::getUserid).collect(Collectors.toList()));
            batchUpsertEmp(dataCenterId, employeeData);
            if (needSync) {
                batchSyncEmployee(tenantId, bind, employeeData);
            }
            if (result.getData().getDataList().size() < limit) {
                break;
            }
            if (stdListData.getMaxTime() != null) {
                arg.setLastMaxId(stdListData.getMaxId());
            }
            if (stdListData.getMaxId() != null) {
                arg.setStartTime(stdListData.getMaxTime());
            }
            offset += limit;
            i++;
        }
        outerOaEmployeeDataManager.deleteInvisibleUsers(outEa, ChannelEnum.standard_oa, appId, empIds);
        return Result.newSuccess();
    }

    private Result<Void> batchUpsertEmp(String dataCenterId, List<StandardEmployeeObject> employeeData) {
        int size = outerOaEmployeeDataManager.batchUpsert(employeeData, ChannelEnum.standard_oa, dataCenterId);
        return Result.newSuccess();
    }

    private OuterOaDeptDataEntity convert2OuterOaDeptDataEntity(StandardDepartmentObject outDeptData, String outEa, String appId) {
        if (outDeptData == null) {
            return null;
        }

        OuterOaDeptDataEntity entity = new OuterOaDeptDataEntity();
        entity.setOutEa(outEa);
        entity.setChannel(ChannelEnum.standard_oa);
        entity.setAppId(appId);
        entity.setOutDeptId(String.valueOf(outDeptData.getDeptId()));
        entity.setDeptName(outDeptData.getName());
        entity.setParentDeptId(outDeptData.getParentId() != null ? outDeptData.getParentId() : null);
        entity.setDeptOrder(outDeptData.getSeq() != null ? Long.valueOf(outDeptData.getSeq()) : 0L);
        entity.setOutDeptInfo(JSON.parseObject(JSON.toJSONString(outDeptData)));
        entity.setCreateTime(System.currentTimeMillis());
        entity.setUpdateTime(System.currentTimeMillis());

        return entity;
    }

    public Result<Void> dealWithCallBackEvent(String tenantId, OuterOaEnterpriseBindEntity bindEntity, OuterCallBackEventMsg callBackEventMsg) {
        String type = callBackEventMsg.getType();
        switch (type) {
            case "initEmployee":
                return initEmployee(tenantId, bindEntity, callBackEventMsg);
            case "upsertEmployee":
                return upsertEmployee(tenantId, bindEntity, callBackEventMsg);
            case "removeEmployee":
                return removeEmployee(tenantId, bindEntity, callBackEventMsg);
            case "stopEmployee":
                return stopEmployee(tenantId, bindEntity, callBackEventMsg);
            case "initDepartment":
                return initDepartment(tenantId, bindEntity, callBackEventMsg);
            case "upsertDepartment":
                return upsertDepartment(tenantId, bindEntity, callBackEventMsg);
            case "removeDepartment":
                return removeDepartment(tenantId, bindEntity, callBackEventMsg);
            case "stopDepartment":
                return stopDepartment(tenantId, bindEntity, callBackEventMsg);
            case "upsertConfig":
                return upsertConfig(tenantId, bindEntity, callBackEventMsg);
            case "deleteConfig":
                return deleteConfig(tenantId, bindEntity, callBackEventMsg);
            default:
                return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }
    }

    private Result<Void> stopDepartment(String tenantId, OuterOaEnterpriseBindEntity bindEntity, OuterCallBackEventMsg callBackEventMsg) {
        return Result.newSuccess();
    }

    private Result<Void> removeDepartment(String tenantId, OuterOaEnterpriseBindEntity bindEntity, OuterCallBackEventMsg callBackEventMsg) {
        return Result.newSuccess();
    }

    private Result<Void> upsertDepartment(String tenantId, OuterOaEnterpriseBindEntity bindEntity, OuterCallBackEventMsg callBackEventMsg) {
        return Result.newSuccess();
    }

    private Result<Void> initDepartment(String tenantId, OuterOaEnterpriseBindEntity bindEntity, OuterCallBackEventMsg callBackEventMsg) {
        return Result.newSuccess();
    }
    public Result<Void> refreshOuterDepartmentData(String dataCenterId, String connectorApiName, String outEa, String appId, Integer tenantId) {
//        Set<String> departIds = Sets.newHashSet();
//        Long now = System.currentTimeMillis();
//        Integer i = 0, offset = 0, limit = 200;
//        TimeFilterArg arg = new TimeFilterArg();
//        arg.setStartTime(0L);
//        arg.setEndTime(now);
//        arg.setLimit(limit);
//        arg.setObjAPIName(OpenConnectorObjApiName.department);
//        while (i < 200) {//最多两百页
//            arg.setOffset(offset);
//            Result<StdListData> result = openConnectorManager.queryOaMasterBatch(tenantId.toString(), dataCenterId, connectorApiName, arg);
//            StdListData stdListData = result.getData();
//            List<StdData> dataList = stdListData.getDataList();
//            List<StandardDepartmentObject> departmentObjects = dataList.stream().filter(v -> v.getMasterFieldVal() != null).map(v -> {
//                StandardDepartmentObject object = OuterObjectData.change2DepartmentObject(v.getMasterFieldVal());
//                return object;
//            }).collect(Collectors.toList());
//            departIds.addAll(departmentObjects.stream().map(StandardDepartmentObject::getDeptId).collect(Collectors.toSet()));
//            batchUpsertDepartment(outEa, appId, departmentObjects);
//            if (!result.isSuccess()) {
//                return Result.copy(result);
//            }
//            if (result.getData().getDataList() == null || result.getData().getDataList().size() < limit) {
//                break;
//            }
//            if (stdListData.getMaxTime() != null) {
//                arg.setLastMaxId(stdListData.getMaxId());
//            }
//            if (stdListData.getMaxId() != null) {
//                arg.setStartTime(stdListData.getMaxTime());
//            }
//            offset += limit;
//            i++;
//        }
//        outerOaDeptDataManager.deleteInvisibleDepts(outEa, ChannelEnum.standard_oa, appId, departIds);
        return Result.newSuccess();
    }

    private void batchUpsertDepartment(String outEa, String appId, List<StandardDepartmentObject> departmentObjects) {
        List<OuterOaDeptDataEntity> deptDataEntities = departmentObjects.stream()
                .map(outDeptData -> convert2OuterOaDeptDataEntity(outDeptData, outEa, appId)).collect(Collectors.toList());
        outerOaDeptDataManager.batchUpdateOutDeptId(deptDataEntities);
    }

    private Result<Void> upsertEmployee(String tenantId, OuterOaEnterpriseBindEntity bindEntity, OuterCallBackEventMsg callBackEventMsg) {
        if(callBackEventMsg==null||callBackEventMsg.getMsg()==null||callBackEventMsg.getMsg().get("data")==null){
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }
        Object dataList = callBackEventMsg.getMsg().get("data");
        List<OuterObjectData> objectDataList = JacksonUtil.fromJson(JacksonUtil.toJson(dataList), new com.fasterxml.jackson.core.type.TypeReference<List<OuterObjectData>>() {
        });
        if (objectDataList == null) {
            return Result.newSuccess();
        }
        List<StandardEmployeeObject> employeeData = objectDataList.stream().map(v -> {
            StandardEmployeeObject object = OuterObjectData.change2EmployeeObject(v);
            return object;
        }).collect(Collectors.toList());
        //入库
        batchUpsertEmp(bindEntity.getId(), employeeData);
        //同步
        batchSyncEmployee(Integer.valueOf(tenantId), bindEntity, employeeData);
        return Result.newSuccess();
    }

    private Result<Void> stopEmployee(String tenantId, OuterOaEnterpriseBindEntity bindEntity, OuterCallBackEventMsg callBackEventMsg) {
        if (callBackEventMsg.getMsg() == null || callBackEventMsg.getMsg().get("data") == null) {
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }
        Object dataList = callBackEventMsg.getMsg().get("data");
        List<OuterObjectData> objectDataList = JacksonUtil.fromJson(JacksonUtil.toJson(dataList), new com.fasterxml.jackson.core.type.TypeReference<List<OuterObjectData>>() {
        });
        if (objectDataList == null) {
            return Result.newSuccess();
        }
        List<StandardEmployeeObject> employeeData = objectDataList.stream().map(v -> {
            StandardEmployeeObject object = OuterObjectData.change2EmployeeObject(v);
            return object;
        }).collect(Collectors.toList());
        Set<String> needDeleteErpIds = employeeData.stream().map(v -> v.getUserid()).collect(Collectors.toSet());
        outerOaEmployeeDataManager.deleteInvisibleUsers(bindEntity.getOutEa(), ChannelEnum.standard_oa, bindEntity.getAppId(), needDeleteErpIds);
        return batchRemoveEmployee(Integer.valueOf(tenantId), bindEntity, needDeleteErpIds, RemoveEmployeeEventType.RESIGN_EMPLOYEE);
    }

    private Result<Void> removeEmployee(String tenantId, OuterOaEnterpriseBindEntity bindEntity, OuterCallBackEventMsg callBackEventMsg) {
        if (callBackEventMsg.getMsg() == null || callBackEventMsg.getMsg().get("data") == null) {
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }
        Object dataList = callBackEventMsg.getMsg().get("data");
        List<OuterObjectData> objectDataList = JacksonUtil.fromJson(JacksonUtil.toJson(dataList), new com.fasterxml.jackson.core.type.TypeReference<List<OuterObjectData>>() {
        });
        if (objectDataList == null) {
            return Result.newSuccess();
        }
        List<StandardEmployeeObject> employeeData = objectDataList.stream().map(v -> {
            StandardEmployeeObject object = OuterObjectData.change2EmployeeObject(v);
            return object;
        }).collect(Collectors.toList());
        Set<String> needDeleteErpIds = employeeData.stream().map(v -> v.getUserid()).collect(Collectors.toSet());
        outerOaEmployeeDataManager.deleteInvisibleUsers(bindEntity.getOutEa(), ChannelEnum.standard_oa, bindEntity.getAppId(), needDeleteErpIds);
        return batchRemoveEmployee(Integer.valueOf(tenantId), bindEntity, needDeleteErpIds, RemoveEmployeeEventType.REMOVE_RANGE);
    }

    private Result<Void> initEmployee(String tenantId, OuterOaEnterpriseBindEntity bindEntity, OuterCallBackEventMsg callBackEventMsg) {
        if (callBackEventMsg.getMsg() == null || callBackEventMsg.getMsg().get("data") == null) {
            //入库,同步
            Result<Void> voidResult = refreshOuterEmpData(bindEntity, Integer.valueOf(tenantId), true);
            if (!voidResult.isSuccess()) {
                return voidResult;
            }
        } else {
            Object dataList = callBackEventMsg.getMsg().get("data");
            List<OuterObjectData> objectDataList = JacksonUtil.fromJson(JacksonUtil.toJson(dataList), new com.fasterxml.jackson.core.type.TypeReference<List<OuterObjectData>>() {
            });
            if (objectDataList == null) {
                return Result.newSuccess();
            }
            List<StandardEmployeeObject> employeeData = objectDataList.stream().map(v -> {
                StandardEmployeeObject object = OuterObjectData.change2EmployeeObject(v);
                return object;
            }).collect(Collectors.toList());
            //入库
            batchUpsertEmp(bindEntity.getId(), employeeData);
            //同步
            batchSyncEmployee(Integer.valueOf(tenantId), bindEntity, employeeData);

        }
        return Result.newSuccess();
    }

    private Result<String> batchSyncEmployee(Integer tenantId, OuterOaEnterpriseBindEntity bindEntity, List<StandardEmployeeObject> employees) {
        String dcId = bindEntity.getId();

        //看配置是否要同步
        OuterOaConfigInfoEntity configInfoEntity = outerOaConfigInfoManager.getEntityByDataCenterId(OuterOaConfigInfoTypeEnum.SETTING_BIND_RULES, dcId);
        SettingAccountRulesModel settingAccountRulesModel = JSON.parseObject(configInfoEntity.getConfigInfo(), SettingAccountRulesModel.class);
        if (settingAccountRulesModel.getSyncTypeEnum() == EnterpriseConfigAccountSyncTypeEnum.accountSync) {
            //同步通讯录逻辑
            return dealSyncContactsEvent(bindEntity, employees);
        } else {
            if (settingAccountRulesModel.getBindTypeEnum() == BindTypeEnum.auto) {
                //自动绑定
                return autoBindEmployee(tenantId, bindEntity, employees);
            } else {
                //手动绑定：不做任何处理
                log.info("batchRefreshEmployee,manual bind,do nothing,bindEntity={},employees={}", bindEntity, employees);
                return Result.newSuccess();
            }
        }
    }

    private Result<Void> batchRemoveEmployee(Integer tenantId, OuterOaEnterpriseBindEntity bindEntity, Set<String> needDeleteErpIds, RemoveEmployeeEventType removeEmployeeEventType) {
        String dcId = bindEntity.getId();
        if (removeEmployeeEventType == null) {
            removeEmployeeEventType = RemoveEmployeeEventType.REMOVE_RANGE;
        }
        //看配置是否要同步
        OuterOaConfigInfoEntity configInfoEntity = outerOaConfigInfoManager.getEntityByDataCenterId(OuterOaConfigInfoTypeEnum.SETTING_BIND_RULES, dcId);
        SettingAccountRulesModel settingAccountRulesModel = JSON.parseObject(configInfoEntity.getConfigInfo(), SettingAccountRulesModel.class);
        if (settingAccountRulesModel.getSyncTypeEnum() == EnterpriseConfigAccountSyncTypeEnum.accountSync) {
            if (CollectionUtils.isNotEmpty(needDeleteErpIds)) {
                //停用和反绑定
                for (String outerId : needDeleteErpIds) {
                    objectDataManager.removeEmpData(bindEntity, outerId, removeEmployeeEventType);//默认是范围不可见
                }
            }
        } else {
            if (settingAccountRulesModel.getBindTypeEnum() == BindTypeEnum.auto) {
                if (CollectionUtils.isNotEmpty(needDeleteErpIds)) {
                    for (String outerId : needDeleteErpIds) {
                        objectDataManager.removeEmpData(bindEntity, outerId, removeEmployeeEventType);//默认是范围不可见
                    }
                }
            } else {
                //手动绑定：不做任何处理
                log.info("batchRefreshEmployee,manual bind,do nothing,bindEntity={},needDeleteErpIds={}", bindEntity, needDeleteErpIds);
                return Result.newSuccess();
            }
        }
        return Result.newSuccess();
    }

    private Result<String> dealSyncContactsEvent(OuterOaEnterpriseBindEntity entity, List<StandardEmployeeObject> employees) {
        if (CollectionUtils.isEmpty(employees)) {
            return Result.newSuccess();
        }
        for (int i = 0; i < employees.size(); i++) {
            StandardEmployeeObject item = employees.get(i);
            log.info("dealSyncContactsEvent,item={}", item);
            // 校验是否已存在对应的员工映射,同一个企业的员工映射只能有一个
            final String fsEmpId = outerOaEmployeeBindManager.getFsEmpIdByEaAndOutEmpId(entity.getChannel(), entity.getAppId(), entity.getFsEa(),
                    entity.getOutEa(), item.getUserid());
            if (StringUtils.isNotEmpty(fsEmpId)) {
                // 修改crm对象
                Result<IncrementUpdateResult> updateResult = objectDataManager.updateEmpData(entity, item.getUserid());
                log.info("dealSyncContactsEvent,updateEmpData,createResult={}", updateResult);
            } else {
                Result<ActionAddResult> createResult = objectDataManager.createEmployee(entity,
                        item.getUserid());
                log.info("dealSyncContactsEvent,createEmployee,createResult={}", createResult);
            }
        }
        return Result.newSuccess();
    }

    private Result<String> autoBindEmployee(Integer tenantId, OuterOaEnterpriseBindEntity enterpriseBindEntity, List<StandardEmployeeObject> employees) {
        String fsEa = enterpriseBindEntity.getFsEa();
        String outEa = enterpriseBindEntity.getOutEa();
        String appId = enterpriseBindEntity.getAppId();
        String dcId = enterpriseBindEntity.getId();
        //查看是否设置了映射规则
        OuterOaConfigInfoEntity configInfoEntity = outerOaConfigInfoManager.getEntityByDataCenterId(OuterOaConfigInfoTypeEnum.EMPLOYEE_UNIQUE_IDENTITY, dcId);
        SystemFieldMappingResult systemFieldMappingResult = JSON.parseObject(configInfoEntity.getConfigInfo(), SystemFieldMappingResult.class);
        if (ObjectUtils.isEmpty(systemFieldMappingResult) || CollectionUtils.isEmpty(systemFieldMappingResult.getItemFieldMappings())) {
            log.info("ContactsServiceImpl.autoBindEmployee,systemFieldMappingResult is empty,dcId={}", dcId);
            return Result.newSuccess();
        }

        List<SystemFieldMappingResult.ItemFieldMapping> itemFieldMappings = systemFieldMappingResult.getItemFieldMappings();
        //设置了一个字段，用来判断是否是处理自动绑定的唯一规则
        itemFieldMappings = itemFieldMappings.stream().filter(SystemFieldMappingResult.ItemFieldMapping::getMatchUnique).collect(Collectors.toList());
        log.info("ContactsServiceImpl.autoBindEmployee,itemFieldMappings={}", JSON.toJSONString(itemFieldMappings));

        // 员工列表
        if (CollectionUtils.isEmpty(employees)) {
            log.info("employees is empty,dcId={}", dcId);
            return Result.newSuccess();
        }

        // 获取纷享销客员工列表
        com.facishare.open.order.contacts.proxy.api.result.Result<List<ObjectData>> fsEmployeeResult =
                fsEmployeeServiceProxy.listAll(tenantId, GlobalValue.ALL_COMPANY_DEPARTMENT_ID + "");
        if (!fsEmployeeResult.isSuccess() || CollectionUtils.isEmpty(fsEmployeeResult.getData())) {
            log.info("ContactsServiceImpl.autoBindEmployee,fsEmployeeResult is empty,ei={}", tenantId);
            return Result.newSuccess();
        }
        List<ObjectData> fsEmployees = fsEmployeeResult.getData();

        // 已绑定的员工ID列表，避免重复绑定
        List<OuterOaEmployeeBindEntity> existBinds = outerOaEmployeeBindManager.getEntitiesByNotPage(
                OuterOaEmployeeBindParams.builder()
                        .channel(enterpriseBindEntity.getChannel())
                        .outEa(outEa)
                        .fsEa(fsEa)
                        .appId(appId)
                        .build());
        Set<String> boundFsEmpIds = new HashSet<>();
        Set<String> boundOutEmpIds = new HashSet<>();
        if (CollectionUtils.isNotEmpty(existBinds)) {
            boundFsEmpIds = existBinds.stream()
                    .map(OuterOaEmployeeBindEntity::getFsEmpId)
                    .collect(Collectors.toSet());
            boundOutEmpIds = existBinds.stream()
                    .map(OuterOaEmployeeBindEntity::getOutEmpId)
                    .collect(Collectors.toSet());
        }

        // 开始匹配并绑定
        List<OuterOaEmployeeBindEntity> newBinds = new ArrayList<>();
        for (StandardEmployeeObject employeeObject : employees) {
            // 已绑定的员工跳过
            if (boundOutEmpIds.contains(employeeObject.getUserid())) {
                continue;
            }

            for (ObjectData fsEmployee : fsEmployees) {
                // 已绑定的员工跳过
                if (boundFsEmpIds.contains(fsEmployee.getId())) {
                    continue;
                }

                // 根据映射规则匹配员工
                boolean isMatch = true;
                for (SystemFieldMappingResult.ItemFieldMapping mapping : itemFieldMappings) {
                    String outerFieldName = mapping.getOuterOAFieldApiName();
                    String crmFieldName = mapping.getCrmFieldApiName();

                    // 获取字段值
                    Object outerValue = getFieldValueFromStandardEmployee(employeeObject,outerFieldName);
                    // 获取纷享销客字段值
                    Object crmValue = fsEmployee.get(crmFieldName);

                    // 如果任一字段值为空，则跳过此字段比较
                    if (outerValue == null || crmValue == null) {
                        continue;
                    }

                    // 比较字段值是否匹配
                    if (!outerValue.toString().equals(crmValue.toString())) {
                        isMatch = false;
                        break;
                    }
                }

                // 如果所有字段都匹配，则创建绑定关系
                if (isMatch) {
                    OuterOaEmployeeBindEntity bindEntity = new OuterOaEmployeeBindEntity();
                    bindEntity.setId(IdGenerator.get());
                    bindEntity.setChannel(enterpriseBindEntity.getChannel());
                    bindEntity.setFsEa(fsEa);
                    bindEntity.setOutEa(outEa);
                    bindEntity.setAppId(appId);
                    bindEntity.setDcId(dcId);
                    bindEntity.setFsEmpId(fsEmployee.getId());
                    bindEntity.setOutEmpId(employeeObject.getUserid());
                    bindEntity.setBindStatus(BindStatusEnum.normal);
                    bindEntity.setCreateTime(System.currentTimeMillis());
                    bindEntity.setUpdateTime(System.currentTimeMillis());

                    newBinds.add(bindEntity);

                    // 更新已绑定集合，避免一个纷享员工绑定多个企业微信员工
                    boundFsEmpIds.add(fsEmployee.getId());
                    boundOutEmpIds.add(employeeObject.getUserid());

                    log.info("autoBindEmployee,match success,fsEmpId={},outEmpId={}",
                            fsEmployee.getId(), employeeObject.getUserid());
                    break;
                }
            }
        }

        // 批量保存绑定关系
        if (CollectionUtils.isNotEmpty(newBinds)) {
            Integer count = outerOaEmployeeBindManager.batchUpsert(newBinds);
            log.info("ContactsServiceImpl.autoBindEmployee,batchInsert count={}", count);
        }
        return Result.newSuccess();
    }

    private Object getFieldValueFromStandardEmployee(StandardEmployeeObject employee, String fieldName) {
        try {
            try {
                Field field = employee.getClass().getDeclaredField(fieldName);
                field.setAccessible(true);
                return field.get(employee);
            } catch (NoSuchFieldException e) {
                // 尝试使用getter方法
                String getterMethodName = "get" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1);
                try {
                    Method getterMethod = employee.getClass().getMethod(getterMethodName);
                    return getterMethod.invoke(employee);
                } catch (NoSuchMethodException ex) {
                    // 尝试is开头的getter（用于boolean类型）
                    getterMethodName = "is" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1);
                    try {
                        Method isMethod = employee.getClass().getMethod(getterMethodName);
                        return isMethod.invoke(employee);
                    } catch (NoSuchMethodException exc) {
                        log.error("ContactsServiceImpl.getFieldValueFromQywxEmployee: No field or getter found for {}", fieldName);
                        return null;
                    }
                }
            }
        } catch (Exception e) {
            log.error("ContactsServiceImpl.getFieldValueFromQywxEmployee error,fieldName={},error={}",
                    fieldName, e.getMessage(), e);
            return null;
        }
    }


    public Result<Void> upsertConfig(String tenantId, OuterOaEnterpriseBindEntity bindEntity, OuterCallBackEventMsg callBackEventMsg) {
        if(callBackEventMsg==null||callBackEventMsg.getMsg()==null||callBackEventMsg.getMsg().get("data")==null||callBackEventMsg.getMsg().get("configType")==null){
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }
        String configType=callBackEventMsg.getMsg().get("configType").toString();
        Object configData=callBackEventMsg.getMsg().get("data");
        outerOaConfigInfoManager.upsertOpenConnectorConfig(bindEntity,configType,configData);
        return Result.newSuccess();
    }

    public Result<Void> deleteConfig(String tenantId, OuterOaEnterpriseBindEntity bindEntity, OuterCallBackEventMsg callBackEventMsg) {
        if(callBackEventMsg==null||callBackEventMsg.getMsg()==null||callBackEventMsg.getMsg().get("data")==null||callBackEventMsg.getMsg().get("configType")==null){
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }
        String configType=callBackEventMsg.getMsg().get("configType").toString();
        outerOaConfigInfoManager.deleteOpenConnectorConfig(bindEntity,configType);
        return Result.newSuccess();
    }
}
