package com.facishare.open.outer.oa.connector.web.msg.template;

import com.facishare.open.erpdss.outer.oa.connector.base.context.MethodContext;
import com.facishare.open.erpdss.outer.oa.connector.base.context.TemplateResult;
import com.facishare.open.erpdss.outer.oa.connector.base.outer.msg.SendMsgHandlerTemplate;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.outer.oa.connector.common.api.admin.StandardConnectorVo;
import com.facishare.open.outer.oa.connector.common.api.enums.AlertTypeEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.BindStatusEnum;
import com.facishare.open.outer.oa.connector.common.api.result.Result;
import com.facishare.open.outer.oa.connector.web.openconnetor.OpenConnectorManager;
import com.facishare.open.outer.oa.connector.web.openconnetor.model.OuterCreateTodoArg;
import com.facishare.open.outer.oa.connector.web.openconnetor.model.OuterDealTodoArg;
import com.facishare.open.outer.oa.connector.web.openconnetor.model.OuterMsgModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/5/26
 */

@Slf4j
@Component
public class OpenConnectorDealTodoTemplate extends SendMsgHandlerTemplate {
    @Resource
    private OpenConnectorManager openConnectorManager;


    @Override
    protected void filterMsg(MethodContext context) {
        log.info("OpenConnectorDealTodoTemplate.filterMsg,context={}", context);
        OuterDealTodoArg outerDealTodoArg = context.getData();

        if (ObjectUtils.isEmpty(outerDealTodoArg)) {
            context.setResult(TemplateResult.newError(null));
            return;
        }
        OuterOaEnterpriseBindEntity enterpriseBindEntity = outerDealTodoArg.getOuterOaEnterpriseBindEntity();
        if (!enterpriseBindEntity.getBindStatus().equals(BindStatusEnum.normal)) {
            log.info("OpenConnectorDealTodoTemplate.filterMsg.is not normal,enterpriseBindEntity={}", enterpriseBindEntity);
            context.setResult(TemplateResult.newError(null));
            return;
        }
        StandardConnectorVo standardConnectorVo = enterpriseBindEntity.getConnectParams().getStandard_oa();
        if (!standardConnectorVo.isAlertConfig()) {
            log.info("OpenConnectorDealTodoTemplate.filterMsg.is not alert config,standardConnectorVo={}", standardConnectorVo);
            context.setResult(TemplateResult.newError(null));
            return;
        }
        if (!standardConnectorVo.getAlertTypes().contains(AlertTypeEnum.CRM_TODO)) {
            log.info("OpenConnectorDealTodoTemplate.filterMsg.if not todo type,qywxConnectorVo={}", standardConnectorVo);
            context.setResult(TemplateResult.newError(null));
            return;
        }
        Result<Void> result = openConnectorManager.filterDealTodoMsg(outerDealTodoArg.getTenantId(), outerDealTodoArg.getDcId(), outerDealTodoArg.getApiName(), outerDealTodoArg.getCrmArg());
        if (result.isSuccess()) {
            context.setResult(TemplateResult.newSuccess());
        } else {
            context.setResult(TemplateResult.newError(result.getMsg()));
        }
    }

    @Override
    public void buildMsg(MethodContext context) {
        log.info("OpenConnectorDealTodoTemplate.build,context={}",context);
        OuterDealTodoArg outerDealTodoArg = context.getData();
        Result<OuterMsgModel> result = openConnectorManager.buildDealTodoMsg(outerDealTodoArg.getTenantId(), outerDealTodoArg.getDcId(), outerDealTodoArg.getApiName(), outerDealTodoArg.getCrmArg());
        if(result.isSuccess()){
            outerDealTodoArg.setOuterModel(result.getData());
            context.setResult(TemplateResult.newSuccess());
        }else{
            context.setResult(TemplateResult.newError(result.getMsg()));
        }
    }

    @Override
    public void sendMsg(MethodContext context) {
        log.info("OpenConnectorDealTodoTemplate.sendMsg,context={}",context);
        OuterDealTodoArg outerDealTodoArg = context.getData();
        Result<Void> result = openConnectorManager.sendDealTodoMsg(outerDealTodoArg.getTenantId(), outerDealTodoArg.getDcId(), outerDealTodoArg.getApiName(), outerDealTodoArg.getOuterModel());
        if(result.isSuccess()){
            context.setResult(TemplateResult.newSuccess());
        }else{
            context.setResult(TemplateResult.newError(result.getMsg()));
        }
    }
}
