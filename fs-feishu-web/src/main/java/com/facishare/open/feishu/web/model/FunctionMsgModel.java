//package com.facishare.open.feishu.web.model;
//
//import com.facishare.open.feishu.syncapi.enums.ChannelEnum;
//import lombok.Data;
//
//import java.io.Serializable;
//
//@Data
//public class FunctionMsgModel implements Serializable {
//    private String dataCenterId;
//    private ChannelEnum channel;
//    private String fsEa;
//    private String ourEa;
//    private String appId;
//    private String type;
//    private String eventType;
//    private String data;  //原始数据
//}
