package com.facishare.open.outer.oa.connector.web.openconnetor.service;


import com.facishare.open.erpdss.outer.oa.connector.base.context.TemplateResult;
import com.facishare.open.outer.oa.connector.common.api.result.Result;
import com.facishare.open.outer.oa.connector.web.openconnetor.model.OuterMsgModel;
import com.fxiaoke.message.extrnal.platform.model.arg.CreateTodoArg;
import com.fxiaoke.message.extrnal.platform.model.arg.DealTodoArg;

public interface OpenConnectorDealTodoService {
    /**
     * 过滤消息
     * @param context
     */
    Result<Void> filterDealTodoMsg(String tenantId, String dataCenterId, String connectorApiName, DealTodoArg context);

    /**
     * 组装消息
     * @param context
     */
    Result<OuterMsgModel> buildDealTodoMsg(String tenantId, String dataCenterId, String connectorApiName,DealTodoArg context);

    /**
     * 发送消息
     * @param context
     */
    Result<Void> sendDealTodoMsg(String tenantId, String dataCenterId, String connectorApiName,OuterMsgModel context);
}
