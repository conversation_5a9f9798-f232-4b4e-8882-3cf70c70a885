package com.facishare.open.outer.oa.connector.web.openconnetor.service;


import com.facishare.open.erpdss.outer.oa.connector.base.context.TemplateResult;
import com.facishare.open.outer.oa.connector.common.api.result.Result;
import com.facishare.open.outer.oa.connector.web.openconnetor.model.OuterMsgModel;
import com.fxiaoke.message.extrnal.platform.model.arg.CreateTodoArg;
import com.fxiaoke.message.extrnal.platform.model.arg.SendTextMessageArg;

public interface OpenConnectorSendTextMsgService {
    /**
     * 过滤消息
     * @param context
     */
    Result<Void> filterTextMsg(String tenantId, String dataCenterId, String connectorApiName, SendTextMessageArg context);

    /**
     * 组装消息
     * @param context
     */
    Result<OuterMsgModel> buildTextMsg(String tenantId, String dataCenterId, String connectorApiName,SendTextMessageArg context);

    /**
     * 发送消息
     * @param context
     */
    Result<Void> sendTextMsg(String tenantId, String dataCenterId, String connectorApiName,OuterMsgModel context);
}
