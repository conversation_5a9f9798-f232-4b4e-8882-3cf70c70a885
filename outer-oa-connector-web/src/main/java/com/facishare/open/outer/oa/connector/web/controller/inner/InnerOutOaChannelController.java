package com.facishare.open.outer.oa.connector.web.controller.inner;

import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.alibaba.fastjson.JSONObject;
import com.facishare.open.outer.oa.connector.common.api.login.CrmUserModel;
import com.facishare.open.outer.oa.connector.common.api.outerInterface.CommonLoginAuthService;
import com.facishare.open.outer.oa.connector.common.api.result.Result;
import com.facishare.open.outer.oa.connector.common.api.result.ResultCodeEnum;
import com.facishare.open.outer.oa.connector.i18n.I18NStringEnum;
import com.facishare.open.outer.oa.connector.web.exception.OutOaConnectorException;
import com.facishare.open.outer.oa.connector.web.manager.CustomFunctionFactory;
import com.facishare.open.outer.oa.connector.web.model.CreateJsapiSignature;
import com.facishare.open.outer.oa.connector.web.model.JsapiSignature;
import com.facishare.open.outer.oa.connector.web.model.admin.CommonExecuteArg;
import com.facishare.open.outer.oa.connector.web.service.CommonExecuteService;
import com.facishare.open.outer.oa.connector.web.service.OuterChannelAuthService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import retrofit2.http.Body;
import retrofit2.http.Header;

import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/1/15 14:19:15
 */
@RestController
@RequestMapping("/inner/out/oa")
public class InnerOutOaChannelController {

    @Autowired
    private Map<String, OuterChannelAuthService> authServiceMap;
    @Autowired
    private CommonLoginAuthService commonLoginAuthService;
    @Autowired
    private CustomFunctionFactory customFunctionFactory;

    @PostMapping("/{channel}/signature")
    public JsapiSignature createJsapiSignature(@PathVariable("channel") String channel, @RequestBody CreateJsapiSignature.Arg arg) {
        final OuterChannelAuthService authService = authServiceMap.get(channel);
        if (Objects.isNull(authService)) {
            throw new OutOaConnectorException(I18NStringEnum.cxb001, channel);
        }

        return authService.getJsapiSignature(arg.getAppId(), arg.getFsEa(), arg.getOutEa(), arg.getUrl());
    }

    @GetMapping("/getFsUserInfo")
    public Result<CrmUserModel> getFsUserInfo(@RequestParam("ticket") String ticket){
        Result<CrmUserModel> crmUserByTicket = commonLoginAuthService.getCrmUserByTicket(ticket);
        return crmUserByTicket;
    }

    @GetMapping("/getJsApiSignature")
    public Result<Map<String,Object>> getJsApiSignature(@RequestParam("appId") String appId,@RequestParam("url") String url,
                                                        @RequestParam("outEa") String outEa, @RequestParam("fsEa") String fsEa,@RequestParam("channel") String channel){
        final OuterChannelAuthService authService = authServiceMap.get(channel);
        if (Objects.isNull(authService)) {
            throw new OutOaConnectorException(I18NStringEnum.cxb001, channel);
        }
        JsapiSignature jsapiSignature = authService.getJsapiSignature(appId, fsEa, outEa, url);
        return Result.newSuccess(JSONObject.parseObject(JSONObject.toJSONString(jsapiSignature),Map.class));
    }

    @PostMapping("/common/execute")
    public Result<?>  commonExecute(@RequestHeader(value="x-fs-ei") Integer tenantId, @RequestBody CommonExecuteArg arg){
        if (arg == null||tenantId==null||arg.getType()==null||arg.getParams()==null) {
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }
        arg.setTenantId(String.valueOf(tenantId));
        CommonExecuteService commonExecuteService=customFunctionFactory.getHandlerByType(arg.getType());
        return commonExecuteService.executeLogic(arg);
    }
}
