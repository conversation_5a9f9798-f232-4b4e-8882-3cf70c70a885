package com.facishare.open.ding.api.enums;

/**
 * @<NAME_EMAIL>
 * @ClassName: ExtraObjEnum
 * @Description: 用于前端显示页签的扩展字段
 * @datetime 2019/3/29 14:58
 * @Version 1.0
 */
public enum ExtraObjEnum {

    DeliveryNoteObj("发货单", "DeliveryNoteObj");

    private String descName;
    private String apiName;

    private ExtraObjEnum(String descName, String apiName) {
        this.descName = descName;
        this.apiName = apiName;
    }

    public String getDescName() {
        return this.descName;
    }

    public String getApiName() {
        return this.apiName;
    }

    public static ExtraObjEnum[] getExtraObj(){
        return ExtraObjEnum.values();
    }

}
