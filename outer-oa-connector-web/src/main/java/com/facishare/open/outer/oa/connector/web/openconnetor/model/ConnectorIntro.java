package com.facishare.open.outer.oa.connector.web.openconnetor.model;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2025/5/14
 */
@Data
public class ConnectorIntro implements Serializable {
    /**
     * 连接器名称，暂无使用
     */
    private String name;

    /**
     * 版本
     */
    private String version;



    /**
     * 参数格式，用于构建用户输入和展示连接参数的页面。
     */
    private List<ParamSchema> paramSchemas;

    /**
     * 特性配置
     */
    private long features;

    @Data
    public static class ParamSchema {
        /**
         * 字段标签
         */
        private String label;
        /**
         * 字段key
         */
        private String key;

        /**
         * 必填
         */
        private boolean required = true;

        /**
         * 是否允许用户修改，函数是均允许修改
         */
        private boolean userEditable = true;

        /**
         * 是否展示在连接器配置页，当为false时，页面无法配置只能函数获取后更新
         */
        private boolean show = true;

        /**
         * 授权之后展示
         */
        private boolean showAfterAuthorization = false;

        /**
         * 输入类型，目前就输入文本和密码，值类型都是string类型
         */
        private ParamType type = ParamType.TEXT;

        /**
         * 文字提示
         */
        private String tooltip;

        /**
         * 默认值
         */
        private String defaultValue;

        /**
         * 框内提示语
         */
        private String placeholder;
    }

    public enum ParamType {
        /**
         * 输入文本
         */
        TEXT,
        /**
         * 密码
         */
        PASSWORD,
        /**
         * 列表Map
         */
        LIST_MAP
    }
}
