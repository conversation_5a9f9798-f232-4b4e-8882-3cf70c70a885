package com.facishare.open.feishu.sync.test.service;

import com.alibaba.fastjson.JSONObject;
import com.facishare.open.feishu.sync.test.BaseTest;
import com.facishare.open.feishu.syncapi.enums.MsgTypeEnum;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.service.FeishuMessageService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class FeishuMessageServiceTest extends BaseTest {

    @Autowired
    private FeishuMessageService feishuMessageService;

    @Test
    public void sendTextMessage(){
        JSONObject content = new JSONObject();
        content.put("text","普通文本消息 \\n 普通文本消息2");

        Result<Void> booleanResult = feishuMessageService.send("cli_a20192f6afb8d00c",
                "11b1a8c266da9758",
                MsgTypeEnum.text,
                "ou_d9292ea3f4405f4663083a15ffd9a333",
                content.toJSONString());
        System.out.println(booleanResult.getData()+"-"+booleanResult.getCode()+"-"+booleanResult.getMsg());
    }

    @Test
    public void sendInteractiveMessage(){
        Result<Void> result = feishuMessageService.send("cli_a20192f6afb8d00c",
                "11b1a8c266da9758",
                MsgTypeEnum.interactive,
                "ou_d9292ea3f4405f4663083a15ffd9a333",
                "{\"config\":{\"wide_screen_mode\":true},\"elements\":[{\"alt\":{\"content\":\"\",\"tag\":\"plain_text\"},\"img_key\":\"img_7ea74629-9191-4176-998c-2e603c9c5e8g\",\"tag\":\"img\"},{\"tag\":\"div\",\"text\":{\"content\":\"你是否曾因为一本书而产生心灵共振，开始感悟人生？\\n你有哪些想极力推荐给他人的珍藏书单？\\n\\n加入 **4·23 飞书读书节**，分享你的**挚爱书单**及**读书笔记**，**赢取千元读书礼**！\\n\\n📬 填写问卷，晒出你的珍藏好书\\n😍 想知道其他人都推荐了哪些好书？马上[入群围观](https://open.feishu.cn/)\\n📝 用[读书笔记模板](https://open.feishu.cn/)（桌面端打开），记录你的心得体会\\n🙌 更有惊喜特邀嘉宾 4月12日起带你共读\",\"tag\":\"lark_md\"}},{\"actions\":[{\"tag\":\"button\",\"text\":{\"content\":\"立即推荐好书\",\"tag\":\"plain_text\"},\"type\":\"primary\",\"url\":\"https://open.feishu.cn/\"},{\"tag\":\"button\",\"text\":{\"content\":\"查看活动指南\",\"tag\":\"plain_text\"},\"type\":\"default\",\"url\":\"https://open.feishu.cn/\"}],\"tag\":\"action\"}],\"header\":{\"template\":\"turquoise\",\"title\":{\"content\":\"📚晒挚爱好书，赢读书礼金\",\"tag\":\"plain_text\"}}}");
        System.out.println(result.getData()+"-"+result.getCode()+"-"+result.getMsg());
    }

    @Test
    public void sendWelcomeMsg() {
        Result<Void> result = feishuMessageService.sendWelcomeMsg("cli_a3ddeb52763b100c",
                "100d08b69448975d",
                "ou_0fd979c697c5dd375d12ffb999492a91");
        System.out.println(result);
    }
}
