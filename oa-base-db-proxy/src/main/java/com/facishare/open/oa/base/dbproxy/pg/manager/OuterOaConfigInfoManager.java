package com.facishare.open.oa.base.dbproxy.pg.manager;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.facishare.open.oa.base.dbproxy.manager.DescManager;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaConfigInfoEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.mapper.OuterOaConfigInfoMapper;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaConfigInfoParams;
import com.facishare.open.oauth.model.enums.AppTypeEnum;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.open.outer.oa.connector.common.api.admin.BaseConnectorVo;
import com.facishare.open.outer.oa.connector.common.api.annotation.SystemAnnotation;
import com.facishare.open.outer.oa.connector.common.api.enums.*;
import com.facishare.open.outer.oa.connector.common.api.info.SettingAccountRulesModel;
import com.facishare.open.outer.oa.connector.common.api.result.Result;
import com.facishare.open.outer.oa.connector.common.api.result.SettingsResult;
import com.facishare.rest.core.util.JacksonUtil;
import com.facishare.open.outer.oa.connector.common.api.result.SystemFieldMappingResult;
import com.facishare.open.outer.oa.connector.common.api.result.SystemFieldResult;
import com.facishare.open.outer.oa.connector.i18n.I18NStringManager;
import com.fxiaoke.api.IdGenerator;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.BooleanUtils;
import com.google.common.collect.Sets;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.util.*;

/**
 * Manager 类 - 配置信息
 */
@Component
// IgnoreI18nFile
public class OuterOaConfigInfoManager {

    @Resource
    private OuterOaConfigInfoMapper outerOaConfigInfoMapper;
    @Autowired
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;
    @Autowired
    private DescManager descManager;
    @Autowired
    private I18NStringManager i18NStringManager;

    public Integer insert(OuterOaConfigInfoEntity entity) {
        if (StringUtils.isEmpty(entity.getId())) {
            entity.setId(IdGenerator.get());
        }
        return outerOaConfigInfoMapper.insert(entity);
    }

    public Integer updateById(OuterOaConfigInfoEntity entity) {
        return outerOaConfigInfoMapper.updateById(entity);
    }

    public OuterOaConfigInfoEntity queryConnectorConfigInfoByDcId(String dcId, OuterOaConfigInfoTypeEnum type) {
        LambdaQueryWrapper<OuterOaConfigInfoEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OuterOaConfigInfoEntity::getDcId, dcId);
        wrapper.eq(OuterOaConfigInfoEntity::getType, type);
        return outerOaConfigInfoMapper.selectOne(wrapper);
    }

    //delete
    public Integer deleteById(OuterOaConfigInfoEntity entity) {
        return outerOaConfigInfoMapper.deleteById(entity);
    }

    /**
     * 根据参数查询配置信息列表
     *
     * @param params 查询参数
     * @return 配置信息实体列表
     */
    public List<OuterOaConfigInfoEntity> getEntities(OuterOaConfigInfoParams params) {
        LambdaQueryWrapper<OuterOaConfigInfoEntity> wrapper = new LambdaQueryWrapper<>();
        if (params.getChannel() != null) {
            wrapper.eq(OuterOaConfigInfoEntity::getChannel, params.getChannel());
        }
        if (StringUtils.isNotEmpty(params.getDcId())) {
            wrapper.eq(OuterOaConfigInfoEntity::getDcId, params.getDcId());
        }
        if (StringUtils.isNotEmpty(params.getFsEa())) {
            wrapper.eq(OuterOaConfigInfoEntity::getFsEa, params.getFsEa());
        }
        if (StringUtils.isNotEmpty(params.getOutEa())) {
            wrapper.eq(OuterOaConfigInfoEntity::getOutEa, params.getOutEa());
        }
        if (StringUtils.isNotEmpty(params.getAppId())) {
            wrapper.eq(OuterOaConfigInfoEntity::getAppId, params.getAppId());
        }
        if (params.getType() != null) {
            wrapper.eq(OuterOaConfigInfoEntity::getType, params.getType());
        }

        return outerOaConfigInfoMapper.selectList(wrapper);
    }

    /**
     * 初始化默认值
     *
     * @param type
     * @param dataCenterId
     * @return
     */
    public OuterOaConfigInfoEntity getEntityByDataCenterId(OuterOaConfigInfoTypeEnum type, String dataCenterId) {
        LambdaQueryWrapper<OuterOaConfigInfoEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OuterOaConfigInfoEntity::getType, type);
        wrapper.eq(OuterOaConfigInfoEntity::getDcId, dataCenterId);
        wrapper.last("limit 1");
        OuterOaConfigInfoEntity outerOaConfigInfoEntity = outerOaConfigInfoMapper.selectOne(wrapper);
        if (ObjectUtils.isEmpty(outerOaConfigInfoEntity)) {
            if (Objects.equals(type, OuterOaConfigInfoTypeEnum.SETTING_BIND_RULES)) {
                outerOaConfigInfoEntity = getOrDefaultRules(dataCenterId);
            }
            if (Objects.equals(type, OuterOaConfigInfoTypeEnum.EMPLOYEE_UNIQUE_IDENTITY)) {
                outerOaConfigInfoEntity = getOrDefaultEmployeeMappingsFields(dataCenterId);
            }
            if (Objects.equals(type, OuterOaConfigInfoTypeEnum.OUTER_SYSTEM_OBJECT_FIELDS)) {
                outerOaConfigInfoEntity = getOrDefaultOuterEmployeeDesc(dataCenterId);
            }
        }
        return outerOaConfigInfoEntity;
    }

    private OuterOaConfigInfoEntity getOrDefaultOuterEmployeeDesc( String dataCenterId){
        // 获取所有带有SystemAnnotation注解的字段
        OuterOaEnterpriseBindEntity outerOaEnterpriseBindEntity = outerOaEnterpriseBindManager
                .getEntityById(dataCenterId);
        if (ObjectUtils.isEmpty(outerOaEnterpriseBindEntity)) {
            LogUtils.warn("getdata default mappings data center error:{}", dataCenterId);
            return null;
        }
        ChannelEnum channelEnum=outerOaEnterpriseBindEntity.getChannel();
        Class<?> employeeObjectClass = channelEnum.getEmployeeObjectClass();
        if (employeeObjectClass == null) {
            LogUtils.warn("No employee object class defined for channel: {}", channelEnum);
            return null;
        }
        Set<SystemFieldResult> outerSystemFields = Sets.newHashSet();
        for (Field field : employeeObjectClass.getDeclaredFields()) {
            SystemAnnotation annotation = field.getAnnotation(SystemAnnotation.class);
            if (annotation != null) {
                SystemFieldResult systemField = SystemFieldResult.builder()
                        .fieldApiName(annotation.outerOaFieldName().isEmpty() ? field.getName()
                                : annotation.outerOaFieldName())
                        .fieldLabel(getFieldValue(annotation,outerOaEnterpriseBindEntity.getFsEa()))
                        .fieldType(annotation.outerOaFieldType().name()) // 默认字段类型
                        .build();
                outerSystemFields.add(systemField);
            }
        }

        // 创建并保存配置到数据库
        if (!outerSystemFields.isEmpty()) {
            OuterOaConfigInfoEntity entity = OuterOaConfigInfoEntity.builder().id(IdGenerator.get()).dcId(outerOaEnterpriseBindEntity.getId())
                    .channel(channelEnum).fsEa(outerOaEnterpriseBindEntity.getFsEa())
                    .outEa(outerOaEnterpriseBindEntity.getOutEa()).appId(outerOaEnterpriseBindEntity.getAppId())
                    .type(OuterOaConfigInfoTypeEnum.OUTER_SYSTEM_OBJECT_FIELDS)
                    .configInfo(JSONObject.toJSONString(outerSystemFields)).createTime(System.currentTimeMillis())
                    .updateTime(System.currentTimeMillis()).build();
            try {
                outerOaConfigInfoMapper.batchUpsertByDcIdAndType(Lists.newArrayList(entity));
                return entity;
            } catch (Exception e) {
                LogUtils.error("Save outer system fields error, dcId: {}", outerOaEnterpriseBindEntity.getId(), e);
            }
        }
        return null;
    }


    private String getFieldValue(SystemAnnotation systemAnnotation,String fsEa){
      String value=  systemAnnotation.outerOALabel().isEmpty() ? systemAnnotation.value().getDesc()
              : systemAnnotation.outerOALabel();
        String langByEa = i18NStringManager.getLangByEa(systemAnnotation.i18n().getI18nKey(), null, fsEa, value);
        return langByEa;
    }

    public OuterOaConfigInfoEntity initDefault(OuterOaConfigInfoTypeEnum type, String dataCenterId) {
        LambdaQueryWrapper<OuterOaConfigInfoEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OuterOaConfigInfoEntity::getType, type);
        wrapper.eq(OuterOaConfigInfoEntity::getDcId, dataCenterId);
        wrapper.last("limit 1");
        OuterOaConfigInfoEntity outerOaConfigInfoEntity = null;
        if (ObjectUtils.isEmpty(outerOaConfigInfoEntity)) {
            if (Objects.equals(type, OuterOaConfigInfoTypeEnum.SETTING_BIND_RULES)) {
                outerOaConfigInfoEntity = getOrDefaultRules(dataCenterId);
            }
            if (Objects.equals(type, OuterOaConfigInfoTypeEnum.EMPLOYEE_UNIQUE_IDENTITY)) {
                outerOaConfigInfoEntity = getOrDefaultEmployeeMappingsFields(dataCenterId);
            }
        }
        return outerOaConfigInfoEntity;
    }

    /**
     * 根据数据中心ID和配置类型进行upsert操作（更新或插入） 如果存在对应记录，则更新；如果不存在，则插入新记录
     *
     * @param entity 配置信息实体
     * @return 操作影响的行数
     */
    public Integer upsertByDcIdAndType(OuterOaConfigInfoEntity entity) {
        if (entity == null || entity.getType() == null || StringUtils.isEmpty(entity.getDcId())) {
            throw new IllegalArgumentException("配置类型和数据中心ID不能为空");
        }

        // 设置时间戳
        long currentTimeMillis = System.currentTimeMillis();
        if (entity.getCreateTime() == null) {
            entity.setCreateTime(currentTimeMillis);
        }
        entity.setUpdateTime(currentTimeMillis);

        // 查询是否已存在
        OuterOaConfigInfoEntity existingEntity = getEntityByDataCenterId(entity.getType(), entity.getDcId());

        if (existingEntity != null) {
            // 已存在记录，执行更新
            entity.setId(existingEntity.getId());
            return updateById(entity);
        } else {
            // 不存在记录，执行插入
            return insert(entity);
        }
    }

    /**
     * 批量插入或更新配置信息 根据dcId和type进行批量upsert操作
     *
     * @param entityList 配置信息实体列表
     * @return 操作影响的行数
     */
    public Integer batchUpsertByDcIdAndType(List<OuterOaConfigInfoEntity> entityList) {
        if (entityList == null || entityList.isEmpty()) {
            return 0;
        }

        // 检查并为每个实体设置ID
        long currentTimeMillis = System.currentTimeMillis();
        List<OuterOaConfigInfoEntity> processedList = new ArrayList<>(entityList.size());

        for (OuterOaConfigInfoEntity entity : entityList) {
            if (entity.getType() == null || StringUtils.isEmpty(entity.getDcId())) {
                throw new IllegalArgumentException("配置类型和数据中心ID不能为空");
            }

            if (StringUtils.isEmpty(entity.getId())) {
                entity.setId(IdGenerator.get());
            }
            entity.setUpdateTime(currentTimeMillis);
            processedList.add(entity);
        }
        LogUtils.info("batch upsert dcid info:{}",processedList);
        // 执行批量upsert
        return outerOaConfigInfoMapper.batchUpsertByDcIdAndType(processedList);
    }

    public SettingsResult getSettingBindRules(String dcId) {
        OuterOaConfigInfoEntity entity = getEntityByDataCenterId(OuterOaConfigInfoTypeEnum.SETTING_BIND_RULES, dcId);
        if (Objects.isNull(entity) || StringUtils.isEmpty(entity.getConfigInfo())) {
            return null;
        }
        return JSON.parseObject(entity.getConfigInfo(), SettingsResult.class);
    }

    /**
     * 获取不同步组织的自动绑定时,获取字段映射
     */
    public SystemFieldMappingResult.ItemFieldMapping getEmployeeUniqueIdentity(String dcId) {
        OuterOaConfigInfoEntity entity = getEntityByDataCenterId(OuterOaConfigInfoTypeEnum.EMPLOYEE_UNIQUE_IDENTITY, dcId);
        if (Objects.isNull(entity) || StringUtils.isEmpty(entity.getConfigInfo())) {
            return null;
        }
        final SystemFieldMappingResult systemFieldMappingResult = JSON.parseObject(entity.getConfigInfo(), SystemFieldMappingResult.class);
        return systemFieldMappingResult.getItemFieldMappings().stream()
                .filter(itemFieldMapping -> BooleanUtils.isTrue(itemFieldMapping.getMatchUnique()))
                .findFirst().orElse(null);
    }
    // 默认规则
    private OuterOaConfigInfoEntity getOrDefaultRules(String dataCenterId) {
        SettingAccountRulesModel settingAccountRulesModel = new SettingAccountRulesModel();
        settingAccountRulesModel
                .setEmployeeRangeRemoveRule(new SettingAccountRulesModel.EmployeeRangeRemoveRule(true, false));
        settingAccountRulesModel.setBindTypeEnum(BindTypeEnum.manual);
        settingAccountRulesModel.setSyncTypeEnum(EnterpriseConfigAccountSyncTypeEnum.accountBind);
        settingAccountRulesModel.setEmployeeLeaveRule(new SettingAccountRulesModel.EmployeeLeaveRule(true, true));
        OuterOaEnterpriseBindEntity entityById = outerOaEnterpriseBindManager.getEntityById(dataCenterId);
        OuterOaConfigInfoEntity outerOaConfigInfoEntity = null;
        // 需要初始化页面规则
        if (ObjectUtils.isNotEmpty(entityById)) {
            outerOaConfigInfoEntity = OuterOaConfigInfoEntity.builder().id(IdGenerator.get())
                    .channel(entityById.getChannel()).dcId(entityById.getId()).fsEa(entityById.getFsEa())
                    .outEa(entityById.getOutEa()).appId(entityById.getAppId())
                    .type(OuterOaConfigInfoTypeEnum.SETTING_BIND_RULES).createTime(System.currentTimeMillis())
                    .updateTime(System.currentTimeMillis()).configInfo(JSON.toJSONString(settingAccountRulesModel))
                    .build();
            outerOaConfigInfoMapper.batchUpsertByDcIdAndType(Lists.newArrayList(outerOaConfigInfoEntity));
        }
        return outerOaConfigInfoEntity;
    }

    // 默认字段
    private OuterOaConfigInfoEntity getOrDefaultEmployeeMappingsFields(String dataCenterId) {
        OuterOaEnterpriseBindEntity outerOaEnterpriseBindEntity = outerOaEnterpriseBindManager
                .getEntityById(dataCenterId);
        if (ObjectUtils.isEmpty(outerOaEnterpriseBindEntity)) {
            LogUtils.warn("getdata default mappings data center error:{}", dataCenterId);
            return null;
        }
        OuterOaConfigInfoEntity outerOaConfigInfoEntity = OuterOaConfigInfoEntity.builder().id(IdGenerator.get())
                .dcId(outerOaEnterpriseBindEntity.getId()).appId(outerOaEnterpriseBindEntity.getAppId())
                .channel(outerOaEnterpriseBindEntity.getChannel()).fsEa(outerOaEnterpriseBindEntity.getFsEa())
                .createTime(System.currentTimeMillis()).updateTime(System.currentTimeMillis())
                .outEa(outerOaEnterpriseBindEntity.getOutEa()).type(OuterOaConfigInfoTypeEnum.EMPLOYEE_UNIQUE_IDENTITY)
                .build();
        SystemFieldMappingResult mappingResult = outerOaEnterpriseBindEntity.getChannel().buildFieldMapping();

        // 需要判断，自建应用类型，默认以手机号码匹配，其余默认姓名
        String connectInfo = outerOaEnterpriseBindEntity.getConnectInfo();
        BaseConnectorVo baseConnectorVo = JSON.parseObject(connectInfo, BaseConnectorVo.class);
        for (SystemFieldMappingResult.ItemFieldMapping itemFieldMapping : mappingResult.getItemFieldMappings()) {
            if ((itemFieldMapping.getCrmFieldApiName().equals(CRMEmployeeFiledEnum.PHONE.getCode())
                    &&  OuterOaAppInfoTypeEnum.selfBuild==baseConnectorVo.getAppType() )) {
                itemFieldMapping.setMatchUnique(true);
                break;
            }
            if ((itemFieldMapping.getCrmFieldApiName().equals(CRMEmployeeFiledEnum.NAME.getCode())
                    && OuterOaAppInfoTypeEnum.isv==baseConnectorVo.getAppType() )) {
                itemFieldMapping.setMatchUnique(true);
                break;
            }
        }
        for (SystemFieldMappingResult.ItemFieldMapping itemFieldMapping : mappingResult.getItemFieldMappings()) {
            if(StringUtils.isNotEmpty(itemFieldMapping.getOuterI18nKey())){
                String langByEa = i18NStringManager.getLangByEa(itemFieldMapping.getOuterI18nKey(), null, outerOaEnterpriseBindEntity.getFsEa(), itemFieldMapping.getOuterOAFieldLabel());
                itemFieldMapping.setOuterOAFieldLabel(langByEa);
            }
            if(StringUtils.isNotEmpty(itemFieldMapping.getCrmI18nKey())){
                String langByEa = i18NStringManager.getLangByEa(itemFieldMapping.getCrmI18nKey(), null, outerOaEnterpriseBindEntity.getFsEa(), itemFieldMapping.getCrmFieldLabel());
                itemFieldMapping.setCrmFieldLabel(langByEa);
            }
        }
        LogUtils.info("getData default lang:{}",mappingResult.getItemFieldMappings());
        // 保存映射结果到数据库
        outerOaConfigInfoEntity.setConfigInfo(JSONObject.toJSONString(mappingResult));
        outerOaConfigInfoMapper.batchUpsertByDcIdAndType(Lists.newArrayList(outerOaConfigInfoEntity));
        return outerOaConfigInfoEntity;
    }
    @Transactional
    public Result<Void> upsertOpenConnectorConfig(OuterOaEnterpriseBindEntity bindEntity,String configType,Object configData) {
        OuterOaConfigInfoEntity configInfoEntity = outerOaConfigInfoMapper.selectOaConfigforUpdate(bindEntity.getId(), OuterOaConfigInfoTypeEnum.OPEN_CONNECTOR_CONFIG.name());
        if(configInfoEntity==null){
            configInfoEntity = new OuterOaConfigInfoEntity();
            configInfoEntity.setId(IdGenerator.get());
            configInfoEntity.setChannel(bindEntity.getChannel());
            configInfoEntity.setDcId(bindEntity.getId());
            configInfoEntity.setFsEa(bindEntity.getFsEa());
            configInfoEntity.setOutEa(bindEntity.getOutEa());
            configInfoEntity.setAppId(bindEntity.getAppId());
            configInfoEntity.setCreateTime(System.currentTimeMillis());
            configInfoEntity.setUpdateTime(configInfoEntity.getCreateTime());
            Map<String,Object> config= Maps.newHashMap();
            config.put(configType,configData);
            configInfoEntity.setType(OuterOaConfigInfoTypeEnum.OPEN_CONNECTOR_CONFIG);
            configInfoEntity.setConfigInfo(JacksonUtil.toJson(config));
        }else{
            Map<String,Object> config=JacksonUtil.fromJson(configInfoEntity.getConfigInfo(),Map.class);
            config.put(configType,configData);
            configInfoEntity.setConfigInfo(JacksonUtil.toJson(config));
            configInfoEntity.setUpdateTime(System.currentTimeMillis());
        }
        outerOaConfigInfoMapper.batchUpsertByDcIdAndType(Lists.newArrayList(configInfoEntity));
        return Result.newSuccess();
    }
    @Transactional
    public Result<Void> deleteOpenConnectorConfig(OuterOaEnterpriseBindEntity bindEntity, String configType) {
        OuterOaConfigInfoEntity configInfoEntity = outerOaConfigInfoMapper.selectOaConfigforUpdate(bindEntity.getId(), OuterOaConfigInfoTypeEnum.OPEN_CONNECTOR_CONFIG.name());
        if(configInfoEntity==null){
            return Result.newSuccess();
        }else{
            Map<String,Object> config=JacksonUtil.fromJson(configInfoEntity.getConfigInfo(),Map.class);
            config.remove(configType);
            configInfoEntity.setConfigInfo(JacksonUtil.toJson(config));
            configInfoEntity.setUpdateTime(System.currentTimeMillis());
        }
        outerOaConfigInfoMapper.batchUpsertByDcIdAndType(Lists.newArrayList(configInfoEntity));
        return Result.newSuccess();
    }
}
