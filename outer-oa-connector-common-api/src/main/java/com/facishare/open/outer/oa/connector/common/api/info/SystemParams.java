package com.facishare.open.outer.oa.connector.common.api.info;


import javax.validation.constraints.NotNull;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class SystemParams extends LinkedHashMap<String, String> {

    public String getBaseUrl() {
        return get("baseUrl");
    }

    public SystemParams setBaseUrl(String baseUrl) {
        if (baseUrl != null) {
            put("baseUrl", baseUrl);
        }
        return this;
    }


    public static SystemParams of(@NotNull Map<String, String> src) {
        SystemParams systemParams = new SystemParams();
        systemParams.putAll(src);
        return systemParams;
    }
}
