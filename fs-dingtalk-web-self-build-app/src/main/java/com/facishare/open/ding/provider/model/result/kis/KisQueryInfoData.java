package com.facishare.open.ding.provider.model.result.kis;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

/**
 * Created by system on 2018/5/18.
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class KisQueryInfoData<T> extends KisServerBaseData {

    /** 当前获取数据时间节点的最大时间戳 **/
    private Integer timeStamp;

    /** 仅分页模式时返回 **/
    private Integer totalNumber;

    /** 查询结果列表 **/
    private List<T> data;

}
