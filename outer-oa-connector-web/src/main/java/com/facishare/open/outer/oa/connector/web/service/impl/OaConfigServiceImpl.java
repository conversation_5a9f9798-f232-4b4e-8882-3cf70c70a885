package com.facishare.open.outer.oa.connector.web.service.impl;

import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaConfigInfoEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaConfigInfoManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEnterpriseBindManager;
import com.facishare.open.outer.oa.connector.common.api.enums.OuterOaConfigInfoTypeEnum;
import com.facishare.open.outer.oa.connector.common.api.result.Result;
import com.facishare.open.outer.oa.connector.common.api.result.ResultCodeEnum;
import com.facishare.open.outer.oa.connector.web.model.admin.CommonExecuteArg;
import com.facishare.open.outer.oa.connector.web.service.CommonExecuteService;
import com.facishare.rest.core.util.JacksonUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Service("getOaConfigByType")
public class OaConfigServiceImpl implements CommonExecuteService {
    @Autowired
    private OuterOaConfigInfoManager outerOaConfigInfoManager;

    @Override
    public Result<?> executeLogic(CommonExecuteArg arg) {
        String tenantId = arg.getTenantId();
        String params = arg.getParams();
        Map<String, String> dataMap = JacksonUtil.fromJson(params, Map.class);
        String dcId = dataMap.get("dcId");
        if (StringUtils.isBlank(dcId)) {
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }
        String configType = dataMap.get("configType");
        OuterOaConfigInfoEntity configInfo = outerOaConfigInfoManager.getEntityByDataCenterId(OuterOaConfigInfoTypeEnum.OPEN_CONNECTOR_CONFIG, dcId);
        if (configInfo == null || configInfo.getConfigInfo() == null) {
            return Result.newSuccess();
        }
        Map<String, Object> config = JacksonUtil.fromJson(configInfo.getConfigInfo(), Map.class);
        if (configType != null) {
            return Result.newSuccess(config.get(configType));
        } else {
            return Result.newSuccess(config);
        }
    }
}
