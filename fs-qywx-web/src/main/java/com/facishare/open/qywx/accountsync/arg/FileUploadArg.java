package com.facishare.open.qywx.accountsync.arg;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 企微文件上传接口参数模型
 * <AUTHOR>
 * @date 2022-11-03
 */
@Data
public class FileUploadArg implements Serializable {
    /**
     * 纷享企业EA，如果为空，刚默认从cookie中取，如果取不到，则报参数错误
     */
    private String fsEa;
    /**
     * 需要上传到企微的纷享npath文件信息
     */
    private List<FileItem> fileList;
    /**
     * 企微文件类型,可为空，默认为file
     */
    private FileTypeEnum fileType;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class FileItem implements Serializable {
        /**
         * 可上传npath和apath
         */
        private String npath;
        /**
         * 公开的url文件
         */
        private String url;
        /**
         * 文件名称
         */
        private String fileName;
        /**
         * 员工id，apath需要
         */
        private Integer employId;
        /**
         * 业务key，apath需要
         */
        private String business;
        /**
         * 安全组，apath需要
         */
        private String fileSecurityGroup;
    }

    public enum FileTypeEnum {
        file,//最大20MB
        image,//最大10MB，支持JPG,PNG格式
        video,//最大10MB，支持MP4格式
        voice,//2MB，播放长度不超过60s，仅支持AMR格式
    }
}
