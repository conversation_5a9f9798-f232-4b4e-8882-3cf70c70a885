package com.facishare.open.outer.oa.connector.web.openconnetor.model;

import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.facishare.open.outer.oa.connector.i18n.I18nUtil;
import com.facishare.open.outer.oa.connector.web.openconnetor.service.Connector;
import lombok.*;
import lombok.experimental.FieldNameConstants;
import lombok.extern.jackson.Jacksonized;

/**
 * 开放连接器
 *
 * <AUTHOR>
 */
@Data
@Jacksonized
@Builder
@NoArgsConstructor(access = AccessLevel.PACKAGE)
@AllArgsConstructor
@FieldNameConstants
public class OuterConnector implements Connector {

    /**
     * 唯一键
     */
    private String apiName;

    /**
     * 默认名称，i18n名称从apiName获取
     */
    private String defaultName;

    /**
     * 应用模块编码，用于与产品绑定
     */
    private String moduleCode;

    /**
     * 系统名称
     */
    private String systemName;

    /**
     * 图标url
     */
    private String iconUrl;

    /**
     * 用于生成连接信息Num
     * jar包的使用10000+, hub使用20000+，临时hub使用30000+
     */
    private Integer connectorId;

    /**
     * 安装码,,,如果有安装码，优先使用安装码安装，而不使用应用市场id安装。
     * 这时为了适配不挂产品的情况
     */
    private String installId;

    /**
     * 是否需要安装
     */
    private boolean needInstall;
    /**
     * 应用市场id，应用市场(应用)的数据ID
     */
    private String applicationMarketId;



    @Override
    public String getNameI18nKey() {
        return I18nUtil.buildKey("open.connector", apiName);
    }

    @Override
    public ChannelEnum getChannel() {
        return ChannelEnum.standard_oa;
    }

    @Override
    public String getKey() {
        return getApiName();
    }
}
