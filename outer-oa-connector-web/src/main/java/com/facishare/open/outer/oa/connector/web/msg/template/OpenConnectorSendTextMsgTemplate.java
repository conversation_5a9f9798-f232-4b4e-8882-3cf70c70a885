package com.facishare.open.outer.oa.connector.web.msg.template;

import com.alibaba.fastjson2.JSON;
import com.facishare.open.erpdss.outer.oa.connector.base.context.MethodContext;
import com.facishare.open.erpdss.outer.oa.connector.base.context.TemplateResult;
import com.facishare.open.erpdss.outer.oa.connector.base.outer.msg.SendMsgHandlerTemplate;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.outer.oa.connector.common.api.admin.QywxConnectorVo;
import com.facishare.open.outer.oa.connector.common.api.admin.StandardConnectorVo;
import com.facishare.open.outer.oa.connector.common.api.enums.AlertTypeEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.BindStatusEnum;
import com.facishare.open.outer.oa.connector.common.api.result.Result;
import com.facishare.open.outer.oa.connector.web.openconnetor.OpenConnectorManager;
import com.facishare.open.outer.oa.connector.web.openconnetor.model.OuterMsgModel;
import com.facishare.open.outer.oa.connector.web.openconnetor.model.OuterSendTextCardMessageArg;
import com.facishare.open.outer.oa.connector.web.openconnetor.model.OuterSendTextMessageArg;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


/**
 *
 * <AUTHOR>
 * @date 2025/5/26
 */

@Slf4j
@Component
public class OpenConnectorSendTextMsgTemplate extends SendMsgHandlerTemplate {
    @Resource
    private OpenConnectorManager openConnectorManager;


    @Override
    protected void filterMsg(MethodContext context) {
        log.info("OpenConnectorSendTextMsgTemplate.filterMsg,context={}",context);
        OuterSendTextMessageArg sendTextMessagePushArg = context.getData();

        if (ObjectUtils.isEmpty(sendTextMessagePushArg)) {
            context.setResult(TemplateResult.newError(null));
            return;
        }
        OuterOaEnterpriseBindEntity enterpriseBindEntity = sendTextMessagePushArg.getOuterOaEnterpriseBindEntity();
        if (!enterpriseBindEntity.getBindStatus().equals(BindStatusEnum.normal)) {
            log.info("OpenConnectorSendTextMsgTemplate.filterMsg.is not normal,enterpriseBindEntity={}", enterpriseBindEntity);
            context.setResult(TemplateResult.newError(null));
            return;
        }

        StandardConnectorVo standardConnectorVo=enterpriseBindEntity.getConnectParams().getStandard_oa();
        if (!standardConnectorVo.isAlertConfig()) {
            log.info("OpenConnectorSendTextMsgTemplate.filterMsg.is not alert config,standardConnectorVo={}", standardConnectorVo);
            context.setResult(TemplateResult.newError(null));
            return;
        }
        if (!standardConnectorVo.getAlertTypes().contains(AlertTypeEnum.CRM_NOTIFICATION)) {
            log.info("OpenConnectorSendTextMsgTemplate.filterMsg.if not notify type,standardConnectorVo={}", standardConnectorVo);
            context.setResult(TemplateResult.newError(null));
            return;
        }
        Result<Void> voidResult = openConnectorManager.filterTextMsg(sendTextMessagePushArg.getTenantId(), sendTextMessagePushArg.getDcId(),
                sendTextMessagePushArg.getApiName(), sendTextMessagePushArg.getCrmArg());
        if (voidResult.isSuccess()) {
            context.setResult(TemplateResult.newSuccess());
        } else {
            context.setResult(TemplateResult.newError(voidResult.getMsg()));
        }
    }

    @Override
    public void buildMsg(MethodContext context) {
        OuterSendTextMessageArg outerSendTextMessageArg = context.getData();
        Result<OuterMsgModel> result = openConnectorManager.buildTextMsg(outerSendTextMessageArg.getTenantId(), outerSendTextMessageArg.getDcId(),
                outerSendTextMessageArg.getApiName(), outerSendTextMessageArg.getCrmArg());
        if (result.isSuccess()) {
            outerSendTextMessageArg.setOuterModel(result.getData());
            context.setResult(TemplateResult.newSuccess());
        } else {
            context.setResult(TemplateResult.newError(result.getMsg()));
        }
        log.info("OpenConnectorSendTextMsgTemplate.build,context={} result={}", context,result);
    }

    @Override
    public void sendMsg(MethodContext context) {
        OuterSendTextMessageArg outerSendTextMessageArg = context.getData();
        Result<Void> result = openConnectorManager.sendTextMsg(outerSendTextMessageArg.getTenantId(), outerSendTextMessageArg.getDcId(),
                outerSendTextMessageArg.getApiName(), outerSendTextMessageArg.getOuterModel());
        if (result.isSuccess()) {
            context.setResult(TemplateResult.newSuccess());
        } else {
            context.setResult(TemplateResult.newError(result.getMsg()));
        }
        log.info("OpenConnectorSendTextMsgTemplate.sendMsg,context={} result={}", context,result);
    }
}
