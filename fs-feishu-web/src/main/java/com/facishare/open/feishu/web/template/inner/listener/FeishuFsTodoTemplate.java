//package com.facishare.open.feishu.web.template.inner.listener;
//
//import com.facishare.open.erpdss.outer.oa.connector.base.context.MethodContext;
//import com.facishare.open.erpdss.outer.oa.connector.base.context.TemplateResult;
//import com.facishare.open.erpdss.outer.oa.connector.base.inner.todo.FsTodoTemplate;
//import com.facishare.open.feishu.syncapi.result.Result;
//import com.facishare.open.feishu.syncapi.service.ExternalTodoMsgService;
//import com.fxiaoke.message.extrnal.platform.model.arg.CreateTodoArg;
//import com.fxiaoke.message.extrnal.platform.model.arg.DealTodoArg;
//import com.fxiaoke.message.extrnal.platform.model.arg.DeleteTodoArg;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//
//@Slf4j
//@Component
//public class FeishuFsTodoTemplate extends FsTodoTemplate {
//    @Resource
//    private ExternalTodoMsgService externalTodoMsgService;
//
//    @Override
//    public void createTodo(MethodContext context) {
//        log.info("FeishuFsTodoTemplate.createTodo,context={}",context);
//        CreateTodoArg createTodoArg = context.getData();
//        Result<Void> result = externalTodoMsgService.createTodo(createTodoArg);
//        log.info("FeishuFsTodoTemplate.createTodo,result={}",result);
//        context.setResult(TemplateResult.newSuccess(result));
//    }
//
//    @Override
//    public void dealTodo(MethodContext context) {
//        log.info("FeishuFsTodoTemplate.dealTodo,context={}",context);
//        DealTodoArg dealTodoArg = context.getData();
//        Result<Void> result = externalTodoMsgService.dealTodo(dealTodoArg);
//        log.info("FeishuFsTodoTemplate.dealTodo,result={}",result);
//        context.setResult(TemplateResult.newSuccess(result));
//    }
//
//    @Override
//    public void deleteTodo(MethodContext context) {
//        log.info("FeishuFsTodoTemplate.deleteTodo,context={}",context);
//        DeleteTodoArg deleteTodoArg = context.getData();
//        Result<Void> result = externalTodoMsgService.deleteTodo(deleteTodoArg);
//        log.info("FeishuFsTodoTemplate.deleteTodo,result={}",result);
//        context.setResult(TemplateResult.newSuccess(result));
//    }
//}
