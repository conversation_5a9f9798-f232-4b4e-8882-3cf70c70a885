<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xmlns="http://www.springframework.org/schema/beans"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans.xsd
       http://code.alibabatech.com/schema/dubbo
       http://code.alibabatech.com/schema/dubbo/dubbo.xsd">

    <dubbo:application name="${dubbo.application.name}"/>
    <dubbo:registry address="${dubbo.registry.address}" file="${dubbo.registry.file}" timeout="120000"/>
    <dubbo:consumer check="false" timeout="15000" filter="tracerpc"/>

    <dubbo:reference id="activeSessionAuthorizeService"
                     interface="com.facishare.asm.api.service.ActiveSessionAuthorizeService" />

    <dubbo:reference id="enterpriseEditionService"
                     interface="com.facishare.uc.api.service.EnterpriseEditionService"
                     protocol="dubbo"
                     timeout="3000" />

<!--    <dubbo:reference interface="com.facishare.open.ding.api.service.AuthService"-->
<!--                     group="#{systemProperties['dingtalkinnergroup']}"-->
<!--                     id="authService"-->
<!--                     version="${dubbo.provider.version}"/>-->

<!--    <dubbo:reference interface="com.facishare.open.ding.api.service.EnterpriseService"-->
<!--                     group="#{systemProperties['dingtalkinnergroup']}"-->
<!--                     id="enterpriseService"-->
<!--                     version="${dubbo.provider.version}"-->
<!--                     timeout="15000"/>-->

<!--    <dubbo:reference interface="com.facishare.open.ding.api.service.ObjectMappingService"-->
<!--                     group="#{systemProperties['dingtalkinnergroup']}"-->
<!--                     id="objectMappingService"-->
<!--                     version="${dubbo.provider.version}"-->
<!--                     timeout="15000">-->
<!--        <dubbo:method name="updateEmp" async="true"/>-->
<!--    </dubbo:reference>-->

<!--    <dubbo:reference interface="com.facishare.open.ding.api.service.RedisDingService"-->
<!--                     group="#{systemProperties['dingtalkinnergroup']}"-->
<!--                     id="redisDingService"-->
<!--                     version="${dubbo.provider.version}"/>-->

<!--    <dubbo:reference interface="com.facishare.open.ding.api.service.SyncLogService"-->
<!--                     group="#{systemProperties['dingtalkinnergroup']}"-->
<!--                     id="syncLogService"-->
<!--                     version="${dubbo.provider.version}"/>-->

    <dubbo:reference id="ssoLoginService" interface="com.facishare.userlogin.api.service.SSOLoginService"/>

<!--    <dubbo:reference interface="com.facishare.open.ding.api.service.ExternalMessageService"-->
<!--                     id="externalMessageServiceImpl"-->
<!--                     group="dingtalk"/>-->
<!--    <dubbo:reference interface="com.facishare.open.ding.api.service.DingtalkUserService"-->
<!--                     group="#{systemProperties['dingtalkinnergroup']}"-->
<!--                     id="dingtalkUserService"-->
<!--                     protocol="dubbo"-->
<!--                     version="${dubbo.provider.version}"/>-->

    <dubbo:reference id="fsEmployeeServiceProxy"
                     interface="com.facishare.open.order.contacts.proxy.api.service.FsEmployeeServiceProxy"
                     protocol="dubbo"
                     version="1.0"
                     check="false"/>

<!--    <dubbo:reference id="toolsService"-->
<!--                     interface="com.facishare.open.ding.api.service.ToolsService"-->
<!--                     protocol="dubbo"-->
<!--                     version="1.0"-->
<!--                     check="false"/>-->


    <!-- fs-dingtalk-provider dubbo-consumer.xml-->
    <!-- 应用管理员信息 -->
    <dubbo:reference id="openAppAdminService"
                     interface="com.facishare.open.app.center.api.service.OpenAppAdminService" timeout="3000" version="1.3"/>

    <dubbo:reference id="appAdminService"
                     interface="com.facishare.open.app.center.api.service.QueryAppAdminService" timeout="5000" version="1.0"/>

    <!-- 员工 -->
    <!--    <dubbo:reference id="employeeService"-->
    <!--                     interface="com.facishare.open.addressbook.api.EmployeeService" timeout="5000" version="1.1"/>-->
    <!--    <dubbo:reference id="circleService"-->
    <!--                     interface="com.facishare.open.addressbook.api.CircleService" timeout="10000" version="1.1"/>-->
    <!-- 开平消息服务 -->
    <dubbo:reference id="sendMessageService" interface="com.facishare.open.msg.service.SendMessageService" version="1.0" timeout="10000" />

    <!--    <dubbo:reference id="employeeProviderService"-->
    <!--                     interface="com.facishare.organization.api.service.EmployeeProviderService" protocol="dubbo"-->
    <!--                     version="5.7"/>-->

    <!--    <dubbo:reference id="employeeApiService"-->
    <!--                     interface="com.facishare.organization.api.service.EmployeeService" protocol="dubbo"-->
    <!--                     version="5.7"/>-->

    <!--    <dubbo:reference id="employeeAdapterService"-->
    <!--                     interface="com.facishare.organization.adapter.api.service.EmployeeService"/>-->

    <!--    <dubbo:reference id="departmentService"-->
    <!--                     interface="com.facishare.organization.adapter.api.service.DepartmentService"/>-->

    <!--    <dubbo:reference id="departmentPrincipalService"-->
    <!--                     interface="com.facishare.organization.adapter.api.service.DepartmentPrincipalService"/>-->

    <!--    <dubbo:reference id="enterpriseConfigService"-->
    <!--                     interface="com.facishare.organization.adapter.api.config.service.EnterpriseConfigService"/>-->

    <!--    <dubbo:reference id="departmentProviderService"-->
    <!--                     interface="com.facishare.organization.api.service.DepartmentProviderService" protocol="dubbo"-->
    <!--                     version="5.7"/>-->


<!--    <dubbo:reference id="enterpriseEditionService" interface="com.facishare.uc.api.service.EnterpriseEditionService" protocol="dubbo" timeout="3000"/>-->
    <!-- fs-dingtalk-provider dubbo-consumer.xml-->


</beans>