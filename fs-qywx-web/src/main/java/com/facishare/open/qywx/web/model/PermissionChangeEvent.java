package com.facishare.open.qywx.web.model;//package com.facishare.open.qywx.accountsync.model;
//
//import lombok.AllArgsConstructor;
//import lombok.Data;
//import lombok.NoArgsConstructor;
//import lombok.ToString;
//
//import java.util.ArrayList;
//import java.util.List;
//
///**
// * Created by fengyh on 2018/6/14.
// */
//
//@NoArgsConstructor
//@AllArgsConstructor
//@Data
//@ToString
//public class PermissionChangeEvent {
//    public static final int FLAG_ADD_PERMISSION = 1;        //添加权限
//    public static final int FLAG_DELETE_PERMISSION = 2;     //删除权限
//    public static final int FLAG_UPDATE_PERMISSION = 3;     //修改权限
//
//    private int enterpriseId;
//    private String enterpriseAccount;
//    private String permissionCode;
//    private List<com.facishare.organization.api.event.organizationChangeEvent.PermissionChangeEvent.ManageRange> ranges = new ArrayList<>();
//    private long updateTime;
//
//    @Data
//    @AllArgsConstructor
//    @NoArgsConstructor
//    @ToString
//    public static class ManageRange {
//        private int employeeId;
//        private List<Integer> departmentIds = new ArrayList<>();
//    }
//}
