package com.facishare.open.outer.oa.connector.web.msg.template;

import com.alibaba.fastjson2.JSON;
import com.facishare.open.erpdss.outer.oa.connector.base.context.MethodContext;
import com.facishare.open.erpdss.outer.oa.connector.base.context.TemplateResult;
import com.facishare.open.erpdss.outer.oa.connector.base.outer.msg.SendMsgHandlerTemplate;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.outer.oa.connector.common.api.admin.QywxConnectorVo;
import com.facishare.open.outer.oa.connector.common.api.admin.StandardConnectorVo;
import com.facishare.open.outer.oa.connector.common.api.enums.AlertTypeEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.BindStatusEnum;
import com.facishare.open.outer.oa.connector.common.api.result.Result;
import com.facishare.open.outer.oa.connector.web.openconnetor.OpenConnectorManager;
import com.facishare.open.outer.oa.connector.web.openconnetor.model.OuterCreateTodoArg;
import com.facishare.open.outer.oa.connector.web.openconnetor.model.OuterMsgModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 *
 * <AUTHOR>
 * @date 2025/5/26
 */
@Slf4j
@Component
public class OpenConnectorCreateTodoTemplate extends SendMsgHandlerTemplate {
    @Resource
    private OpenConnectorManager openConnectorManager;


    @Override
    protected void filterMsg(MethodContext context) {
        log.info("OpenConnectorCreateTodoTemplate.filterMsg,context={}",context);
        OuterCreateTodoArg createTodoPushArg = context.getData();

        if (ObjectUtils.isEmpty(createTodoPushArg)) {
            context.setResult(TemplateResult.newError(null));
            return;
        }
        OuterOaEnterpriseBindEntity enterpriseBindEntity = createTodoPushArg.getOuterOaEnterpriseBindEntity();
        if (!enterpriseBindEntity.getBindStatus().equals(BindStatusEnum.normal)) {
            log.info("OpenConnectorCreateTodoTemplate.filterMsg.is not normal,enterpriseBindEntity={}", enterpriseBindEntity);
            context.setResult(TemplateResult.newError(null));
            return;
        }
        StandardConnectorVo standardConnectorVo=enterpriseBindEntity.getConnectParams().getStandard_oa();
        if (!standardConnectorVo.isAlertConfig()) {
            log.info("OpenConnectorCreateTodoTemplate.filterMsg.is not alert config,standardConnectorVo={}", standardConnectorVo);
            context.setResult(TemplateResult.newError(null));
            return;
        }
        if (!standardConnectorVo.getAlertTypes().contains(AlertTypeEnum.CRM_TODO)) {
            log.info("OpenConnectorCreateTodoTemplate.filterMsg.if not todo type,qywxConnectorVo={}", standardConnectorVo);
            context.setResult(TemplateResult.newError(null));
            return;
        }
        Result<Void> voidResult = openConnectorManager.filterCreateTodoMsg(createTodoPushArg.getTenantId(), createTodoPushArg.getDcId(), createTodoPushArg.getApiName(), createTodoPushArg.getCrmArg());
        if(voidResult.isSuccess()){
            context.setResult(TemplateResult.newSuccess());
        }else{
            context.setResult(TemplateResult.newError(voidResult.getMsg()));
        }

    }

    @Override
    public void buildMsg(MethodContext context) {
        log.info("OpenConnectorCreateTodoTemplate.build,context={}",context);
        OuterCreateTodoArg createTodoPushArg = context.getData();
        Result<OuterMsgModel> result = openConnectorManager.buildCreateTodoMsg(createTodoPushArg.getTenantId(), createTodoPushArg.getDcId(), createTodoPushArg.getApiName(), createTodoPushArg.getCrmArg());
        if(result.isSuccess()){
            createTodoPushArg.setOuterModel(result.getData());
            context.setResult(TemplateResult.newSuccess());
        }else{
            context.setResult(TemplateResult.newError(result.getMsg()));
        }
    }

    @Override
    public void sendMsg(MethodContext context) {
        log.info("OpenConnectorCreateTodoTemplate.sendMsg,context={}",context);
        OuterCreateTodoArg createTodoPushArg = context.getData();
        Result<Void> result = openConnectorManager.sendCreateTodoMsg(createTodoPushArg.getTenantId(), createTodoPushArg.getDcId(), createTodoPushArg.getApiName(), createTodoPushArg.getOuterModel());
        if(result.isSuccess()){
            context.setResult(TemplateResult.newSuccess());
        }else{
            context.setResult(TemplateResult.newError(result.getMsg()));
        }
    }
}
