package com.facishare.open.ding.provider.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.curator.framework.CuratorFramework;
import org.apache.curator.framework.CuratorFrameworkFactory;
import org.apache.curator.framework.recipes.locks.InterProcessMutex;
import org.apache.curator.retry.RetryNTimes;

import java.util.concurrent.TimeUnit;

/**
 * Created by system on 2018/5/10.
 */
@Slf4j
public class LockUtils {

    private static CuratorFramework client;

    private static String basePath;

    private static ThreadLocal<InterProcessMutex> localLock = new ThreadLocal<>();

    private static ThreadLocal<String> localGroup = new ThreadLocal<>();

    /**
     * 构建基础路径
     * @param zkUrl
     * @param basePath
     */
    public LockUtils(String zkUrl, String basePath) {
        LockUtils.basePath = basePath;
        if (basePath.charAt(0) != 47) {
            LockUtils.basePath = "/" + basePath;
        }
        client = CuratorFrameworkFactory.newClient(zkUrl, new RetryNTimes(10, 5000));
        client.start();
    }

    /**
     * 获取锁,这里必须是同一个线程,使用起来比较方便
     * @param group1  同一个group下,只有一个线程能操作
     * @param maxWait 等待时间，单位：秒
     * @return
     */
    public static boolean canExecute(String group1, int maxWait) {
        String group = group1.replaceAll("/", "-");
        InterProcessMutex lock = new InterProcessMutex(client, basePath + "/" + group);
        localLock.set(lock);
        localGroup.set(group);
        try {
            if (lock.acquire(maxWait, TimeUnit.SECONDS)) {
                return true;
            }
        } catch (Exception e) {
            log.error("canExecute method: get lock error:", e);
            return false;
        }
        //获取分布式锁超时
        return false;
    }

    /**
     * 释放锁
     */
    public static void releaseLock() {
        try {
            InterProcessMutex lock = localLock.get();
            String group = localGroup.get();
            if (lock.isAcquiredInThisProcess()) {
                lock.release();
                try {
                    //如果这个锁不需要用了,则把文件夹删掉,如果还在继续使用,那么这里自然会报错,不会删除
                    client.getZookeeperClient().getZooKeeper().delete(basePath + "/" + group, -1);
                } catch (Exception e) {
                    //do nothing
                }
            }
        } catch (Exception e) {
            log.error("release lock error: ", e);
        }
    }

}
