package com.facishare.open.ding.web.manager;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.event.AnalysisEventListener;
import com.facishare.open.ding.web.manager.excel.ReadExcel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.InputStream;

/**
 * <AUTHOR>
 * @date 2020/4/20
 **/
@Service
@Slf4j
public class FileManager {
//    @Autowired
//    private NFileStorageService storageService;

//    public String uploadTnFile(String ea, Integer userId, byte[] bytes) {
//        NTempFileUpload.Arg arg = new NTempFileUpload.Arg();
//        arg.setBusiness(CommonConstant.ERP_SYNC_DATA_BUSINESS);
//        arg.setData(bytes);
//        arg.setEa(ea);
//        arg.setSourceUser("E." + userId);
//        NTempFileUpload.Result result = storageService.nTempFileUpload(arg, ea);
//        if (result != null && result.getTempFileName() != null) {
//            return result.getTempFileName();
//        } else {
//            log.error("uploadTnFile get result null");
//        }
//        throw new ErpSyncDataException(ResultCodeEnum.FILE_UPLOAD_FAILED);
//    }

    public <E> void readExcel(ReadExcel.Arg<E> arg) {
        InputStream inputStream = arg.getInputStream();
        AnalysisEventListener<E> listener = arg.getExcelListener();
        EasyExcel.read(inputStream, arg.getType(), listener).sheet().doRead();
    }

    public <E> void readExcelBySheetName(ReadExcel.Arg<E> arg) {
        InputStream inputStream = arg.getInputStream();
        AnalysisEventListener<E> listener = arg.getExcelListener();
        EasyExcel.read(inputStream, arg.getType(), listener).sheet(arg.getSheetName()).doRead();
    }
}
