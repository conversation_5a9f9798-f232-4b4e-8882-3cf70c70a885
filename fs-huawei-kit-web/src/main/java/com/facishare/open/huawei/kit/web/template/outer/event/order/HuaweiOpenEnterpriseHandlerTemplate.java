package com.facishare.open.huawei.kit.web.template.outer.event.order;

import com.facishare.enterprise.event.EnterpriseAddEvent;
import com.facishare.open.erpdss.outer.oa.connector.base.context.MethodContext;
import com.facishare.open.erpdss.outer.oa.connector.base.context.TemplateResult;
import com.facishare.open.erpdss.outer.oa.connector.base.outer.event.order.OpenEnterpriseHandlerTemplate;
import com.facishare.open.huawei.kit.web.arg.CreateCustomerAndUpdateMappingArg;
import com.facishare.open.huawei.kit.web.arg.CreateOrderArg;
import com.facishare.open.huawei.kit.web.config.ConfigCenter;
import com.facishare.open.huawei.kit.web.info.AllOrgSyncInfo;
import com.facishare.open.huawei.kit.web.info.AuthSyncInfo;
import com.facishare.open.huawei.kit.web.info.HuaweiOrderInfo;
import com.facishare.open.huawei.kit.web.info.SingleOrgSyncInfo;
import com.facishare.open.huawei.kit.web.model.HuaweiOrderDataModel;
import com.facishare.open.huawei.kit.web.result.result.Result;
import com.facishare.open.huawei.kit.web.result.Result2;
import com.facishare.open.huawei.kit.web.result.ResultCodeEnum;
import com.facishare.open.huawei.kit.web.service.CorpService;
import com.facishare.open.huawei.kit.web.service.EnterpriseBindService;
import com.facishare.open.huawei.kit.web.service.HuaweiContactsService;
import com.facishare.open.huawei.kit.web.service.HuaweiOrderService;
import com.facishare.open.huawei.kit.web.templateData.KitVerifyTemplateData;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEnterpriseBindManager;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaEnterpriseBindParams;
import com.facishare.open.order.contacts.proxy.api.datasource.RedisDataSource;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.open.outer.oa.connector.common.api.constants.GlobalValue;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class HuaweiOpenEnterpriseHandlerTemplate extends OpenEnterpriseHandlerTemplate {
    @Resource
    private HuaweiOrderService huaweiOrderService;
    @Resource
    private CorpService corpService;

    @Resource
    private EnterpriseBindService enterpriseBindService;

    @Resource
    private RedisDataSource redisDataSource;

    @Resource
    private HuaweiContactsService huaweiContactsService;

    @Resource
    private HuaweiSaveOrderHandlerTemplate huaweiSaveOrderHandlerTemplate;

    @Resource
    private RedissonClient redissonClient;
    @Resource
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;

    @Override
    public void saveOrder(MethodContext context) {
        log.info("HuaweiOpenEnterpriseHandlerTemplate.saveOrder,context={}",context);

        KitVerifyTemplateData huaweiOuterKitTemplate = context.getData();
        HuaweiOrderDataModel huaweiOrderDataModel = new HuaweiOrderDataModel();
        huaweiOrderDataModel.setInstanceId(huaweiOuterKitTemplate.getIsvProduceReq().get("businessId").toString());
        huaweiOrderDataModel.setOrderId(huaweiOuterKitTemplate.getIsvProduceReq().get("orderId").toString());

        Result<HuaweiOrderInfo> result = huaweiOrderService.saveOrder(huaweiOrderDataModel);

        log.info("HuaweiOpenEnterpriseHandlerTemplate.saveOrder,result={}",result);

        if(result.isSuccess()) {
            //先看下是否已经绑定过了
            List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntitiesByOutEa(ChannelEnum.huawei, huaweiOrderDataModel.getTenantId(), ConfigCenter.HUAWEI_APP_ID);
            if(CollectionUtils.isNotEmpty(enterpriseBindEntities)) {
                log.info("HuaweiOpenEnterpriseHandlerTemplate.saveOrder,isEnterpriseBind,enterpriseBindEntities={}",enterpriseBindEntities);
                TemplateResult refreshTemplateResult = huaweiSaveOrderHandlerTemplate.execute(huaweiOrderDataModel);
                log.info("HuaweiOpenEnterpriseHandlerTemplate.saveOrder,refreshTemplateResult={}",refreshTemplateResult);
                context.setResult(TemplateResult.newErrorData(Result2.newSuccess()));
                return;
            }

            String fsEa = huaweiOrderService.genFsEa(huaweiOrderDataModel.getEnterpriseName()).getData();
            log.info("HuaweiOpenEnterpriseHandlerTemplate.saveOrder,genFsEa,fsEa={}",fsEa);

            CreateCustomerAndUpdateMappingArg customer = new CreateCustomerAndUpdateMappingArg();
            customer.setFsEa(fsEa);
            //使用tenantId作为outEid
            customer.setOutEid(huaweiOrderDataModel.getTenantId());
            customer.setOutEa(huaweiOrderDataModel.getTenantId());
            customer.setEnterpriseName(huaweiOrderDataModel.getEnterpriseName());
            //新增时没有userId，所以全部取userName作为outUserId
            customer.setInstallerUserId(result.getData().getBuyerInfo().getUserName());
            customer.setInstallerName(result.getData().getBuyerInfo().getUserName());
            customer.setInstallerMobilePhone(result.getData().getBuyerInfo().getMobilePhone());

            Map<String,Object> contextMap = new HashMap<>();
            contextMap.put("order",result.getData());
            contextMap.put("customer",customer);

            log.info("HuaweiOpenEnterpriseHandlerTemplate.saveOrder,contextMap={}",contextMap);

            context.setData(contextMap);
            context.setResult(TemplateResult.newSuccess(Result2.newSuccess()));
        } else {
            context.setResult(TemplateResult.newErrorData(Result2.newSuccess()));
        }
        log.info("HuaweiOpenEnterpriseHandlerTemplate.saveOrder,context.end={}",context);
    }

    @Override
    public void initEnterpriseAndAdminMapping(MethodContext context) {
        log.info("FeishuOpenEnterpriseHandlerTemplate.initEnterpriseAndAdminMapping,context={}",context);
        //暂时不实现
    }

    @Override
    public void createFsCustomerAndUpdateEnterpriseAndAdminMapping(MethodContext context) {
        log.info("HuaweiOpenEnterpriseHandlerTemplate.createFsCustomerAndUpdateEnterpriseAndAdminMapping,context={}",context);

        Map<String,Object> contextMap = context.getData();
        CreateCustomerAndUpdateMappingArg arg = (CreateCustomerAndUpdateMappingArg) contextMap.get("customer");
        Result<Void> result = huaweiOrderService.createCustomerAndUpdateMapping(arg);
        log.info("HuaweiOpenEnterpriseHandlerTemplate.createFsCustomerAndUpdateEnterpriseAndAdminMapping,result={}",result);

        if(result.isSuccess()) {
            context.setResult(TemplateResult.newSuccess(Result2.newSuccess()));
        } else {
            context.setResult(TemplateResult.newErrorData(Result2.newError(ResultCodeEnum.OTHER_INNER_ERROR)));
        }
        log.info("HuaweiOpenEnterpriseHandlerTemplate.createFsCustomerAndUpdateEnterpriseAndAdminMapping,context.end={}",context);
    }

    @Override
    public void createFsOrder(MethodContext context) {
        log.info("HuaweiOpenEnterpriseHandlerTemplate.createFsOrder,context={}",context);
        Map<String,Object> contextMap = context.getData();
        HuaweiOrderInfo order = (HuaweiOrderInfo) contextMap.get("order");
        CreateCustomerAndUpdateMappingArg customer = (CreateCustomerAndUpdateMappingArg) contextMap.get("customer");


        CreateOrderArg createOrderArg = new CreateOrderArg();
        createOrderArg.setFsEa(customer.getFsEa());
        createOrderArg.setOutEa(customer.getOutEa());
        createOrderArg.setOrderId(order.getOrderId());

        log.info("HuaweiOpenEnterpriseHandlerTemplate.createFsOrder,createOrderArg={}",createOrderArg);
        Result<Void> result = huaweiOrderService.createOrder(createOrderArg);
        log.info("HuaweiOpenEnterpriseHandlerTemplate.createFsOrder,result={}",result);
        if(result.isSuccess()) {
            context.setResult(TemplateResult.newSuccess(Result2.newSuccess()));
        } else {
            context.setResult(TemplateResult.newErrorData(Result2.newError(ResultCodeEnum.OTHER_INNER_ERROR)));
        }
        log.info("HuaweiOpenEnterpriseHandlerTemplate.createFsOrder,context.end={}",context);
    }

    @Override
    public boolean isEnterpriseBind(String ea) {
        log.info("HuaweiOpenEnterpriseHandlerTemplate.isEnterpriseBind,ea={}",ea);
        return huaweiOrderService.isEnterpriseBind(ea).getData();
    }

//    @Override
//    public TemplateResult onEnterpriseOpened(Object data) {
//        return super.onEnterpriseOpened(data);
//    }

    @Override
    public void updateEnterpriseAndAdminMapping(MethodContext context) {
        log.info("HuaweiOpenEnterpriseHandlerTemplate.updateEnterpriseAndAdminMapping,context={}",context);
        EnterpriseAddEvent enterpriseAddEvent = context.getData();
        Result<Void> result = huaweiOrderService.updateEnterpriseAndAdminMapping(enterpriseAddEvent.getEnterpriseAccount(),
                GlobalValue.FS_ADMIN_USER_ID + "");
        log.info("HuaweiOpenEnterpriseHandlerTemplate.updateEnterpriseAndAdminMapping,result={}",result);
    }

    @Override
    public void sendWelcomeMsg(MethodContext context) {
        log.info("HuaweiOpenEnterpriseHandlerTemplate.sendWelcomeMsg,context={}",context);
    }

    @Override
    public void initEnterpriseContacts(MethodContext context) {
        log.info("HuaweiOpenEnterpriseHandlerTemplate.initEnterpriseContacts,context={}",context);
        Gson gson = new Gson();
        EnterpriseAddEvent enterpriseAddEvent = context.getData();
        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntities(OuterOaEnterpriseBindParams.builder().channel(ChannelEnum.huawei).fsEa(enterpriseAddEvent.getEnterpriseAccount()).build());

        if(CollectionUtils.isEmpty(enterpriseBindEntities)) {
            return;
        }
        String tem = redisDataSource.getRedisClient().get("huawei-kit2-sync-" + enterpriseBindEntities.get(0).getOutEa());
        if(StringUtils.isEmpty(tem)) {
            return;
        }

        Map<String, Object> temSyncMap = gson.fromJson(tem, new TypeToken<Map<String, Object>>() {
        });

        log.info("HuaweiOpenEnterpriseHandlerTemplate.initEnterpriseContacts,temSyncMap={}",temSyncMap);

        if(temSyncMap.containsKey("allOrgSync")) {
            AllOrgSyncInfo allOrgSyncInfo = gson.fromJson(temSyncMap.get("allOrgSync").toString(), new TypeToken<AllOrgSyncInfo>() {
            });
            huaweiContactsService.syncAllDepartment(allOrgSyncInfo);
        }

        if(temSyncMap.containsKey("singleOrgSync")) {
            SingleOrgSyncInfo singleOrgSyncInfo = gson.fromJson(temSyncMap.get("singleOrgSync").toString(), new TypeToken<SingleOrgSyncInfo>() {
            });
            huaweiContactsService.syncDepartment(singleOrgSyncInfo);
        }

        if(temSyncMap.containsKey("authSync")) {
            AuthSyncInfo authSyncInfo = gson.fromJson(temSyncMap.get("authSync").toString(), new TypeToken<AuthSyncInfo>() {
            });
            huaweiContactsService.syncEmployee(authSyncInfo);
        }
    }
}
