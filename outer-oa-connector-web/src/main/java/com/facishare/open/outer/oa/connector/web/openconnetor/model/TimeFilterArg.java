package com.facishare.open.outer.oa.connector.web.openconnetor.model;

import lombok.Data;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 * @date 2025/5/14
 */
@Data
public class TimeFilterArg implements Serializable {
    private static final long serialVersionUID = -3408076980991440156L;

    /**
     * 对象ApiName
     */
    private String objAPIName;
    /**
     * 开始时间；返回maxTime后，下一页会修改该值
     */
    private Long startTime;
    /**
     * 结束时间
     */
    private Long endTime;
    /**
     * offset
     */
    private Integer offset = 0;
    /**
     * 限制
     */
    private Integer limit = 100;

    /**
     * 上次最大Id，返回maxId后，下一页调用会传输改值
     */
    private String lastMaxId;

    /**
     * 自定义查询条件
     */
    private String customFilterString;
}
