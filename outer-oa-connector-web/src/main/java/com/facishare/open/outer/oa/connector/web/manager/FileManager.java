package com.facishare.open.outer.oa.connector.web.manager;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.event.AnalysisEventListener;
import com.facishare.converter.EIEAConverter;
import com.facishare.fsi.proxy.model.warehouse.n.fileupload.NDownloadFile;
import com.facishare.fsi.proxy.model.warehouse.n.fileupload.NTempFileUpload;
import com.facishare.fsi.proxy.service.NFileStorageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;
import java.io.InputStream;

/**
 * 文件管理器
 */
@Component
@Slf4j
public class FileManager {

    @Autowired
    private NFileStorageService storageService;

    @Autowired
    private EIEAConverter eieaConverter;

    /**
     * 文件业务类型
     */
    private final String BUSINESS_TYPE = "OUTER_OA_CONNECTOR";

    /**
     * 上传临时文件
     *
     * @param ea     企业EA
     * @param userId 用户ID
     * @param bytes  文件字节数组
     * @return 文件路径
     */
    public String uploadTempFile(String ea, Integer userId, byte[] bytes) {
        return uploadTempFile(ea, userId, null, bytes);
    }

    /**
     * 上传临时文件
     *
     * @param ea       企业EA
     * @param userId   用户ID
     * @param fileName 文件名
     * @param bytes    文件字节数组
     * @return 文件路径
     */
    public String uploadTempFile(String ea, Integer userId, String fileName, byte[] bytes) {
        NTempFileUpload.Arg arg = new NTempFileUpload.Arg();
        arg.setBusiness(BUSINESS_TYPE);
        if (!StringUtils.isEmpty(fileName)) {
            arg.setOriginName(fileName);
        }
        arg.setData(bytes);
        arg.setEa(ea);
        arg.setSourceUser("E." + userId);
        NTempFileUpload.Result result = storageService.nTempFileUpload(arg, ea);
        log.info("上传临时文件，result={}", result);
        if (result != null && result.getTempFileName() != null) {
            return result.getTempFileName();
        }
        return null;
    }

    /**
     * 下载文件
     *
     * @param ea       企业EA
     * @param userId   用户ID
     * @param filePath 文件路径
     * @return 文件字节数组
     */
    public byte[] downloadFile(String ea, Integer userId, String filePath) {
        NDownloadFile.Arg arg = new NDownloadFile.Arg();
        arg.setEa(ea);
        arg.setDownloadUser("E." + ea + "." + userId);
        arg.setnPath(filePath);
        NDownloadFile.Result result = storageService.nDownloadFile(arg, ea);
        if (result != null) {
            return result.getData();
        } else {
            log.error("下载文件失败，filePath={}", filePath);
            return null;
        }
    }

    /**
     * 读取Excel
     *
     * @param inputStream 输入流
     * @param clazz       数据类型
     * @param listener    监听器
     * @param <T>         数据类型
     */
    public <T> void readExcel(InputStream inputStream, Class<T> clazz, AnalysisEventListener<T> listener) {
        EasyExcel.read(inputStream, clazz, listener).sheet().doRead();
    }
}