package com.facishare.open.qywx.web.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.facishare.converter.EIEAConverter;
import com.facishare.open.erpdss.outer.oa.connector.base.context.MethodContext;
import com.facishare.open.oa.base.dbproxy.ch.manager.OAConnectorOpenDataManager;
import com.facishare.open.oa.base.dbproxy.ch.manager.OAConnectorOpenDataModel;
import com.facishare.open.oa.base.dbproxy.mongo.dao.OaConnectorSyncEventDataMongoDao;
import com.facishare.open.oa.base.dbproxy.mongo.document.OaConnectorSyncEventDataDoc;
import com.facishare.open.oa.base.dbproxy.pg.entity.*;
import com.facishare.open.oa.base.dbproxy.pg.manager.*;
import com.facishare.open.oa.base.dbproxy.pg.params.*;
import com.facishare.open.outer.oa.connector.common.api.enums.*;
import com.facishare.open.outer.oa.connector.common.api.info.EnterpriseTrialInfo;
import com.facishare.open.outer.oa.connector.common.api.info.FsEmployeeDetailInfo;
import com.facishare.open.outer.oa.connector.common.api.params.QueryOaConnectorSyncEventDataArg;
import com.facishare.open.outer.oa.connector.common.api.params.QyweixinAppInfoParams;
import com.facishare.open.outer.oa.connector.common.api.admin.QywxConnectorVo;
import com.facishare.open.qywx.accountsync.model.IntelligentAppInfoResult;
import com.facishare.open.qywx.save.arg.QywxQueryMessageArg;
import com.facishare.open.qywx.save.arg.QywxQueryMessageArgByEa;
import com.facishare.open.qywx.save.result.QueryMessageIdResult;
import com.facishare.open.qywx.save.service.AutoPullMessageService;
import com.facishare.open.qywx.web.arg.QywxActviceCodeArg;
import com.facishare.open.qywx.web.info.ConversationArchiveInfo;
import com.facishare.open.qywx.web.model.result.QywxTransferResult;
import com.facishare.open.qywx.web.template.model.JsApiModel;
import com.facishare.open.order.contacts.proxy.api.datasource.RedisDataSource;
import com.facishare.open.order.contacts.proxy.api.service.FsEmployeeServiceProxy;
import com.facishare.open.qywx.accountbind.service.QyweixinAccountBindService;
import com.facishare.open.qywx.accountbind.utils.TraceUtil;
import com.facishare.open.qywx.accountinner.arg.SendTextNoticeArg;
import com.facishare.open.qywx.accountinner.model.QyweixinExternalContact;
import com.facishare.open.qywx.accountinner.model.QyweixinExternalContactRsp;
import com.facishare.open.qywx.accountinner.model.QyweixinGetAuthInfoRsp;
import com.facishare.open.qywx.accountinner.service.ContactBindInnerService;
import com.facishare.open.qywx.accountinner.service.NotificationService;
import com.facishare.open.qywx.accountinner.service.QyweixinAccountBindInnerService;
import com.facishare.open.qywx.accountinner.service.QyweixinGatewayInnerService;
import com.facishare.open.qywx.accountsync.core.enums.QYWXDataTypeEnum;
import com.facishare.open.qywx.web.config.ConfigCenter;
import com.facishare.open.qywx.accountsync.core.enums.QYWXBindTypeEnum;
import com.facishare.open.qywx.web.crm.CrmUrlUtils;
import com.facishare.open.qywx.accountsync.model.OaConnectorDataModel;
import com.facishare.open.qywx.web.manager.*;
import com.facishare.open.qywx.web.model.login.UserTicketModel;
import com.facishare.open.qywx.accountsync.model.qyweixin.*;
import com.facishare.open.qywx.web.model.qyweixin.*;
import com.facishare.open.qywx.accountsync.result.ErrorRefer;
import com.facishare.open.qywx.accountsync.result.Result;
import com.facishare.open.qywx.accountsync.service.ContactsService;
import com.facishare.open.qywx.accountsync.service.QyweixinAccountSyncService;
import com.facishare.open.qywx.web.template.inner.jsapi.QyweixinJsApiTemplate;
import com.facishare.open.qywx.web.template.inner.login.QyweixinLoginTemplate;
import com.facishare.open.outer.oa.connector.i18n.qywx.I18NStringEnum;
import com.facishare.open.outer.oa.connector.i18n.qywx.I18NStringManager;
import com.facishare.open.qywx.save.service.MessageGeneratingService;
import com.facishare.open.qywx.web.threadpool.ThreadPoolHelper;
import com.facishare.open.qywx.web.utils.RandomUtils;
import com.facishare.open.qywx.web.utils.SHA1;
import com.facishare.organization.adapter.api.model.biz.department.Department;
import com.facishare.organization.adapter.api.permission.model.BatchGetRoleCodesByEmployeeIds;
import com.facishare.organization.api.model.employee.EmployeeDto;
import com.facishare.paas.license.Result.LicenseVersionResult;
import com.facishare.paas.license.arg.QueryProductArg;
import com.facishare.paas.license.common.LicenseContext;
import com.facishare.paas.license.http.LicenseClient;
import com.facishare.paas.license.pojo.ProductVersionPojo;
import com.facishare.restful.common.StopWatch;
import com.fxiaoke.api.IdGenerator;
import com.fxiaoke.common.Pair;
import com.fxiaoke.crmrestapi.arg.ControllerListArg;
import com.fxiaoke.crmrestapi.common.contants.CrmConstants;
import com.fxiaoke.crmrestapi.common.contants.FilterOperatorEnum;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.common.data.Page;
import com.fxiaoke.crmrestapi.common.data.SearchQuery;
import com.fxiaoke.crmrestapi.service.MetadataControllerService;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.base.Splitter;
import com.google.common.base.Stopwatch;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.mongodb.client.result.DeleteResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * Created by fengyh on 2018/3/1.
 */

@Slf4j
@Service("qyweixinAccountSyncService")
public class QyweixinAccountSyncServiceImpl implements QyweixinAccountSyncService {

    @Autowired
    private QYWeixinManager qyWeixinManager;
    @Autowired
    private CorpManager corpManager;

    @Autowired
    private RedisDataSource redisDataSource;

    @Autowired
    private QyweixinAccountBindService qyweixinAccountBindService;

    @Autowired
    private QyweixinAccountBindInnerService qyweixinAccountBindInnerService;

    @Autowired
    private QyweixinGatewayInnerService qyweixinGatewayInnerService;

//    @Autowired
//    private QyweixinCorpBindDao qyweixinCorpBindDao;
//
//    @Autowired
//    private QyweixinContactBindDao qyweixinContactBindDao;

    @Autowired
    private FsManager fsManager;

    @Autowired
    private QyweixinExternalManager qyweixinExternalManager;

//    @Resource
//    private QyweixinIdToOpenidDao qyweixinIdToOpenidDao;

    @Autowired
    MessageGeneratingService messageGeneratingService;

    @Autowired
    private MetadataControllerService metadataControllerService;

    @Autowired
    private FsEmployeeServiceProxy fsEmployeeServiceProxy;

//    @Autowired
//    private SyncEventDataManger syncEventDataManger;

//    @Autowired
//    private OutDepartmentInfoManger outDepartmentInfoManger;

//    @Autowired
//    private OutUserInfoManger outUserInfoManger;

    @Autowired
    private ContactBindInnerService contactBindInnerService;

//    @Autowired
//    private QyweixinOrderInfoDao qyweixinOrderInfoDao;

    @Autowired
    private EIEAConverter eieaConverter;

    @Autowired
    private ContactsService contactsService;

    @Resource(name = "qywxI18NStringManager")
    private I18NStringManager i18NStringManager;

    //TODO 补充配置中心
    @ReloadableProperty("contactAppId")
    private String contactAppId;

//    @ReloadableProperty("repAppId")
//    private String repAppId;

    @Autowired
    private DataPersistorManager dataPersistorManager;

    @Autowired
    private OAConnectorDataManager oaConnectorDataManager;

    @Autowired
    private OAConnectorOpenDataManager oaConnectorOpenDataManager;

    @Resource(name = "licenseClient")
    private LicenseClient licenseClient;

    @Autowired
    private NotificationService notificationService;

    @Resource
    private QyweixinJsApiTemplate qyweixinJsApiTemplate;

    @Resource
    private QyweixinLoginTemplate qyweixinLoginTemplate;

    @Resource
    private OANewBaseManager oaNewBaseManager;
//    @Autowired
//    private QyweixinBusinessBindInfoDao qyweixinBusinessBindInfoDao;

    @Autowired
    private AutoPullMessageService autoPullMessageService;

//    @Resource
//    private OaConnectorOutUserInfoMongoDao oaConnectorOutUserInfoMongoDao;
    @Resource
    private OaConnectorSyncEventDataMongoDao oaConnectorSyncEventDataMongoDao;
//    @Resource
//    private OaConnectorOutDepartmentInfoMongoDao oaConnectorOutDepartmentInfoMongoDao;

    ExecutorService switchAccountsThreadPool = Executors.newFixedThreadPool(5);
    @Autowired
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;
    @Autowired
    private OuterOaEmployeeBindManager outerOaEmployeeBindManager;
    @Autowired
    private OuterOaAppInfoManager outerOaAppInfoManager;
    @Autowired
    private OuterOaOrderInfoManager outerOaOrderInfoManager;
    @Autowired
    private QyweixinBusinessInfoBindManager qyweixinBusinessInfoBindManager;
//    @Autowired
//    private QyweixinIdToOpenidManager qyweixinIdToOpenidManager;
    @Autowired
    private QyweixinIdToOpenidManager qyweixinIdToOpenidManager;
    @Autowired
    private OuterOaConfigInfoManager outerOaConfigInfoManager;
    @Autowired
    private OuterOaEmployeeDataManager outerOaEmployeeDataManager;
    @Autowired
    private OuterOaDeptDataManager outerOaDeptDataManager;

    private final String GET_CONTACT_SCOPE_CACHE_KEY = "get_contact_scope_cache_key_";

    @Deprecated
    @Override
    public Result<QyweixinCorpInfo> getCorpInfo(String fsEnterpriseAccount, String appId) {
        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntities(OuterOaEnterpriseBindParams.builder().channel(ChannelEnum.qywx).appId(appId).fsEa(fsEnterpriseAccount).build());
        if (CollectionUtils.isEmpty(enterpriseBindEntities)) {
            return Result.newInstance(ErrorRefer.INTERNAL_ERROR);
        }
        return getCorpInfo2(fsEnterpriseAccount, appId, enterpriseBindEntities.get(0).getOutEa());
    }

    @Override
    public Result<QyweixinCorpInfo> getCorpInfo2(String fsEnterpriseAccount, String appId, String outEa) {
        com.facishare.open.qywx.accountinner.result.Result<QyweixinGetAuthInfoRsp> qyweixinAuthCorpInfoResult = qyWeixinManager.getCorpInfo(outEa, appId);
        if(qyweixinAuthCorpInfoResult.isSuccess() && null != qyweixinAuthCorpInfoResult.getData()){
            QyweixinGetAuthInfoRsp qyweixinAuthCorpInfo = qyweixinAuthCorpInfoResult.getData();
            QyweixinCorpInfo  qyweixinCorpInfo = new QyweixinCorpInfo();
            QyweixinAuthCorpInfoRsp qyweixinAuthCorpInfoRsp = qyweixinAuthCorpInfo.getAuth_corp_info();
            qyweixinCorpInfo.setCorpId(qyweixinAuthCorpInfo.getAuth_corp_info().getCorpid());
            qyweixinCorpInfo.setCorpName(qyweixinAuthCorpInfo.getAuth_corp_info().getCorp_name());
            qyweixinCorpInfo.setAppName(qyweixinAuthCorpInfo.getAuth_info().getAgent().isEmpty() ? "":qyweixinAuthCorpInfo.getAuth_info().getAgent().get(0).getName());

            qyweixinCorpInfo.setCorpName(qyweixinAuthCorpInfoRsp.getCorp_name());
            qyweixinCorpInfo.setCorpFullName(qyweixinAuthCorpInfoRsp.getCorp_full_name());
            qyweixinCorpInfo.setCorpIndustry(qyweixinAuthCorpInfoRsp.getCorp_industry());
            qyweixinCorpInfo.setCorpScale(qyweixinAuthCorpInfoRsp.getCorp_scale());
            qyweixinCorpInfo.setCorpSquareLogoUrl(qyweixinAuthCorpInfoRsp.getCorp_square_logo_url());
            qyweixinCorpInfo.setCorpSubIndustry(qyweixinAuthCorpInfoRsp.getCorp_sub_industry());
            qyweixinCorpInfo.setCorpType(qyweixinAuthCorpInfoRsp.getCorp_type());
            qyweixinCorpInfo.setCorpUserMax(qyweixinAuthCorpInfoRsp.getCorp_user_max());
            qyweixinCorpInfo.setCorpWxqrcode(qyweixinAuthCorpInfoRsp.getCorp_wxqrcode());
            qyweixinCorpInfo.setSubjectType(qyweixinAuthCorpInfoRsp.getSubject_type());
            qyweixinCorpInfo.setVerifiedEndTime(qyweixinAuthCorpInfoRsp.getVerified_end_time());
            qyweixinCorpInfo.setLocation(qyweixinAuthCorpInfoRsp.getLocation());
            qyweixinCorpInfo.setBindTypeEnum(getBindType(qyweixinAuthCorpInfo.getAuth_corp_info().getCorpid(), appId, fsEnterpriseAccount));

            if(!qyweixinAuthCorpInfo.getAuth_info().getAgent().isEmpty()){
                QyweixinAgentPrivilege qyweixinAgentPrivilege = new QyweixinAgentPrivilege();
                QyweixinAgentPrivilegeRsp qyweixinAgentPrivilegeRsp = qyweixinAuthCorpInfo.getAuth_info().getAgent().get(0).getPrivilege();
                qyweixinAgentPrivilege.setAllowParty(qyweixinAgentPrivilegeRsp.getAllow_party());
                qyweixinAgentPrivilege.setAllowUser(qyweixinAgentPrivilegeRsp.getAllow_user());
                qyweixinAgentPrivilege.setAllowTag(qyweixinAgentPrivilegeRsp.getAllow_tag());
                qyweixinCorpInfo.setAppPrivilege(qyweixinAgentPrivilege);
            }
            log.info("getCorpInfo success. fsAccount：{}. appId：{}. qyweixinCorpInfo：{}", fsEnterpriseAccount, appId, qyweixinCorpInfo);
            return new Result<>(qyweixinCorpInfo);
        }
        return Result.newInstance(ErrorRefer.INTERNAL_ERROR);

    }

    /**
     * 获取绑定类型
     *
     * @param corpId 企业微信侧企业ID
     * @return QYWXBindTypeEnum
     */
    private QYWXBindTypeEnum getBindType(String corpId, String appId, String fsEa) {
        // 检测到是否是手动绑定纷享企业
        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntities(OuterOaEnterpriseBindParams.builder().channel(ChannelEnum.qywx).outEa(corpId).appId(appId).fsEa(fsEa).build());

        log.info("getBindType success. corpId：{}. enterpriseBindEntities：{}", corpId, enterpriseBindEntities);
        if(CollectionUtils.isNotEmpty(enterpriseBindEntities)) {
            OuterOaEnterpriseBindEntity enterpriseBindEntity = enterpriseBindEntities.get(0);
            if (Objects.nonNull(enterpriseBindEntity)) {
                return enterpriseBindEntity.getBindType() == BindTypeEnum.auto ? QYWXBindTypeEnum.NEW_CORP_BIND : QYWXBindTypeEnum.OLD_CORP_BIND;
            }
        }
        return QYWXBindTypeEnum.NEW_CORP_BIND;
    }

//    private String getCorpIdByFsEa(String fsEnterpriseAccount) {
//        String corpId = qyweixinAccountBindService.fsEaToOutEa(SourceTypeEnum.QYWX.getSourceType(), fsEnterpriseAccount).getData();
//        if(StringUtils.isBlank(corpId)){
//            throw new RuntimeException("trace getCorpIdByFsEa not find corpId fsEa:" + fsEnterpriseAccount);
//        }
//        return corpId;
//    }

    @Deprecated
    @Override
    public Result<QyweixinGetAdminListRsp> getAdminList(String outEa, String appId, Integer agentId) {
        com.facishare.open.qywx.accountinner.result.Result<QyweixinGetAdminListRsp> result = qyWeixinManager.getAdminList(outEa, appId, agentId);
        if(result.isSuccess()) {
            return new Result<>(result.getData());
        } else {
            return new Result<>(result.getCode(),result.getMsg(),null);
        }
    }

    @Deprecated
    @Override
    public Result<List<QyweixinDepartmentInfo>> getDepartmentInfoList(String fsEnterpriseAccount, String appId, String departmentId) {
        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntities(OuterOaEnterpriseBindParams.builder().channel(ChannelEnum.qywx).appId(appId).fsEa(fsEnterpriseAccount).build());
        if (CollectionUtils.isEmpty(enterpriseBindEntities)) {
            return Result.newInstance(ErrorRefer.INTERNAL_ERROR);
        }
        List<QyweixinDepartmentInfo> departmentInfoList = Lists.newArrayList();
        for (OuterOaEnterpriseBindEntity enterpriseBindEntity : enterpriseBindEntities) {
            departmentInfoList.addAll(getDepartmentInfoList2(fsEnterpriseAccount, appId, departmentId, enterpriseBindEntity.getOutEa()).getData());
        }
        return new Result<>(departmentInfoList);
    }

    @Override
    public Result<List<QyweixinDepartmentInfo>> getDepartmentInfoList2(String fsEnterpriseAccount, String appId, String departmentId, String outEa) {
//        String appId1 = appId;
//        if (Objects.equals(ConfigCenter.crmAppId, appId)) {
//            appId1 = this.getAppId(outEa);
//        }
        com.facishare.open.qywx.accountinner.result.Result<QyweixinDepartmentListRsp> departmentListRspResult = qyWeixinManager.getDepartmentInfoList(appId, outEa, departmentId);
        if(departmentListRspResult.isSuccess() && null != departmentListRspResult.getData()){
            List<QyweixinDepartmentInfo> departmentInfoList = departmentListRspResult.getData().getDepartment().stream()
                    .map(v -> convertQyweixinDepartmentInfo(appId, v)).collect(Collectors.toList());
            return new Result<>(departmentInfoList);
        } else {
            return new Result<>(Lists.newArrayList());
        }
    }

    private QyweixinDepartmentInfo convertQyweixinDepartmentInfo(String appId, QyweixinDepartmentRsp department) {
        QyweixinDepartmentInfo qyweixinDepartmentInfo = new QyweixinDepartmentInfo();
        qyweixinDepartmentInfo.setAppId(appId);
        qyweixinDepartmentInfo.setId(String.valueOf(department.getId()));
        qyweixinDepartmentInfo.setName(department.getName());
        qyweixinDepartmentInfo.setParentId(String.valueOf(department.getParentid()));
        qyweixinDepartmentInfo.setOrder(department.getOrder());
        return qyweixinDepartmentInfo;
    }

    @Override
    public Result<List<QyweixinTag>> getTagInfoList(String fsEnterpriseAccount, String appId) {
        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntities(OuterOaEnterpriseBindParams.builder().channel(ChannelEnum.qywx).appId(appId).fsEa(fsEnterpriseAccount).build());
        if (CollectionUtils.isEmpty(enterpriseBindEntities)) {
            return Result.newInstance(ErrorRefer.INTERNAL_ERROR);
        }
        List<QyweixinTag> tagInfoList = Lists.newArrayList();
        for (OuterOaEnterpriseBindEntity enterpriseBindEntity : enterpriseBindEntities) {
            tagInfoList.addAll(getTagInfoList2(fsEnterpriseAccount, appId, enterpriseBindEntity.getOutEa()).getData());
        }
        return new Result<>(tagInfoList);
    }

    @Override
    public Result<List<QyweixinTag>> getTagInfoList2(String fsEnterpriseAccount, String appId, String outEa) {
        com.facishare.open.qywx.accountinner.result.Result<QyweixinTagListRsp> tagListRspResult = qyWeixinManager.getTagInfoList(appId, outEa);
        if(tagListRspResult.isSuccess() && null != tagListRspResult.getData()){
            List<QyweixinTag> tagList = tagListRspResult.getData().getTaglist().stream().map(v -> convertQyweixinTag(v)).collect(Collectors.toList());
            return new Result<>(tagList);
        } else {
            return new Result<>(Lists.newArrayList());
        }
    }

    private QyweixinTag convertQyweixinTag(QyweixinTagListRsp.QyweixinTagRsp qyweixinTagRsp) {
        QyweixinTag qyweixinTag = new QyweixinTag();
        qyweixinTag.setTagId(qyweixinTagRsp.getTagid());
        qyweixinTag.setTagName(qyweixinTagRsp.getTagname());
        return qyweixinTag;
    }

    @Deprecated
    @Override
    public Result<List<QyweixinEmployeeInfo>> getDepartmentEmployeeInfoList(String fsEnterpriseAccount, String appId, String departmentId) {
        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntities(OuterOaEnterpriseBindParams.builder().channel(ChannelEnum.qywx).appId(appId).fsEa(fsEnterpriseAccount).build());
        if (CollectionUtils.isEmpty(enterpriseBindEntities)) {
            return Result.newInstance(ErrorRefer.INTERNAL_ERROR);
        }
        List<QyweixinEmployeeInfo> employeeInfoList = Lists.newArrayList();
        for (OuterOaEnterpriseBindEntity enterpriseBindEntity : enterpriseBindEntities) {
            employeeInfoList.addAll(getDepartmentEmployeeInfoList3(fsEnterpriseAccount, appId, departmentId, enterpriseBindEntity.getOutEa()).getData());
        }
        return new Result<>(employeeInfoList);
    }

    @Override
    public Result<List<QyweixinEmployeeInfo>> getDepartmentEmployeeInfoList3(String fsEnterpriseAccount, String appId, String departmentId, String outEa) {
//        if (Objects.equals(ConfigCenter.crmAppId, appId)) {
//            appId = this.getAppId(outEa);
//        }
        com.facishare.open.qywx.accountinner.result.Result<QyweixinDepartmentEmployeeListRsp> departmentEmployeeListRspResult = qyWeixinManager.getDepartmentEmployeeList(appId, outEa
                , departmentId);
        if(departmentEmployeeListRspResult.isSuccess() && null != departmentEmployeeListRspResult.getData()){
            List<QyweixinEmployeeInfo> employeeInfoList = departmentEmployeeListRspResult.getData().getUserlist().stream().map(v->convertToQyweixinEmployeeInfo(v)).collect(Collectors.toList());
            return new Result<>(employeeInfoList);
        } else {
            return new Result<>(Lists.newArrayList());
        }
    }

    @Override
    public Result<List<QyweixinEmployeeInfo>> getDepartmentEmployeeInfoList(String fsEnterpriseAccount, String departmentId) {
        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntities(OuterOaEnterpriseBindParams.builder().channel(ChannelEnum.qywx).fsEa(fsEnterpriseAccount).build());
        if (CollectionUtils.isEmpty(enterpriseBindEntities)) {
            return Result.newInstance(ErrorRefer.INTERNAL_ERROR);
        }
        List<QyweixinEmployeeInfo> employeeInfoList = Lists.newArrayList();
        for (OuterOaEnterpriseBindEntity enterpriseBindEntity : enterpriseBindEntities) {
            employeeInfoList.addAll(getDepartmentEmployeeInfoList(fsEnterpriseAccount, enterpriseBindEntity.getAppId(), departmentId).getData());
        }
        return new Result<>(employeeInfoList);
    }

//    /**
//     * 获取企业纷应用ID。
//     * 对于反绑的企业代开发应用优先，依次通讯录应用，最后才是CRM应用
//     *对于原生版的企业通讯录应用优先，最后才是CRM应用
//     * @param corpId 企业微信企业ID
//     * @return
//     */
//    private String getAppId(String corpId) {
//        //通过corpId获取绑定类型，一对多的企业，绑定类型应该也是一样的
//        com.facishare.open.qywx.accountbind.result.Result<List<QyweixinAccountEnterpriseMapping>> enterpriseBindResult =
//                qyweixinAccountBindService.selectEnterpriseBind(SourceTypeEnum.QYWX.getSourceType(), corpId);
//        if(!enterpriseBindResult.isSuccess() || CollectionUtils.isEmpty(enterpriseBindResult.getData())) {
//            return ConfigCenter.crmAppId;
//        }
//        QyweixinCorpBindBo corpBindBo1 = qyweixinCorpBindDao.queryQyweixinCorpBind(corpId, repAppId);
//        if (enterpriseBindResult.getData().get(0).getBindType() == 1
//                && Objects.nonNull(corpBindBo1) && corpBindBo1.getStatus() == 0) {
//            return repAppId;
//        }
//        QyweixinCorpBindBo corpBindBo = qyweixinCorpBindDao.queryQyweixinCorpBind(corpId, contactAppId);
//        if (Objects.nonNull(corpBindBo) && corpBindBo.getStatus() == 0) {
//            return contactAppId;
//        }
//        return ConfigCenter.crmAppId;
//    }

    @Deprecated
    @Override
    public Result<QyweixinTagEmployeeList> getTagEmployeeInfoList(String fsEnterpriseAccount, String appId, String tagId) {
        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntities(OuterOaEnterpriseBindParams.builder().channel(ChannelEnum.qywx).fsEa(fsEnterpriseAccount).appId(appId).build());
        if (CollectionUtils.isEmpty(enterpriseBindEntities)) {
            return Result.newInstance(ErrorRefer.INTERNAL_ERROR);
        }
        //这个没有办法做多应用，所以只能取第一个
        return getTagEmployeeInfoList3(fsEnterpriseAccount,appId,tagId,enterpriseBindEntities.get(0).getOutEa());
    }

    @Override
    public Result<QyweixinTagEmployeeList> getTagEmployeeInfoList3(String fsEnterpriseAccount, String appId, String tagId, String outEa) {
//        if (Objects.equals(ConfigCenter.crmAppId, appId)) {
//            appId = this.getAppId(outEa);
//        }
        com.facishare.open.qywx.accountinner.result.Result<QyweixinTagEmployeeListRsp> tagEmployeeListRspResult = qyWeixinManager.getTagEmployeeList(appId, outEa, tagId);
        if(tagEmployeeListRspResult.isSuccess() && null != tagEmployeeListRspResult.getData()){
            QyweixinTagEmployeeListRsp tagEmployeeListRsp = tagEmployeeListRspResult.getData();
            QyweixinTagEmployeeList qyweixinTagEmployeeList = new QyweixinTagEmployeeList();
            qyweixinTagEmployeeList.setTagName(tagEmployeeListRsp.getTagname());
            List<QyweixinEmployeeInfo> employeeInfoList = tagEmployeeListRsp.getUserlist().stream().map(v-> convertToQyweixinEmployeeInfo(v)).collect(Collectors.toList());
            qyweixinTagEmployeeList.setUserList(employeeInfoList);
            qyweixinTagEmployeeList.setPartyList(tagEmployeeListRsp.getPartylist());

            return new Result<>(qyweixinTagEmployeeList);
        } else {
            return new Result<>();
        }
    }

    @Override
    public Result<QyweixinTagEmployeeList> getTagEmployeeInfoList(String fsEnterpriseAccount, String tagId) {
        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntities(OuterOaEnterpriseBindParams.builder().channel(ChannelEnum.qywx).fsEa(fsEnterpriseAccount).build());
        if (CollectionUtils.isEmpty(enterpriseBindEntities)) {
            return Result.newInstance(ErrorRefer.INTERNAL_ERROR);
        }
        return this.getTagEmployeeInfoList(fsEnterpriseAccount, enterpriseBindEntities.get(0).getAppId(), tagId);
    }

    @Deprecated
    @Override
    public Result<List<QyweixinEmployeeInfo>> getEmployeeInfoBatch(String fsEnterpriseAccount, String appId, List<String> userIds) {
        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntities(OuterOaEnterpriseBindParams.builder().channel(ChannelEnum.qywx).fsEa(fsEnterpriseAccount).appId(appId).build());
        if (CollectionUtils.isEmpty(enterpriseBindEntities)) {
            return Result.newInstance(ErrorRefer.INTERNAL_ERROR);
        }
        List<QyweixinEmployeeInfo> employeeInfoList = new ArrayList<>();
        for (OuterOaEnterpriseBindEntity enterpriseBindEntity : enterpriseBindEntities) {
            employeeInfoList.addAll(getEmployeeInfoBatch3(fsEnterpriseAccount, enterpriseBindEntity.getAppId(), userIds, enterpriseBindEntity.getOutEa()).getData());
        }
        return new Result<>(employeeInfoList);
    }

    @Override
    public Result<List<QyweixinEmployeeInfo>> getEmployeeInfoBatch3(String fsEnterpriseAccount, String appId, List<String> userIds, String outEa) {
//        if (Objects.equals(ConfigCenter.crmAppId, appId)) {
//            appId = this.getAppId(outEa);
//        }
//        String corpId = getCorpIdByFsEa(fsEnterpriseAccount);
        String finalAppId = appId;
        List<QyweixinEmployeeInfo> employeeInfoList = userIds.stream().map(userId -> getUserInfo(finalAppId, outEa, userId)).filter(v-> null != v).collect(Collectors.toList());
        if(null != employeeInfoList){
            return new Result<>(employeeInfoList);
        } else {
            return new Result<>(Lists.newArrayList());
        }
    }

    @Deprecated
    @Override
    public Result<List<QyweixinEmployeeInfo>> getEmployeeInfoBatch(String fsEnterpriseAccount, List<String> userIds) {
        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntities(OuterOaEnterpriseBindParams.builder().channel(ChannelEnum.qywx).fsEa(fsEnterpriseAccount).build());
        if (CollectionUtils.isEmpty(enterpriseBindEntities)) {
            return Result.newInstance(ErrorRefer.INTERNAL_ERROR);
        }
        List<QyweixinEmployeeInfo> employeeInfoList = new ArrayList<>();
        for (OuterOaEnterpriseBindEntity enterpriseBindEntity : enterpriseBindEntities) {
            employeeInfoList.addAll(getEmployeeInfoBatch(fsEnterpriseAccount, enterpriseBindEntity.getAppId(), userIds).getData());
        }
        return new Result<>(employeeInfoList);
    }

    private QyweixinEmployeeInfo getUserInfo(String appId, String corpId, String userId) {
        com.facishare.open.qywx.accountinner.result.Result<QyweixinUserDetailInfoRsp> qyweixinUserDetailInfoRspResult = qyWeixinManager.getUserInfo(appId, corpId, userId);
        if(qyweixinUserDetailInfoRspResult.isSuccess() && ObjectUtils.isNotEmpty(qyweixinUserDetailInfoRspResult.getData())){
            QyweixinUserDetailInfoRsp qyweixinUserDetailInfoRsp = qyweixinUserDetailInfoRspResult.getData();
            return convertToQyweixinEmployeeInfo(qyweixinUserDetailInfoRsp);
        }
        return null;
    }

    @Override
    public Result<List<QyweixinEmployeeInfo>> batchGetEmployeeInfo(String corpId, List<String> userIds) {
        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntities(OuterOaEnterpriseBindParams.builder().channel(ChannelEnum.qywx).outEa(corpId).bindStatus(BindStatusEnum.normal).build());
        if (CollectionUtils.isEmpty(enterpriseBindEntities)) {
            return Result.newInstance(ErrorRefer.INTERNAL_ERROR);
        }
        List<QyweixinEmployeeInfo> employeeInfoList = new ArrayList<>();
        for (OuterOaEnterpriseBindEntity enterpriseBindEntity : enterpriseBindEntities) {
            employeeInfoList.addAll(userIds.stream().map(userId -> getUserInfo(enterpriseBindEntity.getAppId(), corpId, userId)).filter(Objects::nonNull).collect(Collectors.toList()));
        }
        return new Result<>(employeeInfoList);
    }

    public Result<QyweixinEmployeeInfo> getEmployeeInfo(String corpId, String appId, String userId) {

        com.facishare.open.qywx.accountinner.result.Result<QyweixinUserDetailInfoRsp> qyweixinUserDetailInfoRspResult = qyWeixinManager.getUserInfo(appId, corpId, userId);

        if(qyweixinUserDetailInfoRspResult.isSuccess() && ObjectUtils.isNotEmpty(qyweixinUserDetailInfoRspResult.getData())){
            QyweixinEmployeeInfo qyweixinEmployeeInfo = convertToQyweixinEmployeeInfo(qyweixinUserDetailInfoRspResult.getData());
            return new Result<>(qyweixinEmployeeInfo);
        }
        return new Result<>();
    }

    private QyweixinEmployeeInfo convertToQyweixinEmployeeInfo(QyweixinUserDetailInfoRsp qyweixinUserDetailInfoRsp) {
        QyweixinEmployeeInfo qyweixinEmployeeInfo = new QyweixinEmployeeInfo();
        qyweixinEmployeeInfo.setUserId(qyweixinUserDetailInfoRsp.getUserid());
        qyweixinEmployeeInfo.setName(qyweixinUserDetailInfoRsp.getName());
        if( null != qyweixinUserDetailInfoRsp.getDepartment()){
            qyweixinEmployeeInfo.setDepartment(qyweixinUserDetailInfoRsp.getDepartment().stream().map(String::valueOf).collect(Collectors.toList()));
        }
        qyweixinEmployeeInfo.setEnable(qyweixinUserDetailInfoRsp.getEnable());
        qyweixinEmployeeInfo.setOrder(qyweixinUserDetailInfoRsp.getOrder());
        qyweixinEmployeeInfo.setGender(qyweixinUserDetailInfoRsp.getGender()+"");
        qyweixinEmployeeInfo.setStatus(qyweixinUserDetailInfoRsp.getStatus());
        // 表示在所在的部门内是否为上级
        if (CollectionUtils.isNotEmpty(qyweixinUserDetailInfoRsp.getIs_leader_in_dept())) {
            qyweixinEmployeeInfo.setIsLeaderInDept(qyweixinUserDetailInfoRsp.getIs_leader_in_dept().stream().map(Integer::valueOf).collect(Collectors.toList()));
        }
        qyweixinEmployeeInfo.setMainDepartment(qyweixinUserDetailInfoRsp.getMain_department());

        //TODO 登录用户的类型
        qyweixinEmployeeInfo.setUserType(5);
        return  qyweixinEmployeeInfo;
    }

    /**
     * 企业微信上小程序获取当前用户的身份
     *
     * */
    @Override
    public Result<QyweixinUserBind> getQyweixinMiniCurrentUser(Map paramMap) {
        String suitID = String.valueOf(paramMap.get("miniSuitID"));
        String code = String.valueOf(paramMap.get("miniCode"));

        QyweixinUserBind result = new QyweixinUserBind();
        com.facishare.open.qywx.accountinner.result.Result<QyweixinJscode2sessionRsp> rspResult = qyWeixinManager.jscode2sessionService(code, suitID);
        if(!rspResult.isSuccess() || ObjectUtils.isEmpty(rspResult.getData())) {
            return new Result().addError(rspResult.getCode(), rspResult.getMsg(),null);
        }
        QyweixinJscode2sessionRsp rsp = rspResult.getData();
        result.setQywxAppID(suitID);
        result.setQywxCorpID(rsp.getCorpid());
        result.setQywxUserID(rsp.getUserid());
        result.setSessionkey(rsp.getSession_key());

        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntities(OuterOaEnterpriseBindParams.builder().channel(ChannelEnum.qywx).outEa(rsp.getCorpid()).build());
        if (CollectionUtils.isEmpty(enterpriseBindEntities)) {
            return new Result<>(result);
        }

        List<OuterOaEmployeeBindEntity> employeeBindEntities = outerOaEmployeeBindManager.getEntitiesByNotPage(OuterOaEmployeeBindParams.builder().channel(ChannelEnum.qywx).fsEa(enterpriseBindEntities.get(0).getFsEa()).outEa(rsp.getCorpid()).outEmpId(rsp.getUserid()).build());

        if(CollectionUtils.isEmpty(employeeBindEntities)) {
            log.warn("getQyweixinMiniCurrentUser outAccountToFsAccountBatch failed. paramMap:{}, response:{}", paramMap, rsp);
            return  new Result<>(result);
        }

        result.setFsEa(enterpriseBindEntities.get(0).getFsEa());
        result.setFsUserID(employeeBindEntities.get(0).getFsEmpId());

        log.info("getQyweixinMiniCurrentUser success. arg:{}, result:{}", paramMap, result);
        return new Result<>(result);
    }

    @Override
    public Result<QyweixinEmployeeInfo> getQyweixinCurrentUser(Map paramMap) {
        log.info("QyweixinAccountSyncServiceImpl.getQyweixinCurrentUser,paramMap={}",paramMap);
        if(paramMap.containsKey("ticket")){
            //兼容有多个ticket的情况
            String ticketStr=String.valueOf(paramMap.get("ticket"));
            if(ticketStr.contains(",")){
                String[] tickets=ticketStr.split(",");
                paramMap.put("ticket",tickets[0]);
            }
            Stopwatch stopwatch =Stopwatch.createStarted();
            long time = 0;

            MethodContext context = MethodContext.newInstance(paramMap);
            qyweixinLoginTemplate.getFsUserInfoByTicket(context);
            Map<String, Object> userInfoMap = context.getResultData();

            String userTicketInfo = (String) userInfoMap.get("userTicketInfo");
            UserTicketModel userInfo  = (UserTicketModel) userInfoMap.get("userInfo");

            long currentTime = System.currentTimeMillis();
            //检查ticket是否过期
            if((currentTime - userInfo.getTimestamp()) > 10*60*1000){
                log.info("ticket已过期，ticket={}",  paramMap.get("ticket"));
                return Result.newInstance(ErrorRefer.EXPIRED_TICKET);
            }

            QyweixinEmployeeInfo qyweixinEmployeeInfo = new QyweixinEmployeeInfo();
            qyweixinEmployeeInfo.setUserId(userInfo.getUserId());
            qyweixinEmployeeInfo.setCorpId(userInfo.getCorpId());
            qyweixinEmployeeInfo.setAppId(userInfo.getAppId());

            String userId = userInfo.getUserId();
            String fsEa = userInfo.getFsEa();
            List<OuterOaEmployeeBindEntity> employeeBindEntities = outerOaEmployeeBindManager.getEntitiesByNotPage(OuterOaEmployeeBindParams.builder().channel(ChannelEnum.qywx).fsEa(fsEa).outEa(userInfo.getCorpId()).appId(userInfo.getAppId()).outEmpId(userId).build());

            time = stopwatch.elapsed(TimeUnit.MILLISECONDS);
            log.info("QyweixinAccountSyncServiceImpl.getQyweixinCurrentUser,time={}",time);
            if(CollectionUtils.isEmpty(employeeBindEntities)) {
                //把明文的userId转换成密文的userId
                time = stopwatch.elapsed(TimeUnit.MILLISECONDS);
                log.info("QyweixinAccountSyncServiceImpl.getQyweixinCurrentUser,userId2OpenUserId begin,time={}",
                        time);
                com.facishare.open.qywx.accountinner.result.Result<List<QyweixinOpenUserIdInfo>> userListResult = qyWeixinManager.userId2OpenUserId(Lists.newArrayList(userId), userInfo.getCorpId(), userInfo.getAppId());
                time = stopwatch.elapsed(TimeUnit.MILLISECONDS);
                log.info("QyweixinAccountSyncServiceImpl.getQyweixinCurrentUser,userId2OpenUserId end,time={}",
                        time);
                log.info("QyweixinAccountSyncServiceImpl.getQyweixinCurrentUser,userList={}", userListResult);
                if(userListResult.isSuccess() && CollectionUtils.isNotEmpty(userListResult.getData())) {
                    userId = userListResult.getData().get(0).getOpen_userid();
                    log.info("QyweixinAccountSyncServiceImpl.getQyweixinCurrentUser,userId={}",userId);
                    employeeBindEntities = outerOaEmployeeBindManager.getEntitiesByNotPage(OuterOaEmployeeBindParams.builder().channel(ChannelEnum.qywx).fsEa(fsEa).outEa(userInfo.getCorpId()).appId(userInfo.getAppId()).outEmpId(userId).build());
                }
            }
            log.info("QyweixinAccountSyncServiceImpl.getQyweixinCurrentUser,employeeBindEntities={}",employeeBindEntities);
            //检查人员是否绑定
            if(CollectionUtils.isEmpty(employeeBindEntities)){
                String lang = i18NStringManager.getDefaultLangByFsEa(fsEa);

                //检测是否安装了CRM
                List<OuterOaAppInfoEntity> appInfoEntities = outerOaAppInfoManager.getEntities(OuterOaAppInfoParams.builder().channel(ChannelEnum.qywx).outEa(userInfo.getCorpId()).appId(userInfo.getAppId()).build());
                if(CollectionUtils.isEmpty(appInfoEntities)){
                    log.info("trace login, not install CRM for user ticket:{} ",  paramMap.get("ticket"));
                    return Result.newInstance(ErrorRefer.CRM_APP_NOT_INSTALL);
                }

                //检测企业绑定
                List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntities(OuterOaEnterpriseBindParams.builder().channel(ChannelEnum.qywx).fsEa(fsEa).outEa(userInfo.getCorpId()).appId(userInfo.getAppId()).build());

                if(CollectionUtils.isEmpty(enterpriseBindEntities)){
                    log.info("企业帐号未绑定，ticket={}",  paramMap.get("ticket"));
                    return Result.newInstance(ErrorRefer.CORP_CREATING);
                }

                OuterOaEnterpriseBindEntity enterpriseBindEntity = enterpriseBindEntities.get(0);
                if (StringUtils.isEmpty(fsEa)) {
                    fsEa = enterpriseBindEntity.getFsEa();
                }
                //检测手动绑定还是自动绑定
                if(enterpriseBindEntity.getBindType() == BindTypeEnum.auto){
                    log.info("新企业员工帐号未绑定，ticket={}",  paramMap.get("ticket"));
                    //上报
                    OAConnectorOpenDataModel model = OAConnectorOpenDataModel.builder()
                            .ea(fsEa)
                            .channelId(ChannelEnum.qywx.name())
                            .dataTypeId(QYWXDataTypeEnum.EMPLOYEE_LOGIN.getDataType())
                            .corpId(userInfo.getCorpId())
                            .errorCode("103")
                            .errorMsg(i18NStringManager.get(I18NStringEnum.s158,lang,null))
                            .build();
                    oaConnectorOpenDataManager.send(model);
                    //告警
                    SendTextNoticeArg arg = new SendTextNoticeArg();
                    arg.setEnterpriseAccount(ConfigCenter.NOTIFICATION_EA);
                    List<String> receivers = new LinkedList<>(ConfigCenter.NOTIFICATION_MEMBERS);
                    arg.setReceivers(receivers.stream().map(Integer::valueOf).collect(Collectors.toList()));
                    arg.setMsgTitle(i18NStringManager.get(I18NStringEnum.s124,lang,null));
                    String ticket = paramMap.get("ticket").toString();
                    String msg = i18NStringManager.get2(I18NStringEnum.s159.getI18nKey(),
                            lang,
                            null,
                            String.format(I18NStringEnum.s159.getI18nValue(), ticket, userTicketInfo),
                            Lists.newArrayList(
                                    ticket, userTicketInfo
                            ));
                    arg.setMsg(msg);
                    notificationService.sendQYWXNotice(arg);

                    return Result.newInstance(ErrorRefer.CORP_ACCOUNT_NOT_BIND);
                }else {
                    log.info("老企业员工帐号未绑定，ticket={}",  paramMap.get("ticket"));
                    return Result.newInstance(ErrorRefer.OLD_CORP_ACCOUNT_NOT_BIND);
                }
            } else {
                if(employeeBindEntities.get(0).getBindStatus() == BindStatusEnum.stop) {
                    return Result.newInstance(ErrorRefer.EMPLOYEE_IS_STOP);
                }
            }

            if (StringUtils.isEmpty(fsEa)) {
                fsEa = employeeBindEntities.get(0).getFsEa();
            }

            qyweixinEmployeeInfo.setFsAccount("E." + fsEa + "." + employeeBindEntities.get(0).getFsEmpId());
            log.info("QyweixinAccountSyncServiceImpl.getQyweixinCurrentUser, paramMap={}, qyweixinEmployeeInfo={}", paramMap, qyweixinEmployeeInfo);
            time = stopwatch.elapsed(TimeUnit.MILLISECONDS);
            log.info("QyweixinAccountSyncServiceImpl.getQyweixinCurrentUser,total time={}",time);
            return new Result<>(qyweixinEmployeeInfo);
        }

        if(paramMap.containsKey("auth_code")) {
            com.facishare.open.qywx.accountinner.result.Result<QyweixinLoginInfoRsp> loginInfoResult = qyWeixinManager.getWebLoginUserInfo(String.valueOf(paramMap.get("auth_code")));
            log.info("QyweixinAccountSyncServiceImpl.getQyweixinCurrentUser,loginInfo={}",loginInfoResult);
            if(!loginInfoResult.isSuccess() || ObjectUtils.isEmpty(loginInfoResult.getData())) {
                return new Result<QyweixinEmployeeInfo>().addError(ErrorRefer.INTERNAL_ERROR.getCode(),
                        loginInfoResult.getMsg(),null);
            }

            QyweixinLoginInfoRsp loginInfo = loginInfoResult.getData();
            QyweixinEmployeeInfo qyweixinEmployeeInfo = new QyweixinEmployeeInfo();
            qyweixinEmployeeInfo.setUserId(loginInfo.getUser_info().getUserid());
            qyweixinEmployeeInfo.setCorpId(loginInfo.getCorp_info().getCorpid());
            log.info("QyweixinAccountSyncServiceImpl.getQyweixinCurrentUser,qyweixinEmployeeInfo={}",qyweixinEmployeeInfo);
            List<OuterOaEmployeeBindEntity> employeeBindEntities = outerOaEmployeeBindManager.getEntitiesByNotPage(OuterOaEmployeeBindParams.builder().channel(ChannelEnum.qywx).outEa(qyweixinEmployeeInfo.getCorpId()).outEmpId(qyweixinEmployeeInfo.getUserId()).build());

            log.info("QyweixinAccountSyncServiceImpl.getQyweixinCurrentUser,employeeBindEntities={}",employeeBindEntities);
            if(CollectionUtils.isEmpty(employeeBindEntities)){
                return Result.newInstance(ErrorRefer.CORP_ACCOUNT_NOT_BIND);
            } else {
                if(employeeBindEntities.get(0).getBindStatus() == BindStatusEnum.stop) {
                    return Result.newInstance(ErrorRefer.EMPLOYEE_IS_STOP);
                }
            }
            qyweixinEmployeeInfo.setFsAccount("E." + employeeBindEntities.get(0).getFsEa() + "." + employeeBindEntities.get(0).getFsEmpId());
            log.info("QyweixinAccountSyncServiceImpl.getQyweixinCurrentUser,qyweixinEmployeeInfo2={}",qyweixinEmployeeInfo);
            return new Result<>(qyweixinEmployeeInfo);
        }
        return new Result<>();
    }

//    public List<QyweixinAccountEmployeeMapping> getFsAccountByOutAccount(String corpId, String userId,int status,String fsEa) {
//        log.info("QyweixinAccountSyncServiceImpl.getFsAccountByOutAccount,corpId={},userId={}",corpId,userId);
//        Result<List<QyweixinAccountEmployeeMapping>> result = qyweixinAccountBindInnerService.getEmployeeMapping(corpId,
//                userId, status,fsEa);
//        if(result.getData()==null) {
//            corpId = qyWeixinManager.corpId2OpenCorpId(corpId).getData();
//            log.info("QyweixinAccountSyncServiceImpl.getFsAccountByOutAccount,corpId={}",corpId);
//            result = qyweixinAccountBindInnerService.getEmployeeMapping(corpId,
//                    userId, status,fsEa);
//        }
//        log.info("QyweixinAccountSyncServiceImpl.getFsAccountByOutAccount,result={}",result);
//        return result.getData();
//    }

    @Override
    //这里被外部团队引用
    public Result<QyweixinJsapiSignature> createJsapiSignature(String url, String fsEnterpriseAccount, String appId) {
        JsApiModel jsApiModel = new JsApiModel();
        jsApiModel.setAppId(appId);
        jsApiModel.setFsEa(fsEnterpriseAccount);
        jsApiModel.setOutEa(null);
        jsApiModel.setUrl(url);

        MethodContext context = MethodContext.newInstance(jsApiModel);
        qyweixinJsApiTemplate.getJsApiSignature(context);
        Result<QyweixinJsapiSignature> result = (Result<QyweixinJsapiSignature>) context.getResult().getData();
        log.info("QYWeixinManager.createJsapiSignature,result={}",result);
        return result;
    }

    @Override
    public Result<QyweixinJsapiSignature> createJsapiSignature2(String url, String fsEnterpriseAccount, String appId, String outEa) {
        log.info("QYWeixinManager.createJsapiSignature,url={},fsEnterpriseAccount={},appId={},outEa={}",url,fsEnterpriseAccount,appId,outEa);
        String corpId = outEa;
        if(StringUtils.isEmpty(outEa)) {
            List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntities(OuterOaEnterpriseBindParams.builder().channel(ChannelEnum.qywx).fsEa(fsEnterpriseAccount).appId(appId).build());
            if(CollectionUtils.isNotEmpty(enterpriseBindEntities)) {
                corpId = enterpriseBindEntities.get(0).getOutEa();
            }
            log.info("QYWeixinManager.createJsapiSignature,corpId={}",corpId);
        }
        if(StringUtils.isEmpty(corpId)) {
            //兼容老逻辑
            List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntities(OuterOaEnterpriseBindParams.builder().channel(ChannelEnum.qywx).fsEa(fsEnterpriseAccount).build());
            if(CollectionUtils.isNotEmpty(enterpriseBindEntities)) {
                corpId = enterpriseBindEntities.get(0).getOutEa();
                //如果是代开发的appId，不能替换，有特殊用法：群id
                //如果本身是代开发授权的，直接替换就行
                if (!appId.equals(ConfigCenter.repAppId) || enterpriseBindEntities.get(0).getAppId().startsWith("dk")) {
                    appId = enterpriseBindEntities.get(0).getAppId();
                }
            }
            log.info("QYWeixinManager.createJsapiSignature,old,corpId={},appId={}",corpId,appId);
        }

        if(StringUtils.isEmpty(corpId)) {
            return Result.newInstance(ErrorRefer.FS_EA_NOT_BIND);
        }

        corpId = corpManager.getValidCorpId(corpId, appId);
        log.info("QYWeixinManager.createJsapiSignature,validCoprId={}",corpId);

        List<OuterOaAppInfoEntity> appInfoEntities = outerOaAppInfoManager.getEntities(OuterOaAppInfoParams.builder().channel(ChannelEnum.qywx).outEa(corpId).appId(appId).status(OuterOaAppInfoStatusEnum.normal).build());

        log.info("QYWeixinManager.createJsapiSignature,appInfoEntities={}",appInfoEntities);
        if(CollectionUtils.isEmpty(appInfoEntities)) {
            appInfoEntities = outerOaAppInfoManager.getEntities(OuterOaAppInfoParams.builder().channel(ChannelEnum.qywx).outEa(corpId).status(OuterOaAppInfoStatusEnum.normal).build());

            log.info("QYWeixinManager.createJsapiSignature,appInfoEntities2={}",appInfoEntities);
            if(CollectionUtils.isNotEmpty(appInfoEntities)) {
                //替换掉appId，因为对于订货通，服务通，俊文侧传过来的appId是crm的appId
                appId = appInfoEntities.get(0).getAppId();
                log.info("QYWeixinManager.createJsapiSignature,new appId={}",appId);
            }
        }
        if(CollectionUtils.isEmpty(appInfoEntities)) {
            return Result.newInstance(ErrorRefer.QYWX_NOT_INSTALL_VALID_FS_APP);
        }
        long timestamp = System.currentTimeMillis() / 1000;
        String noncestr = RandomUtils.getRandomStr();
        com.facishare.open.qywx.accountinner.result.Result<String> jsapiTicketResult = qyWeixinManager.getJsApiTicket(appId, corpId, false);
        if(!jsapiTicketResult.isSuccess()) {
            return new Result<>(ErrorRefer.INTERNAL_ERROR.getCode(), jsapiTicketResult.getMsg(),null);
        }
        String jsapiTicket = jsapiTicketResult.getData();
        String signature = SHA1.genWithAmple(
                "jsapi_ticket=" + jsapiTicket,
                "noncestr=" + noncestr,
                "timestamp=" + timestamp,
                "url=" + url
        );
        log.info("QYWeixinManager.createJsapiSignature,jsapiTicket={},noncestr={},timestamp={},url={}",jsapiTicket,noncestr,timestamp,url);
        QyweixinJsapiSignature jsapiSignature = new QyweixinJsapiSignature();
        jsapiSignature.setTimestamp(timestamp);
        jsapiSignature.setNonceStr(noncestr);
        jsapiSignature.setUrl(url);
        jsapiSignature.setSignature(signature);
        jsapiSignature.setAppId(corpId);
        com.facishare.open.qywx.accountinner.result.Result<String> agentJsapiTicketResult = qyWeixinManager.getAgentJsApiTicket(appId, corpId, false);
        if(!agentJsapiTicketResult.isSuccess()) {
            return new Result<>(ErrorRefer.INTERNAL_ERROR.getCode(), agentJsapiTicketResult.getMsg(),null);
        }
        String agentJsapiTicket = agentJsapiTicketResult.getData();
        String agentSignature = SHA1.genWithAmple(
                "jsapi_ticket=" + agentJsapiTicket,
                "noncestr=" + noncestr,
                "timestamp=" + timestamp,
                "url=" + url
        );
        QyweixinAppInfoParams qyweixinAppInfoParams = JSON.parseObject(appInfoEntities.get(0).getAppInfo(), QyweixinAppInfoParams.class);
        jsapiSignature.setAgentCorpId(corpId);
        jsapiSignature.setAgentId(String.valueOf(qyweixinAppInfoParams.getAuthAppInfo().getAgentId()));
        jsapiSignature.setAgentNonceStr(noncestr);
        jsapiSignature.setAgentTimestamp(timestamp);
        jsapiSignature.setAgentSignature(agentSignature);
        jsapiSignature.setRealAppId(appId);
        log.info("QYWeixinManager.createJsapiSignature,jsapiSignature={}",jsapiSignature);
        return new Result<>(jsapiSignature);
    }

    private QyweixinApp convertQyweixinCorpBind(OuterOaAppInfoEntity appInfoEntity) {
        QyweixinApp result = new QyweixinApp();
        result.setQyWeixinAppID(appInfoEntity.getAppId());
        return result;
    }

    /**
     * 获取开通的企业微信应用列表。
     *
     * @param fsEnterpriseAccount : 企业在纷享平台上的账号
     * @return : 添加过的企业微信应用列表。如果没有添加过企业微信应用，则返回空列表
     */
    @Deprecated
    @Override
    public Result<List<QyweixinApp>> getAuthorizedAppInfo(String fsEnterpriseAccount) {
        return getAuthorizedAppInfo2(fsEnterpriseAccount, null);
    }

    @Override
    public Result<List<QyweixinApp>> getAuthorizedAppInfo2(String fsEnterpriseAccount, String outEa) {
        try {
            List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntities(OuterOaEnterpriseBindParams.builder().channel(ChannelEnum.qywx).fsEa(fsEnterpriseAccount).outEa(outEa).build());
            if (CollectionUtils.isEmpty(enterpriseBindEntities)) {
                return Result.newInstance(ErrorRefer.CORP_ACCOUNT_NOT_BIND);
            }

            List<QyweixinApp> resultData = new LinkedList<>();
            for (OuterOaEnterpriseBindEntity enterpriseBindEntity : enterpriseBindEntities) {
                String corpId = enterpriseBindEntity.getOutEa();
                List<OuterOaAppInfoEntity> appInfoEntities = outerOaAppInfoManager.getEntities(OuterOaAppInfoParams.builder().channel(ChannelEnum.qywx).outEa(corpId).appId(enterpriseBindEntity.getAppId()).status(OuterOaAppInfoStatusEnum.normal).build());
                if (CollectionUtils.isEmpty(appInfoEntities)) {
                    continue;
                }
                List<QyweixinApp> resultDataByCorp = appInfoEntities.stream().map(this::convertQyweixinCorpBind).collect(Collectors.toList());
                resultData.addAll(resultDataByCorp);
            }
            log.info("getAuthorizedAppInfo for fsea:{}, get result:{} ", fsEnterpriseAccount, resultData);
            return new Result(resultData);
        }catch (Exception e) {
            log.error("getAuthorizedAppInfo get exception, ", e);
            return Result.newInstance(ErrorRefer.INTERNAL_ERROR);
        }
    }

    /**
     * 获取外部联系人详情
     *
     * @param userIDs : 企业微信外部联系人userID列表
     * @return :  @see QyweixinExternalContact
     */
    @Override
    public Result<List<QyweixinExternalContactInfo>> getQyweixinExternalContacts(String appID,
                                                                                 String corpID,
                                                                                 String fsEa,
                                                                                 String fsAccount,
                                                                                 ArrayList<String> userIDs) {
        log.info("QyweixinAccountSyncServiceImpl.getQyweixinExternalContacts,appId={},corpID={},fsAccount={},userIDs={}",
                appID,corpID,fsAccount,userIDs);
        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntities(OuterOaEnterpriseBindParams.builder().channel(ChannelEnum.qywx).fsEa(fsEa).appId(appID).outEa(corpID).build());
        log.info("QyweixinAccountSyncServiceImpl.getQyweixinExternalContacts,enterpriseBindEntities={}",
                enterpriseBindEntities);
        if(CollectionUtils.isEmpty(enterpriseBindEntities)) {
            return Result.newInstance(ErrorRefer.OUT_EA_NOT_BIND);
        }

        List<OuterOaEmployeeBindEntity> employeeBindEntities = outerOaEmployeeBindManager.getEntitiesByNotPage(OuterOaEmployeeBindParams.builder().channel(ChannelEnum.qywx).fsEa(fsEa).appId(appID).outEa(corpID).fsEmpId(fsAccount).build());
        if(CollectionUtils.isEmpty(employeeBindEntities)) {
            return Result.newInstance(ErrorRefer.FS_EMP_NOT_BIND);
        }

        String outAccount = employeeBindEntities.get(0).getOutEmpId();
        log.info("QyweixinAccountSyncServiceImpl.getQyweixinExternalContacts,outAccount={}",
                outAccount);

        ArrayList<QyweixinExternalContactInfo> result = Lists.newArrayList();
        for(String user : userIDs) {
            com.facishare.open.qywx.accountinner.result.Result<QyweixinExternalContactInfo> qyweixinExternalContactResult = qyWeixinManager.getQyweixinExternalContact(appID, corpID, user, outAccount);
            log.info("QyweixinAccountSyncServiceImpl.getQyweixinExternalContacts,appId={},corpID={},userIDs={},qyweixinExternalContact={}",
                    appID,corpID,user,qyweixinExternalContactResult);
            if(qyweixinExternalContactResult.isSuccess() && qyweixinExternalContactResult.getData()!=null) {
                result.add(qyweixinExternalContactResult.getData());
                //可以进行更新操作
                QyweixinExternalContactRsp rsp = new QyweixinExternalContactRsp();
                QyweixinExternalContact contact = new QyweixinExternalContact();
                contact.setExternal_userid(qyweixinExternalContactResult.getData().getExternalUserid());
                contact.setName(qyweixinExternalContactResult.getData().getName());
                contact.setAvatar(qyweixinExternalContactResult.getData().getAvatar());
                rsp.setExternal_contact(contact);
                qyweixinExternalManager.insertOrUpdateExternalContact(corpID, outAccount, Lists.newArrayList(rsp));
            } else {
                return new Result<List<QyweixinExternalContactInfo>>().addError(ErrorRefer.QYWX_NOT_DATA.getCode(), qyweixinExternalContactResult.getMsg(),null);
            }
        }
        log.info("QyweixinAccountSyncServiceImpl.getQyweixinExternalContacts,result={}",result);
        return new Result<>(result);
    }

    @Override
    public Result<QyweixinExternalContactInfo> queryExternalContactsForSelf(String corpId, String appSecret, String externalUser) {

        com.facishare.open.qywx.accountinner.result.Result<QyweixinExternalContact> qyweixinExternalContactResult = qyWeixinManager.queryExternalAccount(appSecret, corpId, externalUser);
        if(!qyweixinExternalContactResult.isSuccess() || ObjectUtils.isEmpty(qyweixinExternalContactResult.getData())) {
            return new Result<>(qyweixinExternalContactResult.getCode(), qyweixinExternalContactResult.getMsg(),null);
        }
        return new Result<>(convertInfo(qyweixinExternalContactResult.getData()));
    }

    @Override
    public Result<QyweixinEmployeeInfo> getUserInfoFromSelf(String corpSecret, String corpId, String userId) {
        com.facishare.open.qywx.accountinner.result.Result<QyweixinUserDetailInfoRsp> userInfoFromSelfResult = qyWeixinManager.getUserInfoFromSelf(corpSecret, corpId, userId);
        if(!userInfoFromSelfResult.isSuccess() || ObjectUtils.isEmpty(userInfoFromSelfResult.getData())){
            return new Result(ErrorRefer.QUERRY_EMPTY.getCode(),userInfoFromSelfResult.getMsg(),null);
        }
        QyweixinEmployeeInfo qyweixinEmployeeInfo = convertToQyweixinEmployeeInfo(userInfoFromSelfResult.getData());
        return new Result<>(qyweixinEmployeeInfo);
    }

    private QyweixinExternalContactInfo convertInfo(QyweixinExternalContact qyweixinExternalContact){
        if(ObjectUtils.isEmpty(qyweixinExternalContact)) return null;
        QyweixinExternalContactInfo contactInfo=new QyweixinExternalContactInfo();
        contactInfo.setAvatar(qyweixinExternalContact.getAvatar());
        contactInfo.setCorpFullName(qyweixinExternalContact.getCorp_full_name());
        contactInfo.setCorpName(qyweixinExternalContact.getCorp_name());
        contactInfo.setExternalProfileList(qyweixinExternalContact.getExternal_profile());
        contactInfo.setName(qyweixinExternalContact.getName());
        contactInfo.setGender(qyweixinExternalContact.getGender());
        contactInfo.setPosition(qyweixinExternalContact.getPosition());
        contactInfo.setType(qyweixinExternalContact.getType());
        contactInfo.setUnionid(qyweixinExternalContact.getUnionid());
        contactInfo.setExternalUserid(qyweixinExternalContact.getExternal_userid());
        return contactInfo;
    }


    /**
     * 获取当前用户的手机号
     *
     * @param appId      : 企业微信开平上创建的应用ID
     * @param qywxCorpId :企业微信平台上分配的企业账号
     * @param code       : 当前用户的企业微信code
     * @return : 用户的手机号。
     */
    @Override
    public Result<Pair<String, String>> getUserPhone(String appId, String qywxCorpId, String code) {
        com.facishare.open.qywx.accountinner.result.Result<Pair<String, String>> userPhonePairResult = qyWeixinManager.getUserPhone(appId, qywxCorpId, code);
        if(userPhonePairResult.isSuccess()) {
            return  new Result<>(userPhonePairResult.getData());
        }
        return  new Result<Pair<String, String>>().addError(userPhonePairResult.getCode(), userPhonePairResult.getMsg(),null);
    }

    @Deprecated
    @Override
    public Result<Integer> getCountQyweixinPrivilegeEmployee(String fsEa) {
        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntities(OuterOaEnterpriseBindParams.builder().channel(ChannelEnum.qywx).fsEa(fsEa).build());
        if(CollectionUtils.isEmpty(enterpriseBindEntities)) {
            return new Result<Integer>(0);
        }
        return getCountQyweixinPrivilegeEmployee2(fsEa, enterpriseBindEntities.get(0).getOutEa(), enterpriseBindEntities.get(0).getAppId());
    }

    @Override
    public Result<Integer> getCountQyweixinPrivilegeEmployee2(String fsEa, String outEa, String appId) {
        //1. 获取组织架构  》 2.获取组织架构中根部门  》 3.获取部门员工  》4.去重获取员工数

        boolean isCancelAuth = qyWeixinManager.isCancelAuth(outEa, appId).getData();
        log.info("getCountQyweixinPrivilegeEmployee isCancelAuth. fsEa:{}. isCancelAuth:{}", fsEa, isCancelAuth);
        if (isCancelAuth) {
            return new Result<Integer>(0);
        }

        String corpId = outEa;
        com.facishare.open.qywx.accountinner.result.Result<QyweixinGetAuthInfoRsp> corpInfoResult = qyWeixinManager.getCorpInfo(corpId, appId);
        if(!corpInfoResult.isSuccess() || ObjectUtils.isEmpty(corpInfoResult.getData())){
            return new Result<Integer>().addError(ErrorRefer.INTERNAL_ERROR.getCode(), corpInfoResult.getMsg(),null);
        }
        QyweixinGetAuthInfoRsp corpInfo = corpInfoResult.getData();
        List<String> allow_party = corpInfo.getAuth_info().getAgent().get(0).getPrivilege().getAllow_party();
        List<String> allow_tag = corpInfo.getAuth_info().getAgent().get(0).getPrivilege().getAllow_tag();
        List<String> allow_user = corpInfo.getAuth_info().getAgent().get(0).getPrivilege().getAllow_user();



        HashSet<String> employeeIdSet = new HashSet<>();

        if(null!=allow_party && !allow_party.isEmpty()){
            allow_party.stream().forEach(v-> {
                com.facishare.open.qywx.accountinner.result.Result<QyweixinDepartmentEmployeeListRsp> departmentEmployeeListResult = qyWeixinManager.getDepartmentEmployeeList(appId, corpId, v);
                if(!departmentEmployeeListResult.isSuccess()){
                    throw new RuntimeException("trace getCountQyweixinPrivilegeEmployee department exception fsEa:" + fsEa + departmentEmployeeListResult.getMsg());
                }
                departmentEmployeeListResult.getData().getUserlist().stream().forEach( user ->{
                    employeeIdSet.add(user.getUserid());
                });
            });
        }

        if(null!=allow_user && !allow_user.isEmpty()){
            allow_user.stream().forEach(v->{
                employeeIdSet.add(v);
            });
        }

        if(null!=allow_tag && !allow_tag.isEmpty()){
            allow_tag.stream().forEach(v->{
                com.facishare.open.qywx.accountinner.result.Result<QyweixinTagEmployeeListRsp> tagEmployeeListResult = qyWeixinManager.getTagEmployeeList(appId, corpId, v);
                if(!tagEmployeeListResult.isSuccess()){
                    throw new RuntimeException("trace getCountQyweixinPrivilegeEmployee tag exception fsEa:" + fsEa + tagEmployeeListResult.getData().getErrmsg());
                }
                tagEmployeeListResult.getData().getUserlist().stream().forEach( user ->{
                    employeeIdSet.add(user.getUserid());
                });
            });
        }


        return new Result<Integer>(employeeIdSet.size());
    }

    @Override
    public Result<QyweixinCorpBindInfo> getCorpBind(String ea, String appId) {
        return getCorpBind2(ea, appId, null);
    }

    @Override
    public Result<QyweixinCorpBindInfo> getCorpBind2(String ea, String appId, String outEa) {
        log.info("QyweixinAccountSyncServiceImpl.getCorpBind, ea={}, appId={}", ea, appId);
        if (StringUtils.isBlank(ea) || StringUtils.isBlank(appId))
          return Result.newInstance(ErrorRefer.FS_EMP_NOT_BIND);

        QyweixinCorpBindInfo result = new QyweixinCorpBindInfo();
        String corpId = outEa;
        if(StringUtils.isEmpty(outEa)) {
            List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntities(OuterOaEnterpriseBindParams.builder().channel(ChannelEnum.qywx).fsEa(ea).bindStatus(BindStatusEnum.normal).build());
            if (CollectionUtils.isEmpty(enterpriseBindEntities)) {
                return Result.newInstance(ErrorRefer.FS_EA_NOT_BIND);
            }

            corpId = enterpriseBindEntities.get(0).getOutEa();
        }
        log.info("QyweixinAccountSyncServiceImpl.getCorpBind, ea={}, corpId={}", ea, corpId);
        OuterOaAppInfoEntity oaAppInfoEntity = outerOaAppInfoManager.getEntity(ChannelEnum.qywx, corpId, appId);
        log.info("QyweixinAccountSyncServiceImpl.getCorpBind, oaAppInfoEntity={}", oaAppInfoEntity);
        if (oaAppInfoEntity == null) {
            return new Result<>(result);
        }

        result.setCorpId(corpId);
        result.setCorpName(oaAppInfoEntity.getOutEa());
        result.setAppId(appId);
        result.setStatus(oaAppInfoEntity.getStatus() == OuterOaAppInfoStatusEnum.normal ? 0 : 1);
        log.info("QyweixinAccountSyncServiceImpl.getCorpBind, result={}", result);
        return new Result<>(result);
    }

    @Override
    public Result<QyweixinCorpBindInfo> getCorpBindInfo(String outEa, String appId) {
        if (StringUtils.isBlank(outEa) || StringUtils.isBlank(appId))
            return Result.newInstance(ErrorRefer.PARAM_ERROR);

        log.info("getCorpBindInfo start. outEa:{}, appId:{}", outEa, appId);
        QyweixinCorpBindInfo qyweixinCorpBindInfo = new QyweixinCorpBindInfo();
        OuterOaAppInfoEntity oaAppInfoEntity = outerOaAppInfoManager.getEntity(ChannelEnum.qywx, outEa, appId);
        log.info("QyweixinAccountSyncServiceImpl.getCorpBind, oaAppInfoEntity={}", oaAppInfoEntity);
        if (oaAppInfoEntity == null) {
            return new Result<>(qyweixinCorpBindInfo);
        }
        qyweixinCorpBindInfo.setCorpId(outEa);
        qyweixinCorpBindInfo.setCorpName(oaAppInfoEntity.getOutEa());
        qyweixinCorpBindInfo.setAppId(appId);
        qyweixinCorpBindInfo.setStatus(oaAppInfoEntity.getStatus() == OuterOaAppInfoStatusEnum.normal ? 0 : 1);
        return new Result<>(qyweixinCorpBindInfo);
    }

    /**
     * 简单写个测试方法 因为验证用户登录所需的code, 操作步骤太多。
     */
    @Override
    public void testDatapersistorUserLogin() {
        String fsEa = "ee";
        String corpId = "ww4ba39487c1f49492";
        String userId = "LiuWei3";
        String appId = "wx4c7edab730f4fdc9";
        dataPersistorManager.userAppLoginLog(fsEa, corpId, appId, userId);
    }

    @Override
    public Result<List<QyweixinExternalContactInfo>> queryExternalContactList(String fsEa, Integer userId) {
        StopWatch stopWatch=StopWatch.create("trace queryContactList");

        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntities(OuterOaEnterpriseBindParams.builder().channel(ChannelEnum.qywx).fsEa(fsEa).build());
        if(CollectionUtils.isEmpty(enterpriseBindEntities)) {
            return Result.newInstance(ErrorRefer.CRM_APP_NOT_INSTALL);
        }
        List<QyweixinExternalContactInfo> infos=Lists.newArrayList();
        stopWatch.lap("toOutAccount");
        for (OuterOaEnterpriseBindEntity enterpriseBindEntity : enterpriseBindEntities) {
            String corpId = enterpriseBindEntity.getOutEa();
            String appId = enterpriseBindEntity.getAppId();

            List<OuterOaEmployeeBindEntity> employeeBindEntities = outerOaEmployeeBindManager.getEntitiesByNotPage(OuterOaEmployeeBindParams.builder().channel(ChannelEnum.qywx).fsEa(fsEa).appId(appId).outEa(corpId).fsEmpId(String.valueOf(userId)).build());
            if(CollectionUtils.isEmpty(employeeBindEntities)) {
                continue;
            }

            OuterOaAppInfoEntity oaAppInfoEntity = outerOaAppInfoManager.getEntity(ChannelEnum.qywx, corpId, appId);
            if (ObjectUtils.isEmpty(oaAppInfoEntity) || oaAppInfoEntity.getStatus() != OuterOaAppInfoStatusEnum.normal) {
                continue;
            }
//            QyweixinAppInfoParams qyweixinAppInfoParams = JSON.parseObject(oaAppInfoEntity.getAppInfo(), QyweixinAppInfoParams.class);

            String outAccount = employeeBindEntities.get(0).getOutEmpId();
            com.facishare.open.qywx.accountinner.result.Result<List<QyweixinExternalContactRsp>> contactListResult = qyWeixinManager.getContactList(oaAppInfoEntity.getAppId(), corpId, fsEa, outAccount);
            if(contactListResult.isSuccess() && CollectionUtils.isNotEmpty(contactListResult.getData())) {
                contactListResult.getData().stream().forEach(item ->{
                    QyweixinExternalContactInfo externalInfo = convertExternalContactInfo(item);
                    infos.add(externalInfo);
                });
            }
        }
        stopWatch.lap("getContactList");
        stopWatch.log();
        return new Result<>(infos);

    }

    @Override
    public List<QyweixinExternalContactInfo> queryExternalContactListTwoScheme(String ea, String outAccount) {
//        List<String> secret = qyweixinCorpBindDao.getSecret(ea);
//        if(CollectionUtils.isEmpty(secret)) {
//            return new LinkedList<>();
//        }
//        log.info("QyweixinAccountSyncServiceImpl.queryExternalContactListTwoScheme secret={}, ea={}", secret, ea);
//        secret = secret.stream().map(SecurityUtil::decryptStr).collect(Collectors.toList());
//        String outEa = qyweixinCorpBindDao.getOutEa(ea, ConfigCenter.crm_domain);
//        //走自建应用
//        com.facishare.open.qywx.accountinner.result.Result<String> tokenResult = qyWeixinManager.getToken(secret.get(0), outEa);
//        if(!tokenResult.isSuccess() || StringUtils.isEmpty(tokenResult.getData())) {
//            return new LinkedList<>();
//        }
//        List<QyweixinExternalContactInfo> infos=Lists.newArrayList();
//        com.facishare.open.qywx.accountinner.result.Result<List<QyweixinExternalContactBatchInfo>> contactListResult = qyWeixinManager.getContactListByTwoScheme(tokenResult.getData(), outAccount);
//        if(!contactListResult.isSuccess() || CollectionUtils.isEmpty(contactListResult.getData())) {
//            return new LinkedList<>();
//        }
//        contactListResult.getData().stream().forEach(item ->{
//            QyweixinExternalContactInfo externalInfo = convertExternalContactBatchInfo(item);
//            infos.add(externalInfo);
//        });
//        return infos;
        return null;
    }





    private QyweixinExternalContactInfo convertExternalContactInfo(QyweixinExternalContactRsp rsp) {
        QyweixinExternalContact data = rsp.getExternal_contact();
        if(null == data) {
            return null;
        }
        QyweixinExternalContactInfo result = new QyweixinExternalContactInfo();
        result.setAvatar(data.getAvatar());
        result.setCorpName(data.getCorp_name());
        result.setCorpFullName(data.getCorp_full_name());
        result.setExternalUserid(data.getExternal_userid());
        result.setGender(data.getGender());
        result.setName(data.getName());
        result.setPosition(data.getPosition());
        result.setType(data.getType());
        result.setUnionid(data.getUnionid());
        result.setExternalProfileList(data.getExternal_profile());
        result.setFollowUserList(rsp.getFollow_user());
        return result;
    }

    @Deprecated
    @Override
    public Result autRetention(String fs_ea, int flag) {
        return autRetention2(fs_ea, flag, null);
    }

    @Override
    public Result autRetention2(String fs_ea, int flag, String outEa) {
//        int aut = qyweixinCorpBindDao.autRetention(fs_ea, flag, outEa);
//        log.info("Update automatic retention");
//        if(aut < 1) {
//            return Result.newInstance(ErrorRefer.DATABASE_RETURN_NULL);
//        }
        return new Result();
    }

    @Override
    public Result openAuthorization(String fs_ea, int flag) {
        return openAuthorization2(fs_ea, flag, null);
    }

    @Override
    public Result openAuthorization2(String fs_ea, int flag, String outEa) {
//        int aut = qyweixinCorpBindDao.openAuthorization(fs_ea, flag, outEa);
//        log.info("Update authorization");
//        if(aut < 1) {
//            return Result.newInstance(ErrorRefer.DATABASE_RETURN_NULL);
//        }
        return new Result();
    }

    @Deprecated
    @Override
    public Result getAuthorization(String fs_ea) {
        return getAuthorization2(fs_ea, null);
    }

    @Override
    public Result getAuthorization2(String fs_ea, String outEa) {
//        int aut = qyweixinCorpBindDao.getAuthorization(fs_ea, outEa, ConfigCenter.crm_domain);
//        log.info("get authorization");
//        if(aut == 1 || aut == 0) {
//            return new Result(aut);
//        } else {
//            return Result.newInstance(ErrorRefer.DATABASE_RETURN_NULL);
//        }
        return new Result();
    }

    @Override
    public List<String> openAuthorizationByPage(int pageNum, int pageSize) {
//        pageNum = pageNum <=0 ? 1 : pageNum;
//        pageSize = pageSize <=0 ? 50: pageSize;
//        List<String> autRetentionCorpBatch = qyweixinCorpBindDao.openAuthorizationByPage((pageNum - 1) * pageSize, pageSize, ConfigCenter.crm_domain);
//        log.info("Batch access to enterprises that can be automatically retained autRetentionCorpBatch={}", autRetentionCorpBatch);
//        if(autRetentionCorpBatch == null || autRetentionCorpBatch.isEmpty()) {
//            return new LinkedList<>();
//        }
//        return autRetentionCorpBatch;
        return new LinkedList<>();
    }

//    @Override
//    public List<String> getAutRetentionCorp() {
//        List<String> autRetentionCorpBatch = qyweixinCorpBindDao.getAutRetentionCorp(ConfigCenter.crm_domain);
//        log.info("Batch access to enterprises that can be automatically retained autRetentionCorpBatch={}", autRetentionCorpBatch);
//        if(autRetentionCorpBatch == null || autRetentionCorpBatch.isEmpty()) {
//            return new LinkedList<>();
//        }
//        return autRetentionCorpBatch;
//    }

//    @Deprecated
//    @Override
//    public QyweixinContactInfo externalContact(String ea, List<String> userIds, String next_cursor, int limit) {
//        return externalContact2(ea, userIds, next_cursor, limit, null);
//    }

    @Override
    public QyweixinContactInfo externalContact2(String ea, List<String> userIds, String next_cursor, int limit, String outEa) {
        QyweixinContactInfo qyweixinContactInfo = new QyweixinContactInfo();
        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntities(OuterOaEnterpriseBindParams.builder().channel(ChannelEnum.qywx).fsEa(ea).outEa(outEa).build());
        if(CollectionUtils.isEmpty(enterpriseBindEntities)) {
            return qyweixinContactInfo;
        }

        //排序一下
        enterpriseBindEntities = enterpriseBindEntities.stream()
                .sorted(Comparator.comparing(OuterOaEnterpriseBindEntity::getCreateTime).reversed())

                .collect(Collectors.toList());

        if (ObjectUtils.isNotEmpty(next_cursor)) {
            List<String> accountList = Splitter.on("@").splitToList(next_cursor);
            next_cursor = accountList.get(2);
            enterpriseBindEntities = filterEnterpriseBindEntities(enterpriseBindEntities, accountList.get(0), accountList.get(1), accountList.get(2));
        }
        if(CollectionUtils.isEmpty(enterpriseBindEntities)) {
            return qyweixinContactInfo;
        }

        List<QyweixinExternalContactInfo> infos=Lists.newArrayList();
        for (OuterOaEnterpriseBindEntity enterpriseBindEntity : enterpriseBindEntities) {
            String corpId = enterpriseBindEntity.getOutEa();
            String appId = enterpriseBindEntity.getAppId();

            com.facishare.open.qywx.accountinner.result.Result<Map<String, Object>> externalContactMapResult = qyWeixinManager.getExternalContact(corpId, appId, userIds, next_cursor, limit);
            if(!externalContactMapResult.isSuccess() || ObjectUtils.isEmpty(externalContactMapResult.getData())) {
                continue;
            }

            List<QyweixinExternalContactBatchInfo> externalContact = JSONArray.parseArray(JSONArray.toJSONString(externalContactMapResult.getData().get("external_contact_list")), QyweixinExternalContactBatchInfo.class);
            if(CollectionUtils.isEmpty(externalContact)) {
                continue;
            }
            externalContact.stream().forEach(item ->{
                QyweixinExternalContactInfo externalInfo = convertExternalContactBatchInfo(item);
                infos.add(externalInfo);
            });
            if (ObjectUtils.isEmpty(externalContactMapResult.getData().get("next_cursor")) && enterpriseBindEntities.get(enterpriseBindEntities.size() - 1).getId().equals(enterpriseBindEntity.getId())) {
                qyweixinContactInfo.setNextCursor(externalContactMapResult.getData().get("next_cursor").toString());
            } else {
                qyweixinContactInfo.setNextCursor(corpId + "@" + appId + "@" + externalContactMapResult.getData().get("next_cursor").toString());
            }
            qyweixinContactInfo.setExternalContactInfo(infos);
            log.info("QyweixinAccountSyncServiceImpl.externalContact qyweixinContactInfo:{}",qyweixinContactInfo);
            //分多次查询，所以这里中断
            break;
        }


//        QyweixinAccountEnterpriseMapping mapping = qyweixinContactBindDao.queryMappingFromFsEa(SourceTypeEnum.QYWX.getSourceType(), ea, outEa, ConfigCenter.crm_domain);
//        Integer applicationAccount = qyweixinCorpBindDao.queryEnterpriseReplaceApplication(mapping.getOutEa(), repAppId);
//        String appId = ConfigCenter.crmAppId;
//        if(applicationAccount > 0) {
//            appId = repAppId;
//        }
//        com.facishare.open.qywx.accountinner.result.Result<String> tokenResult = qyWeixinManager.getCorpAccessToken(appId, mapping.getOutEa(), false);
//        if(!tokenResult.isSuccess() || StringUtils.isEmpty(tokenResult.getData())) {
//            return null;
//        }
//        List<QyweixinExternalContactInfo> infos=Lists.newArrayList();
//        QyweixinContactInfo qyweixinContactInfo = new QyweixinContactInfo();
//        com.facishare.open.qywx.accountinner.result.Result<Map<String, Object>> externalContactMapResult = qyWeixinManager.getExternalContact(tokenResult.getData(), userIds, next_cursor, limit);
//        if(!externalContactMapResult.isSuccess() || ObjectUtils.isEmpty(externalContactMapResult.getData())) {
//            return qyweixinContactInfo;
//        }
//        List<QyweixinExternalContactBatchInfo> externalContact = JSONArray.parseArray(JSONArray.toJSONString(externalContactMapResult.getData().get("external_contact_list")), QyweixinExternalContactBatchInfo.class);
//        if(CollectionUtils.isEmpty(externalContact))return qyweixinContactInfo;
//        externalContact.stream().forEach(item ->{
//            QyweixinExternalContactInfo externalInfo = convertExternalContactBatchInfo(item);
//            infos.add(externalInfo);
//        });
//        qyweixinContactInfo.setNextCursor(externalContactMapResult.getData().get("next_cursor").toString());
//        qyweixinContactInfo.setExternalContactInfo(infos);
//        log.info("QyweixinAccountSyncServiceImpl.externalContact qyweixinContactInfo:{}",qyweixinContactInfo);
        return qyweixinContactInfo;
    }

    private List<OuterOaEnterpriseBindEntity> filterEnterpriseBindEntities(List<OuterOaEnterpriseBindEntity> enterpriseBindEntities, String outEa, String appId, String nextCursor) {
        if(CollectionUtils.isEmpty(enterpriseBindEntities)) {
            return new ArrayList<>();
        }

        // 找到匹配的索引
        int matchIndex = -1;
        for (int i = 0; i < enterpriseBindEntities.size(); i++) {
            OuterOaEnterpriseBindEntity entity = enterpriseBindEntities.get(i);
            if (entity.getOutEa().equals(outEa) && entity.getAppId().equals(appId)) {
                if (ObjectUtils.isEmpty(nextCursor)) {
                    matchIndex = i + 1;
                } else {
                    matchIndex = i;
                }
                break;
            }
        }
        // 根据匹配索引截取列表
        List<OuterOaEnterpriseBindEntity> finalEnterpriseBindEntities;
        if (matchIndex != -1) {
            finalEnterpriseBindEntities = enterpriseBindEntities.subList(matchIndex, enterpriseBindEntities.size());
        } else {
            finalEnterpriseBindEntities = new ArrayList<>(); // 如果没有匹配项，返回空列表
        }
        return finalEnterpriseBindEntities;
    }

    private QyweixinExternalContactInfo convertExternalContactBatchInfo(QyweixinExternalContactBatchInfo rsp) {
        QyweixinExternalContact data = rsp.getExternal_contact();
        if(null == data) {
            return null;
        }
        QyweixinExternalContactInfo result = new QyweixinExternalContactInfo();
        result.setAvatar(data.getAvatar());
        result.setCorpName(data.getCorp_name());
        result.setCorpFullName(data.getCorp_full_name());
        result.setExternalUserid(data.getExternal_userid());
        result.setGender(data.getGender());
        result.setName(data.getName());
        result.setPosition(data.getPosition());
        result.setType(data.getType());
        result.setUnionid(data.getUnionid());
        result.setExternalProfileList(data.getExternal_profile());
        result.setFollowUserList(rsp.getFollow_info());
        return result;
    }
//    @Deprecated
//    @Override
//    public Map<String, String> externalContactEmployeeId(String ea) {
//        return externalContactEmployeeId2(ea,null);
//    }

    @Override
    public Map<String, String> externalContactEmployeeId2(String ea, String outEa) {
        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntities(OuterOaEnterpriseBindParams.builder().channel(ChannelEnum.qywx).fsEa(ea).outEa(outEa).build());
        if(CollectionUtils.isEmpty(enterpriseBindEntities)) {
            return new HashMap<>();
        }

        Map<String, String> accountBind = new HashMap<>();
        for (OuterOaEnterpriseBindEntity enterpriseBindEntity : enterpriseBindEntities) {
            String corpId = enterpriseBindEntity.getOutEa();
            String appId = enterpriseBindEntity.getAppId();
            String fsEa = enterpriseBindEntity.getFsEa();

            com.facishare.open.qywx.accountinner.result.Result<List<String>> externalContactEmployeeIdsResult = qyWeixinManager.getExternalContactEmployeeId(corpId, appId);
            if(!externalContactEmployeeIdsResult.isSuccess() || CollectionUtils.isEmpty(externalContactEmployeeIdsResult.getData())) {
                continue;
            }

            List<OuterOaEmployeeBindEntity> employeeBindEntities = outerOaEmployeeBindManager.batchGetEmployeeBindEntity(ChannelEnum.qywx, outEa, fsEa, appId, null, externalContactEmployeeIdsResult.getData());

            if (CollectionUtils.isEmpty(employeeBindEntities)) {
                continue;
            }

            //employeeBindEntities转成map，outEmpId:"E." + fsEa + "." + fsEmpId
            for (OuterOaEmployeeBindEntity employeeBindEntity : employeeBindEntities) {
                String outEmpId = employeeBindEntity.getOutEmpId();
                String fsEmpId = employeeBindEntity.getFsEmpId();
                accountBind.put(outEmpId, "E." + fsEa + "." + fsEmpId);
            }
            log.info("QyweixinAccountSyncServiceImpl.externalContactEmployeeId accountBind:{}",accountBind);
        }



//
//
//        if(StringUtils.isEmpty(outEa)) {
//            outEa = qyweixinCorpBindDao.getOutEa(ea, ConfigCenter.crm_domain);
//        }
//        com.facishare.open.qywx.accountinner.result.Result<String> tokenReslt = qyWeixinManager.getCorpAccessToken(ConfigCenter.crmAppId, outEa, false);
//        if(!tokenReslt.isSuccess() || StringUtils.isEmpty(tokenReslt.getData())) {
//            return new HashMap<>();
//        }
//        Map<String, String> accountBind = new HashMap<>();
//        com.facishare.open.qywx.accountinner.result.Result<List<String>> externalContactEmployeeIdsResult = qyWeixinManager.getExternalContactEmployeeId(tokenReslt.getData());
//        if(!externalContactEmployeeIdsResult.isSuccess() || CollectionUtils.isEmpty(externalContactEmployeeIdsResult.getData())) {
//            return new HashMap<>();
//        }
//        for(String externalContactEmployeeId : externalContactEmployeeIdsResult.getData()) {
//            //把企信员工与纷享员工账号绑定
//            String fsCount = qyweixinCorpBindDao.getFsCountByOutCount(ea, ConfigCenter.crmAppId, externalContactEmployeeId, outEa);
//            accountBind.put(externalContactEmployeeId, fsCount);
//        }
//        log.info("QyweixinAccountSyncServiceImpl.externalContactEmployeeId accountBind:{}",accountBind);
        return accountBind;
    }

    /**
     * 根据手机号查询对象数据
     */
    public Map<String,String> queryByPhone(Integer enterpriseId, String filed, String filedValue, String obj){
        if(filedValue == null || filedValue.length() == 0) {
            return null;
        }
        Map<String,String> hearsMap= Maps.newHashMap();
        hearsMap.put("x-fs-ei",enterpriseId.toString());
        hearsMap.put("x-fs-userinfo","-10000");

        InnerSearchQueryInfo innerSearchQueryInfo = new InnerSearchQueryInfo();
        InnerSearchQueryInfo.Filter orderIdFilter = new InnerSearchQueryInfo.Filter();
        orderIdFilter.setOperator("EQ");
        orderIdFilter.setField_name(filed);
        orderIdFilter.setField_values(Lists.newArrayList(filedValue));

        List<InnerSearchQueryInfo.Filter> filters = Lists.newArrayList(orderIdFilter);
        innerSearchQueryInfo.setFilters(filters);
        innerSearchQueryInfo.setOffset(0);
        innerSearchQueryInfo.setLimit(100);
        InnerSearchQueryInfo.Order order=new InnerSearchQueryInfo.Order();
        order.setFieldName("last_modified_time");
        order.setAsc(false);
        List<InnerSearchQueryInfo.Order> orders = Lists.newArrayList(order);
        innerSearchQueryInfo.setOrders(orders);

        Map<String, String> queryMap = Maps.newHashMap();
        Gson gson=new Gson();
        queryMap.put("object_describe_api_name", obj);
        queryMap.put("search_query_info", gson.toJson(innerSearchQueryInfo));
        String httpResponseMessage = fsManager.getFsUserId(CrmUrlUtils.queryList("/" + obj), hearsMap, queryMap);
        System.out.println(httpResponseMessage);
        log.info("query emp obj:arg:{},result:{}",queryMap,httpResponseMessage);
        JSONArray read = (JSONArray) JSONPath.read(httpResponseMessage, "$.data.dataList");
        if(read.size()==0){
            log.warn("query by phone failed,arg:{},result:{}",innerSearchQueryInfo,httpResponseMessage);
            Map<String, String> objectMap = Maps.newHashMap();
            return objectMap;
        }
        //String userID = JSONPath.read(httpResponseMessage, "$.data.dataList[0].user_id").toString();
        String object_id = JSONPath.read(httpResponseMessage, "$.data.dataList[0]._id").toString();
        Map<String, String> objectMap = Maps.newHashMap();
        //objectMap.put("user_id",userID);
        objectMap.put("object_id",object_id);
        return objectMap;
    }

    @Override
    public int getEiByEa(String ea) {
//        return qyweixinCorpBindDao.getEiByEa(ea);
        return 0;
    }

    @Override
    public String getOutEaByFsEa(String ea) {
//        return qyweixinCorpBindDao.getOutEaByFsEa(ea, ConfigCenter.crm_domain);
        return null;
    }

//    @Deprecated
//    @Override
//    public QyweixinGroupChatDetail.GroupChat getRoomMessage(String ea, String roomId) {
//        List<String> secret = qyweixinCorpBindDao.getSecret(ea);
//        if(CollectionUtils.isEmpty(secret)) {
//            return null;
//        }
//        log.info("QyweixinAccountSyncServiceImpl.getRoomMessage secret={}, ea={}", secret, ea);
//        secret = secret.stream().map(SecurityUtil::decryptStr).collect(Collectors.toList());
//        String outEa = qyweixinCorpBindDao.getOutEa(ea, ConfigCenter.crm_domain);
//        //走自建应用
//        com.facishare.open.qywx.accountinner.result.Result<String> tokenResult = qyWeixinManager.getToken(secret.get(0), outEa);
//        if(!tokenResult.isSuccess() || StringUtils.isEmpty(tokenResult.getData())) {
//            return null;
//        }
//        com.facishare.open.qywx.accountinner.result.Result<QyweixinGroupChatDetail> groupChatDetailResult = qyWeixinManager.getGroupChatDetail(tokenResult.getData(), roomId);
//        if(!groupChatDetailResult.isSuccess() || ObjectUtils.isEmpty(groupChatDetailResult.getData())) return null;
//
//        return groupChatDetailResult.getData().getGroup_chat();
//    }

    @Override
    public QyweixinGroupChatDetail.GroupChat getRoomMessage2(String ea, String roomId, String outEa) {
//        List<String> secret = qyweixinCorpBindDao.getSecret(ea);
//        if(CollectionUtils.isEmpty(secret)) {
//            return null;
//        }
//        log.info("QyweixinAccountSyncServiceImpl.getRoomMessage secret={}, ea={}", secret, ea);
//        secret = secret.stream().map(SecurityUtil::decryptStr).collect(Collectors.toList());
//        if(StringUtils.isEmpty(outEa)) {
//            outEa = qyweixinCorpBindDao.getOutEa(ea, ConfigCenter.crm_domain);
//        }
//        //走自建应用
//        com.facishare.open.qywx.accountinner.result.Result<String> tokenResult = qyWeixinManager.getToken(secret.get(0), outEa);
//        if(!tokenResult.isSuccess() || StringUtils.isEmpty(tokenResult.getData())) {
//            return null;
//        }
//        com.facishare.open.qywx.accountinner.result.Result<QyweixinGroupChatDetail> groupChatDetailResult = qyWeixinManager.getGroupChatDetail(tokenResult.getData(), roomId);
//        if(!groupChatDetailResult.isSuccess() || ObjectUtils.isEmpty(groupChatDetailResult.getData())) return null;
//
//        return groupChatDetailResult.getData().getGroup_chat();
        return null;
    }

//    @Deprecated
//    @Override
//    public Result<List<QyweixinGetMessageUser>> AutoGetExternalContactEmployeeId(String ea) {
//        return AutoGetExternalContactEmployeeId2(ea, null);
//    }

    @Override
    public Result<List<QyweixinGetMessageUser>> AutoGetExternalContactEmployeeId2(String ea, String outEa) {
//        String token;
//        List<String> secret = qyweixinCorpBindDao.getConversionSecret(ea, outEa);
//        if(CollectionUtils.isEmpty(secret)) {
//            return null;
//        }
//        log.info("QyweixinAccountSyncServiceImpl.AutoGetExternalContactEmployeeId secret={}, ea={}", secret, ea);
//        secret = secret.stream().map(SecurityUtil::decryptStr).collect(Collectors.toList());
//        if(StringUtils.isEmpty(outEa)) {
//            outEa = qyweixinCorpBindDao.getOutEa(ea, ConfigCenter.crm_domain);
//        }
//        //走自建应用或代开发
//        com.facishare.open.qywx.accountinner.result.Result<String> tokenResult = qyWeixinManager.getToken(secret.get(0), outEa);
//        if(!tokenResult.isSuccess() || StringUtils.isEmpty(tokenResult.getData())) {
//            return Result.newInstance(ErrorRefer.TOKEN_ERROR);
//        }
//        List<QyweixinGetMessageUser> messageUserList = new LinkedList<>();
//        com.facishare.open.qywx.accountinner.result.Result<List<String>> externalContactEmployeeIdsResult = qyWeixinManager.getPermitUserList2(tokenResult.getData());
//        //获取会话范围的人员，有可能会话服务过期，导致获取不到信息，直接返回空
//        if(!externalContactEmployeeIdsResult.isSuccess() || CollectionUtils.isEmpty(externalContactEmployeeIdsResult.getData())) {
//            return new Result<>(ErrorRefer.INTERNAL_ERROR.getCode(), externalContactEmployeeIdsResult.getMsg(),null);
//        }
//        for(String externalContactEmployeeId : externalContactEmployeeIdsResult.getData()) {
//            QyweixinGetMessageUser messageUser = new QyweixinGetMessageUser();
//            List<QyweixinIdToOpenidBo> qyweixinIdToOpenidBoList = qyweixinIdToOpenidDao.getByPlaintextIds(outEa, Lists.newArrayList(externalContactEmployeeId));
//            if(CollectionUtils.isEmpty(qyweixinIdToOpenidBoList)) {
//                continue;
//            }
//            //把企信员工与纷享员工账号绑定
//            String fsCount = this.getFsAccount(ea, qyweixinIdToOpenidBoList.get(0).getOpenid(), outEa);
////            if(StringUtils.isEmpty(fsCount)) {
////                //明文转密文
////                List<QyweixinOpenUserIdInfo> qyweixinOpenUserIdInfos = qyWeixinManager.userId2OpenUserId(Lists.newArrayList(externalContactEmployeeId), outEa);
////                if(CollectionUtils.isEmpty(qyweixinOpenUserIdInfos)) {
////                    continue;
////                }
////                externalContactEmployeeId = qyweixinOpenUserIdInfos.get(0).getOpen_userid();
////                fsCount = this.getFsAccount(ea, externalContactEmployeeId, outEa);
////            }
//            if(StringUtils.isNotEmpty(fsCount)) {
//                messageUser.setPlaintextId(externalContactEmployeeId);
//                messageUser.setOpenid(qyweixinIdToOpenidBoList.get(0).getOpenid());
//                messageUser.setFsId(fsCount);
//                messageUserList.add(messageUser);
//            }
//        }
//        log.info("QyweixinAccountSyncServiceImpl.externalContactEmployeeId messageUserList:{}",messageUserList);
//        return new Result(messageUserList);
        return new Result();
    }

    @Override
    public String getFsAccount(String ea, String externalContactEmployeeId, String outEa) {
//        String mainAppId = qyweixinGatewayInnerService.getMainAppId(outEa).getData();
//        String fsCountByOutCount = qyweixinCorpBindDao.getFsCountByOutCount(ea, mainAppId, externalContactEmployeeId, outEa);
//        if(StringUtils.isEmpty(fsCountByOutCount)){
//            String isvAccount = qyweixinCorpBindDao.getFsCountByIsvCount(ea, mainAppId, externalContactEmployeeId, outEa);
//            if(StringUtils.isNotEmpty(isvAccount)){
//                return isvAccount;
//            }
//        }
//        return fsCountByOutCount;
        return null;
    }

    @Override
    public String sendAutoMessage(AutoMessageArg autoMessageArg, Map<String, Object> attempt) {
        Map<String, String> headerMap = new HashMap<>();
        Map<String, Object> bodyMap = new HashMap<>();
        Map<String, Object> objectData = new HashMap<>();
        Map<String, Object> activeRecordContent = new HashMap<>();
        Map<String, Object> url = new HashMap<>();
        headerMap.put("x-fs-ei", String.valueOf(autoMessageArg.getEi()));
        headerMap.put("x-fs-userInfo", String.valueOf(autoMessageArg.getFsId()));
        headerMap.put("x-tenant-id", autoMessageArg.getEa());
        headerMap.put("x-user-id", String.valueOf(autoMessageArg.getFsId()));
        headerMap.put("x-fs-locale", "zh-CN");
        headerMap.put("Content-Type", "application/json");

        url.put("summary", autoMessageArg.getUrl());
        url.put("title", autoMessageArg.getTitle());
        url.put("url", autoMessageArg.getUrl());
        url.put("icon", "https://a9.fspage.com/FSR/weex/avatar/feed/images/chat_history_to_sales_record.png");
        activeRecordContent.put("url", url);
        activeRecordContent.put("text", autoMessageArg.getTitle());
        activeRecordContent.put("attachments", attempt.get("attachments"));

        List<String> list = new LinkedList<>();
        list.add(String.valueOf(autoMessageArg.getCreatorId()));
        objectData.put("object_describe_api_name", "ActiveRecordObj");
        objectData.put("record_type", "default__c");
        objectData.put("created_by", list);
        objectData.put("active_record_content", activeRecordContent);
        objectData.put("related_object", autoMessageArg.getRelatedObject());
        bodyMap.put("object_data", objectData);
        bodyMap.put("source", "501");
        String result = qyWeixinManager.sendAutoMessage(headerMap, bodyMap).getData();
        return result;
    }

//    @Deprecated
//    @Override
//    public Result<List<QyweixinExternalUserIdInfo>> switchExternalContactEmployeeId(String ea, List<String> externalUserIds) {
//        return switchExternalContactEmployeeId2(ea,externalUserIds, null, null);
//    }

    @Override
    public Result<List<QyweixinExternalUserIdInfo>> switchExternalContactEmployeeId2(String ea, List<String> externalUserIds, String outEa, String appId) {
//        if(StringUtils.isEmpty(outEa)) {
//            outEa = qyweixinCorpBindDao.getOutEa(ea, ConfigCenter.crm_domain);
//        }
        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntities(OuterOaEnterpriseBindParams.builder().channel(ChannelEnum.qywx).fsEa(ea).outEa(outEa).appId(appId).build());
        if(CollectionUtils.isEmpty(enterpriseBindEntities)) {
            return new Result<>();
        }

        List<QyweixinExternalUserIdInfo> QyweixinExternalUserIdInfos = Lists.newArrayList();
        for (OuterOaEnterpriseBindEntity enterpriseBindEntity : enterpriseBindEntities) {
            String corpId = enterpriseBindEntity.getOutEa();
            String finalAppId = enterpriseBindEntity.getAppId();
            com.facishare.open.qywx.accountinner.result.Result<String> result = qyWeixinManager.switchExternalContactEmployeeId(corpId, externalUserIds, finalAppId);

            if (result.isSuccess() && JSONPath.read(result.getData(), "$.errcode").equals(0)) {
                String resultInfo = JSONArray.toJSONString(JSONPath.read(result.getData(), "$.items"));
                QyweixinExternalUserIdInfos = JSONArray.parseArray(resultInfo, QyweixinExternalUserIdInfo.class);
                log.info("QyweixinAccountSyncServiceImpl.switchExternalContactEmployeeId QyweixinExternalUserIdInfos={}", QyweixinExternalUserIdInfos);
            }
        }

//        com.facishare.open.qywx.accountinner.result.Result<String>  result = qyWeixinManager.switchExternalContactEmployeeId(outEa, externalUserIds, appId);
//
//        if (result.isSuccess() && JSONPath.read(result.getData(), "$.errcode").equals(0)) {
//            String resultInfo = JSONArray.toJSONString(JSONPath.read(result.getData(), "$.items"));
//            QyweixinExternalUserIdInfos = JSONArray.parseArray(resultInfo, QyweixinExternalUserIdInfo.class);
//            log.info("QyweixinAccountSyncServiceImpl.switchExternalContactEmployeeId QyweixinExternalUserIdInfos={}", QyweixinExternalUserIdInfos);
//        } else {
//            return new Result<>(QyweixinExternalUserIdInfos).Result(String.valueOf(JSONPath.read(result.getData(), "$.errcode")), String.valueOf(JSONPath.read(result.getData(), "$.errmsg")),null);
//        }
        return new Result<>(QyweixinExternalUserIdInfos);
    }

//    @Deprecated
//    @Override
//    public Result<List<QyweixinOpenUserIdInfo>> switchEmployeeId(String ea, List<String> userIds) {
//        return switchEmployeeId2(ea, userIds ,null);
//    }

    @Override
    public Result<List<QyweixinOpenUserIdInfo>> switchEmployeeId2(String ea, List<String> userIds, String outEa, String appId) {
//        if(StringUtils.isEmpty(outEa)) {
//            outEa = qyweixinCorpBindDao.getOutEa(ea, ConfigCenter.crm_domain);
//        }
        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntities(OuterOaEnterpriseBindParams.builder().channel(ChannelEnum.qywx).fsEa(ea).outEa(outEa).appId(appId).build());
        if(CollectionUtils.isEmpty(enterpriseBindEntities)) {
            return new Result<>();
        }
        List<QyweixinOpenUserIdInfo> openUserIdInfo = new ArrayList<>();
        for (OuterOaEnterpriseBindEntity enterpriseBindEntity : enterpriseBindEntities) {
            String corpId = enterpriseBindEntity.getOutEa();
            String finalAppId = enterpriseBindEntity.getAppId();
            com.facishare.open.qywx.accountinner.result.Result<List<QyweixinOpenUserIdInfo>> result = qyWeixinManager.userId2OpenUserId(userIds, corpId, finalAppId);
            if (result.isSuccess()) {
                openUserIdInfo.addAll(result.getData());
            }
        }

        return new Result<>(openUserIdInfo);
    }

//    @Override
//    public Result<Void> finishExternalMigration(String ea) {
//        String outEa = qyweixinCorpBindDao.getOutEa(ea, ConfigCenter.crm_domain);
//        com.facishare.open.qywx.accountinner.result.Result<String>  result = qyWeixinManager.finishExternalMigration(outEa);
//        if (result.isSuccess() && JSONPath.read(result.getData(), "$.errcode").equals(0)) {
//            return new Result<>();
//        }
//        return new Result<Void>().Result(String.valueOf(JSONPath.read(result.getData(), "$.errcode")), "",null);
//    }

    @Override
    public String getToken(String outEa, String secret) {
        com.facishare.open.qywx.accountinner.result.Result<String> tokenResult = qyWeixinManager.getToken(secret, outEa);
        if(!tokenResult.isSuccess() || StringUtils.isEmpty(tokenResult.getData())) {
            return null;
        }
        return tokenResult.getData();
    }

//    @Override
//    public int getCorpSecret(String ea) {
//        return qyweixinCorpBindDao.getCorpSecret(ea);
//    }

    @Override
    public Integer queryEnterpriseReplaceApplication(String corpId) {
        List<OuterOaAppInfoEntity> appInfoEntities = outerOaAppInfoManager.getEntities(OuterOaAppInfoParams.builder().channel(ChannelEnum.qywx).outEa(corpId).appType(OuterOaAppInfoTypeEnum.serviceRepDev).build());
        return appInfoEntities.size();
    }

//    @Override
//    public String queryEnterpriseIdByName(String corpName) {
//        return qyweixinCorpBindDao.queryEnterpriseIdByName(corpName, repAppId);
//    }
//    @Override
//    public QyweixinCorpRep getQYWXCorpBindInfo(String corpId) {
//        QyweixinCorpBindBo corpBindBo = qyweixinCorpBindDao.queryQyweixinCorpBind(corpId, repAppId);
//        QyweixinCorpRep corpRep = new QyweixinCorpRep();
//        if(ObjectUtils.isNotEmpty(corpBindBo)) {
//            BeanUtils.copyProperties(corpBindBo, corpRep);
//        }
//        log.info("QyweixinAccountSyncServiceImpl.switchExternalContactEmployeeId.getQYWXCorpBindInfo,corpRep={}", corpRep);
//        return corpRep;
//    }

    @Override
    public void plainToEncryption(String corpId, List<String> plainAccounts) {
        Thread thread = new Thread(() -> plainToEncryption2(corpId, plainAccounts));
        thread.start();
    }

    public void plainToEncryption2(String corpId, List<String> plainAccounts) {
        log.info("QyweixinAccountSyncServiceImpl.plainToEncryption2,corpId={},plainAccounts={}.", corpId, plainAccounts);
//        String corpSecretCode = qyweixinCorpBindDao.getCorpSecretCode(corpId, repAppId);
//        corpSecretCode = SecurityUtil.decryptStr(corpSecretCode);
//        if(StringUtils.isEmpty(corpSecretCode)) {
//            return;
//        }
//        com.facishare.open.qywx.accountinner.result.Result<String> accessTokenResult = qyWeixinManager.getToken(corpSecretCode, corpId);
//        if(!accessTokenResult.isSuccess() || StringUtils.isEmpty(accessTokenResult.getData())) {
//            return;
//        }
//        List<QyweixinIdToOpenidBo> qyweixinIdToOpenidBos = new LinkedList<>();
//        for (int i = 0; i < plainAccounts.size(); i++) {
//            QyweixinIdToOpenidBo qyweixinIdToOpenidBo = new QyweixinIdToOpenidBo();
//            qyweixinIdToOpenidBo.setCorpId(corpId);
//            qyweixinIdToOpenidBo.setPlaintextId(plainAccounts.get(i));
//            qyweixinIdToOpenidBo.setType(1);
//            com.facishare.open.qywx.accountinner.result.Result<String> toServiceExternalResult = qyWeixinManager.toServiceExternalUserId2(accessTokenResult.getData(), plainAccounts.get(i));
//            JSONObject object = JSONObject.parseObject(toServiceExternalResult.getData());
//            String externalUserId = String.valueOf(object.get("external_userid"));
//            if(StringUtils.isNotEmpty(externalUserId) && !"null".equals(externalUserId)) {
//                qyweixinIdToOpenidBo.setOpenid(externalUserId);
//            }
//            qyweixinIdToOpenidBos.add(qyweixinIdToOpenidBo);
//        }
//        if(CollectionUtils.isNotEmpty(qyweixinIdToOpenidBos)) {
//            int i = qyweixinIdToOpenidDao.batchSaveQyweixinIdToOpenid(qyweixinIdToOpenidBos);
//            log.info("QyweixinAccountSyncServiceImpl.plainToEncryption2.getQYWXCorpBindInfo,qyweixinIdToOpenidBos={},i={}.", qyweixinIdToOpenidBos, i);
//        }
    }

    @Override
    public Result<Void> autoIdToOpenid() {
        //先删除客户转换失败的数据
        //查询会话存档的企业
        //查询会话可见范围的员工
        //转换
        //成功的入库
//        qyweixinIdToOpenidDao.deleteSwitchFailed();
//        com.facishare.open.qywx.save.result.Result<List<String>> result = messageGeneratingService.queryAllSetting();
//        if(!result.isSuccess() || CollectionUtils.isEmpty(result.getData())) {
//            return new Result<>();
//        }
//        for(String ea : result.getData()) {
//
//            Boolean aBoolean = oaNewBaseManager.canRunInNewBaseByFsEa(ea);
//            if(!aBoolean) {
//                continue;
//            }
//
//            log.info("QyweixinAccountSyncServiceImpl.autoIdToOpenid,ea={}.", ea);
//            switchAccountsThreadPool.execute(()-> this.switchMessageUser(ea));
//        }
        return null;
    }

    @Override
    public Result<Void> switchMessageUser(String ea) {
        return switchMessageUser2(ea, null);
    }

    @Override
    public Result<Void> switchMessageUser2(String ea, String outEa) {
//        GetPermitUserListResult userListResult = this.getPermitUserList(ea, outEa);
//        log.info("switchMessageUser2,userListResult={}",userListResult);
//        if(CollectionUtils.isNotEmpty(userListResult.getIds())) {
//            log.info("switchMessageUser2,userListResult.ids.size={}",userListResult.getIds().size());
//            com.facishare.open.qywx.accountbind.result.Result<QyweixinAccountEnterpriseMapping> enterpriseMappingResult = qyweixinAccountBindService.fsEaToOutEaResult2("qywx",
//                    ea,
//                    outEa);
//            if(!enterpriseMappingResult.isSuccess() || ObjectUtils.isEmpty(enterpriseMappingResult.getData())) {
//                return new Result<>();
//            }
//            //先判断数据库是否已经有了该数据
//            List<QyweixinIdToOpenidBo> qyweixinIdToOpenidBoList = new ArrayList<>();
//            List<List<String>> pageDataList = PageUtils.getPageList(userListResult.getIds(),50);
//            //分页查询，降低数据库查询的压力
//            for(List<String> page : pageDataList) {
//                List<QyweixinIdToOpenidBo> qyweixinIdToOpenidBoList2 = qyweixinIdToOpenidDao.getByPlaintextIds(enterpriseMappingResult.getData().getOutEa(),
//                        page);
//                log.info("switchMessageUser2,qyweixinIdToOpenidBoList2={}",qyweixinIdToOpenidBoList2);
//                qyweixinIdToOpenidBoList.addAll(qyweixinIdToOpenidBoList2);
//            }
//
//            log.info("switchMessageUser2,qyweixinIdToOpenidBoList={}",qyweixinIdToOpenidBoList);
//
//            List<String> plaintextIds = qyweixinIdToOpenidBoList.stream()
//                    .map(QyweixinIdToOpenidBo::getPlaintextId)
//                    .collect(Collectors.toList());
//            List<String> updatePlaintextIds = userListResult.getIds().stream()
//                    .filter(v -> !plaintextIds.contains(v))
//                    .collect(Collectors.toList());
//            if(CollectionUtils.isEmpty(updatePlaintextIds)) {
//                return new Result<>();
//            }
//            com.facishare.open.qywx.accountinner.result.Result<List<QyweixinOpenUserIdInfo>> accountsResult = qyWeixinManager.userId2OpenUserId(updatePlaintextIds, enterpriseMappingResult.getData().getOutEa());
//            log.info("QyweixinAccountSyncServiceImpl.switchMessageUser.getQYWXCorpBindInfo,updatePlaintextIds={},accounts={}.", updatePlaintextIds, accountsResult);
//            if(!accountsResult.isSuccess() || CollectionUtils.isEmpty(accountsResult.getData())) {
//                return new Result<>();
//            }
//            List<QyweixinIdToOpenidBo> qyweixinIdToOpenidBos = new LinkedList<>();
//            for(QyweixinOpenUserIdInfo info : accountsResult.getData()) {
//                QyweixinIdToOpenidBo qyweixinIdToOpenidBo = new QyweixinIdToOpenidBo();
//                qyweixinIdToOpenidBo.setCorpId(enterpriseMappingResult.getData().getOutEa());
//                qyweixinIdToOpenidBo.setPlaintextId(info.getUserid());
//                qyweixinIdToOpenidBo.setOpenid(info.getOpen_userid());
//                qyweixinIdToOpenidBo.setType(0);
//                qyweixinIdToOpenidBos.add(qyweixinIdToOpenidBo);
//            }
//            if(CollectionUtils.isNotEmpty(qyweixinIdToOpenidBos)) {
//                int i = qyweixinIdToOpenidDao.batchSaveQyweixinIdToOpenid(qyweixinIdToOpenidBos);
//                log.info("QyweixinAccountSyncServiceImpl.switchMessageUser.getQYWXCorpBindInfo,qyweixinIdToOpenidBos={},i={}.", qyweixinIdToOpenidBos, i);
//            }
//        }
        return new Result<>();
    }

    private GetPermitUserListResult getPermitUserList(String fsEa, String outEa) {
//        List<String> secret = qyweixinCorpBindDao.getConversionSecret(fsEa, outEa);
//        if(CollectionUtils.isEmpty(secret)) {
//            return null;
//        }
//        log.info("QyweixinAccountSyncServiceImpl.getPermitUserList secret={}, ea={}", secret, fsEa);
//        secret = secret.stream().map(SecurityUtil::decryptStr).collect(Collectors.toList());
//        if(StringUtils.isEmpty(outEa)) {
//            outEa = qyweixinCorpBindDao.getOutEa(fsEa, ConfigCenter.crm_domain);
//        }
//        if(StringUtils.isEmpty(outEa)) {
//            return null;
//        }
//        //走自建应用或代开发
//        com.facishare.open.qywx.accountinner.result.Result<String> tokenResult = qyWeixinManager.getToken(secret.get(0), outEa);
//        log.info("QyweixinGatewayInnerServiceImpl.getPermitUserList,token={}",tokenResult);
//        if(!tokenResult.isSuccess() || StringUtils.isEmpty(tokenResult.getData())) {
//            return null;
//        }
//        com.facishare.open.qywx.accountinner.result.Result<GetPermitUserListResult> result = qyWeixinManager.getPermitUserList(tokenResult.getData());
//        return result.getData();
        return null;
    }

    @Override
    public Result<List<QyweixinTransferCustomerResult>> externalContactTransferCustomer(QyweixinTransferCustomerInfo customerInfo, Boolean isResigned) {
        if(ObjectUtils.isEmpty(customerInfo)) {
            return Result.newInstance(ErrorRefer.PARAM_ERROR);
        }

//        com.facishare.open.qywx.accountbind.result.Result<QyweixinAccountEnterpriseMapping> result = qyweixinAccountBindService.fsEaToOutEaResult2("qywx",
//                customerInfo.getEa(),
//                customerInfo.getOutEa());
//        if(!result.isSuccess() && ObjectUtils.isEmpty(result)) {
//            return Result.newInstance(ErrorRefer.FS_EA_NOT_BIND);
//        }
//        String corpId = result.getData().getOutEa();

        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntities(OuterOaEnterpriseBindParams.builder().channel(ChannelEnum.qywx).fsEa(customerInfo.getEa()).outEa(customerInfo.getOutEa()).bindStatus(BindStatusEnum.normal).build());
        if(CollectionUtils.isEmpty(enterpriseBindEntities)) {
            return new Result<>();
        }

//        //员工id传的是fs的userId,直接在这转换
//        String handoverUserId = "E." + customerInfo.getEa() + "." + customerInfo.getHandoverUserId();
//        String takeoverUserId = "E." + customerInfo.getEa() + "." + customerInfo.getTakeoverUserId();
//        com.facishare.open.qywx.accountbind.result.Result<List<EmployeeAccountMatchResult>> outAccountResult = qyweixinAccountBindService.fsAccountToOutAccount2("qywx",
//                Lists.newArrayList(handoverUserId, takeoverUserId),
//                -1,
//                corpId);

        StringBuilder errorMsg = new StringBuilder();
        List<QyweixinTransferCustomerResult> transferCustomerResults = new ArrayList<>();
        for (OuterOaEnterpriseBindEntity enterpriseBindEntity : enterpriseBindEntities) {
            String corpId = enterpriseBindEntity.getOutEa();
            String appId = enterpriseBindEntity.getAppId();

            List<OuterOaEmployeeBindEntity> employeeBindEntities = outerOaEmployeeBindManager.batchGetEmployeeBindEntity(ChannelEnum.qywx, corpId, customerInfo.getEa(), appId, Lists.newArrayList(customerInfo.getHandoverUserId(), customerInfo.getTakeoverUserId()), null);
            if(CollectionUtils.isEmpty(employeeBindEntities) || employeeBindEntities.size() < 2) {
                log.info("QyweixinGatewayInnerServiceImpl.externalContactTransferCustomer,employeeBindEntities={}",employeeBindEntities);
                continue;
            }

            String outHandoverUserId = null;
            String outTakeoverUserId = null;
            for(OuterOaEmployeeBindEntity matchResult : employeeBindEntities) {
                if(StringUtils.equalsIgnoreCase(matchResult.getFsEmpId(),customerInfo.getHandoverUserId())) {
                    outHandoverUserId = matchResult.getOutEmpId();
                    continue;
                }
                if(StringUtils.equalsIgnoreCase(matchResult.getFsEmpId(),customerInfo.getTakeoverUserId())) {
                    outTakeoverUserId = matchResult.getOutEmpId();
                }
            }

            QyweixinTransferCustomer customer = new QyweixinTransferCustomer();
            customer.setExternalUserId(customerInfo.getExternalUserId());
            customer.setHandoverUserId(outHandoverUserId);
            customer.setTakeoverUserId(outTakeoverUserId);
            customer.setTransferSuccessMsg(customerInfo.getTransferSuccessMsg());
            com.facishare.open.qywx.accountinner.result.Result<List<QyweixinTransferCustomerResult>> customerResult = qyWeixinManager.externalContactTransferCustomer(customer, corpId, isResigned, appId);
            if (customerResult.isSuccess()) {
                transferCustomerResults.addAll(customerResult.getData());
            } else {
                errorMsg.append(corpId).append(":").append(customerResult.getMsg()).append(".");
            }
        }
        if(StringUtils.isNotEmpty(errorMsg) && CollectionUtils.isEmpty(transferCustomerResults)) {
            return new Result<>(ErrorRefer.TRANSFER_CUSTOMER_ERROR.getCode(), errorMsg.toString(),null);
        }

        log.info("QyweixinGatewayInnerServiceImpl.externalContactTransferCustomer,transferCustomerResults={}",transferCustomerResults);
        return new Result<>(transferCustomerResults);
    }

    @Override
    public Result<QyweixinTransferCustomerStatusResult> externalContactTransferResult(QyweixinTransferCustomerStatusInfo customerStatusInfo, Boolean isResigned) {
        if(ObjectUtils.isEmpty(customerStatusInfo)) {
            return Result.newInstance(ErrorRefer.PARAM_ERROR);
        }

        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntities(OuterOaEnterpriseBindParams.builder().channel(ChannelEnum.qywx).fsEa(customerStatusInfo.getEa()).outEa(customerStatusInfo.getOutEa()).bindStatus(BindStatusEnum.normal).build());
        if(CollectionUtils.isEmpty(enterpriseBindEntities)) {
            return new Result<>();
        }

        //排序一下
        enterpriseBindEntities = enterpriseBindEntities.stream()
                .sorted(Comparator.comparing(OuterOaEnterpriseBindEntity::getCreateTime).reversed())

                .collect(Collectors.toList());

        if (ObjectUtils.isNotEmpty(customerStatusInfo.getCursor())) {
            List<String> accountList = Splitter.on("@").splitToList(customerStatusInfo.getCursor());
            customerStatusInfo.setCursor(accountList.get(2));
            enterpriseBindEntities = filterEnterpriseBindEntities(enterpriseBindEntities, accountList.get(0), accountList.get(1), accountList.get(2));
        }
        if(CollectionUtils.isEmpty(enterpriseBindEntities)) {
            return new Result<>(new QyweixinTransferCustomerStatusResult());
        }

        StringBuilder errorMsg = new StringBuilder();
        QyweixinTransferCustomerStatusResult transferCustomerStatusResult = new QyweixinTransferCustomerStatusResult();
        for (OuterOaEnterpriseBindEntity enterpriseBindEntity : enterpriseBindEntities) {
            String corpId = enterpriseBindEntity.getOutEa();
            String appId = enterpriseBindEntity.getAppId();

            List<OuterOaEmployeeBindEntity> employeeBindEntities = outerOaEmployeeBindManager.batchGetEmployeeBindEntity(ChannelEnum.qywx, corpId, customerStatusInfo.getEa(), appId, Lists.newArrayList(customerStatusInfo.getHandoverUserId(), customerStatusInfo.getTakeoverUserId()), null);
            if (CollectionUtils.isEmpty(employeeBindEntities) || employeeBindEntities.size() < 2) {
                log.info("QyweixinGatewayInnerServiceImpl.externalContactTransferCustomer,employeeBindEntities={}", employeeBindEntities);
                continue;
            }

            String outHandoverUserId = null;
            String outTakeoverUserId = null;
            for(OuterOaEmployeeBindEntity matchResult : employeeBindEntities) {
                if(StringUtils.equalsIgnoreCase(matchResult.getFsEmpId(),customerStatusInfo.getHandoverUserId())) {
                    outHandoverUserId = matchResult.getOutEmpId();
                    continue;
                }
                if(StringUtils.equalsIgnoreCase(matchResult.getFsEmpId(),customerStatusInfo.getTakeoverUserId())) {
                    outTakeoverUserId = matchResult.getOutEmpId();
                }
            }

            customerStatusInfo.setHandoverUserId(outHandoverUserId);
            customerStatusInfo.setTakeoverUserId(outTakeoverUserId);

            com.facishare.open.qywx.accountinner.result.Result<QyweixinTransferCustomerStatusResult> customerStatusResult = qyWeixinManager.transferCustomerStatusResult(customerStatusInfo, corpId, isResigned, appId);
            if (customerStatusResult.isSuccess()) {
                transferCustomerStatusResult.setCustomer(customerStatusResult.getData().getCustomer());
                if (StringUtils.isEmpty(customerStatusResult.getData().getNextCursor()) && enterpriseBindEntities.get(enterpriseBindEntities.size() - 1).getId().equals(enterpriseBindEntity.getId())) {
                    transferCustomerStatusResult.setNextCursor(customerStatusResult.getData().getNextCursor());
                } else {
                    transferCustomerStatusResult.setNextCursor(corpId + "@" + appId + "@" + customerStatusResult.getData().getNextCursor());
                }
                break;
            } else {
                errorMsg.append(corpId).append(":").append(customerStatusResult.getMsg()).append(".");
            }
        }

        if(StringUtils.isNotEmpty(errorMsg) && CollectionUtils.isEmpty(transferCustomerStatusResult.getCustomer())) {
            return new Result<>(ErrorRefer.TRANSFER_CUSTOMER_ERROR.getCode(), errorMsg.toString(),null);
        }

        log.info("QyweixinGatewayInnerServiceImpl.externalContactTransferCustomer,transferCustomerStatusResult={}",transferCustomerStatusResult);
        return new Result<>(transferCustomerStatusResult);

//        com.facishare.open.qywx.accountbind.result.Result<QyweixinAccountEnterpriseMapping> result = qyweixinAccountBindService.fsEaToOutEaResult2("qywx",
//                customerStatusInfo.getEa(),
//                customerStatusInfo.getOutEa());
//        if(!result.isSuccess() && ObjectUtils.isEmpty(result)) {
//            return Result.newInstance(ErrorRefer.FS_EA_NOT_BIND);
//        }
//        String corpId = result.getData().getOutEa();
//
//        //员工id传的是fs的userId,直接在这转换
//        String handoverUserId = "E." + customerStatusInfo.getEa() + "." + customerStatusInfo.getHandoverUserId();
//        String takeoverUserId = "E." + customerStatusInfo.getEa() + "." + customerStatusInfo.getTakeoverUserId();
//        com.facishare.open.qywx.accountbind.result.Result<List<EmployeeAccountMatchResult>> outAccountResult = qyweixinAccountBindService.fsAccountToOutAccount2("qywx",
//                Lists.newArrayList(handoverUserId, takeoverUserId),
//                -1,
//                corpId);
//        if(!outAccountResult.isSuccess() || ObjectUtils.isEmpty(outAccountResult.getData()) || outAccountResult.getData().size() < 2) {
//            log.info("QyweixinGatewayInnerServiceImpl.externalContactTransferResult,outAccountResult={}",outAccountResult);
//            return Result.newInstance(ErrorRefer.FS_EMP_NOT_BIND);
//        }
//        String outHandoverUserId = null;
//        String outTakeoverUserId = null;
//        for(EmployeeAccountMatchResult matchResult : outAccountResult.getData()) {
//            if(StringUtils.equalsIgnoreCase(matchResult.getFsAccount(),handoverUserId)) {
//                outHandoverUserId = matchResult.getOutAccount();
//            }
//            if(StringUtils.equalsIgnoreCase(matchResult.getFsAccount(),takeoverUserId)) {
//                outTakeoverUserId = matchResult.getOutAccount();
//            }
//        }
//
//        customerStatusInfo.setHandoverUserId(outHandoverUserId);
//        customerStatusInfo.setTakeoverUserId(outTakeoverUserId);
//
//        com.facishare.open.qywx.accountinner.result.Result<QyweixinTransferCustomerStatusResult> customerStatusResult = qyWeixinManager.transferCustomerStatusResult(customerStatusInfo, corpId, isResigned);
//        if(!customerStatusResult.isSuccess()) {
//            return new Result<>(ErrorRefer.TRANSFER_CUSTOMER_ERROR.getCode(), customerStatusResult.getMsg(),null);
//        }
//        return new Result<>(customerStatusResult.getData());
    }

    @Override
    public Result<List<QyweixinTransferGroupChatResult>> externalContactTransferGroupChat(QyweixinTransferGroupChatInfo transferGroupChatInfo, Boolean isResigned) {
        if(ObjectUtils.isEmpty(transferGroupChatInfo)) {
            return Result.newInstance(ErrorRefer.PARAM_ERROR);
        }

        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntities(OuterOaEnterpriseBindParams.builder().channel(ChannelEnum.qywx).fsEa(transferGroupChatInfo.getEa()).outEa(transferGroupChatInfo.getOutEa()).bindStatus(BindStatusEnum.normal).build());
        if(CollectionUtils.isEmpty(enterpriseBindEntities)) {
            return Result.newInstance(ErrorRefer.FS_EA_NOT_BIND);
        }

        StringBuilder errorMsg = new StringBuilder();
        List<QyweixinTransferGroupChatResult> transferGroupChatResults = Lists.newArrayList();
        for (OuterOaEnterpriseBindEntity enterpriseBindEntity : enterpriseBindEntities) {
            String corpId = enterpriseBindEntity.getOutEa();
            String appId = enterpriseBindEntity.getAppId();

            List<OuterOaEmployeeBindEntity> employeeBindEntities = outerOaEmployeeBindManager.batchGetEmployeeBindEntity(ChannelEnum.qywx, corpId, transferGroupChatInfo.getEa(), appId, Lists.newArrayList(transferGroupChatInfo.getNewOwner()), null);
            if (CollectionUtils.isEmpty(employeeBindEntities) || employeeBindEntities.size() < 1) {
                log.info("QyweixinGatewayInnerServiceImpl.externalContactTransferGroupChat,employeeBindEntities={}", employeeBindEntities);
                continue;
            }

            transferGroupChatInfo.setNewOwner(employeeBindEntities.get(0).getOutEmpId());
            com.facishare.open.qywx.accountinner.result.Result<List<QyweixinTransferGroupChatResult>> transferGroupChatResult = qyWeixinManager.externalContactTransferGroupChat(transferGroupChatInfo, corpId, isResigned, appId);
            if (transferGroupChatResult.isSuccess()) {
                transferGroupChatResults.addAll(transferGroupChatResult.getData());
            } else {
                errorMsg.append(corpId).append(":").append(transferGroupChatResult.getMsg()).append(".");
            }
        }
        if(StringUtils.isNotEmpty(errorMsg) && ObjectUtils.isEmpty(transferGroupChatResults)) {
            return new Result<>(ErrorRefer.TRANSFER_CUSTOMER_ERROR.getCode(), errorMsg.toString(),null);
        }

        log.info("QyweixinGatewayInnerServiceImpl.externalContactTransferGroupChat,transferGroupChatResults={}",transferGroupChatResults);
        return new Result<>(transferGroupChatResults);
//        com.facishare.open.qywx.accountbind.result.Result<QyweixinAccountEnterpriseMapping> result = qyweixinAccountBindService.fsEaToOutEaResult2("qywx",
//                transferGroupChatInfo.getEa(),
//                transferGroupChatInfo.getOutEa());
//        if(!result.isSuccess() && ObjectUtils.isEmpty(result)) {
//            return Result.newInstance(ErrorRefer.FS_EA_NOT_BIND);
//        }
//        String corpId = result.getData().getOutEa();
//
//        //员工id传的是fs的userId,直接在这转换
//        String newOwnerId = "E." + transferGroupChatInfo.getEa() + "." + transferGroupChatInfo.getNewOwner();
//        com.facishare.open.qywx.accountbind.result.Result<List<EmployeeAccountMatchResult>> outAccountResult = qyweixinAccountBindService.fsAccountToOutAccount2("qywx",
//                Lists.newArrayList(newOwnerId),
//                -1,
//                corpId);
//        if(!outAccountResult.isSuccess() || ObjectUtils.isEmpty(outAccountResult.getData()) || outAccountResult.getData().size() < 1) {
//            log.info("QyweixinGatewayInnerServiceImpl.externalContactTransferGroupChat,outAccountResult={}",outAccountResult);
//            return Result.newInstance(ErrorRefer.FS_EMP_NOT_BIND);
//        }
//        transferGroupChatInfo.setNewOwner(outAccountResult.getData().get(0).getOutAccount());
//        com.facishare.open.qywx.accountinner.result.Result<List<QyweixinTransferGroupChatResult>> transferGroupChatResult = qyWeixinManager.externalContactTransferGroupChat(transferGroupChatInfo, corpId, isResigned);
//        if(!transferGroupChatResult.isSuccess()) {
//            return new Result<>(ErrorRefer.TRANSFER_CUSTOMER_ERROR.getCode(), transferGroupChatResult.getMsg(),null);
//        }
//        return new Result<>(transferGroupChatResult.getData());
    }

    @Override
    public Result<QyweixinUnassignedExternalContactResult> unassignedExternalContact(QyweixinUnassignedExternalContactInfo externalContactInfo) {
        if(ObjectUtils.isEmpty(externalContactInfo)) {
            return new Result<>();
        }

        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntities(OuterOaEnterpriseBindParams.builder().channel(ChannelEnum.qywx).fsEa(externalContactInfo.getEa()).outEa(externalContactInfo.getOutEa()).bindStatus(BindStatusEnum.normal).build());
        if(CollectionUtils.isEmpty(enterpriseBindEntities)) {
            return new Result<>();
        }

        //排序一下
        enterpriseBindEntities = enterpriseBindEntities.stream()
                .sorted(Comparator.comparing(OuterOaEnterpriseBindEntity::getCreateTime).reversed())

                .collect(Collectors.toList());

        if (ObjectUtils.isNotEmpty(externalContactInfo.getCursor())) {
            List<String> accountList = Splitter.on("@").splitToList(externalContactInfo.getCursor());
            externalContactInfo.setCursor(accountList.get(2));
            enterpriseBindEntities = filterEnterpriseBindEntities(enterpriseBindEntities, accountList.get(0), accountList.get(1), accountList.get(2));
        }
        if(CollectionUtils.isEmpty(enterpriseBindEntities)) {
            return new Result<>(new QyweixinUnassignedExternalContactResult());
        }

        StringBuilder errorMsg = new StringBuilder();
        QyweixinUnassignedExternalContactResult unassignedExternalContactResult = new QyweixinUnassignedExternalContactResult();
        for (OuterOaEnterpriseBindEntity enterpriseBindEntity : enterpriseBindEntities) {
            String corpId = enterpriseBindEntity.getOutEa();
            String appId = enterpriseBindEntity.getAppId();
            com.facishare.open.qywx.accountinner.result.Result<QyweixinUnassignedExternalContactResult> externalContactResult = qyWeixinManager.unassignedExternalContact(externalContactInfo, corpId, appId);
            if (externalContactResult.isSuccess()) {
                unassignedExternalContactResult.setInfo(externalContactResult.getData().getInfo());
                if (enterpriseBindEntities.get(enterpriseBindEntities.size() - 1).getId().equals(enterpriseBindEntity.getId())) {
                    //最后一个企业
                    unassignedExternalContactResult.setIsLast(externalContactResult.getData().getIsLast());
                } else {
                    unassignedExternalContactResult.setIsLast(Boolean.FALSE);
                }
                if (StringUtils.isEmpty(externalContactResult.getData().getNextCursor()) && enterpriseBindEntities.get(enterpriseBindEntities.size() - 1).getId().equals(enterpriseBindEntity.getId())) {
                    unassignedExternalContactResult.setNextCursor(externalContactResult.getData().getNextCursor());
                } else {
                    unassignedExternalContactResult.setNextCursor(corpId + "@" + appId + "@" + externalContactResult.getData().getNextCursor());
                }
                break;
            } else {
                errorMsg.append(corpId).append(":").append(externalContactResult.getMsg()).append(".");
            }
        }

        if(StringUtils.isNotEmpty(errorMsg) && CollectionUtils.isEmpty(unassignedExternalContactResult.getInfo())) {
            return new Result<>(ErrorRefer.INTERNAL_ERROR.getCode(), errorMsg.toString(),null);
        }

        return new Result<>(unassignedExternalContactResult);

//        com.facishare.open.qywx.accountbind.result.Result<QyweixinAccountEnterpriseMapping> result = qyweixinAccountBindService.fsEaToOutEaResult2("qywx",
//                externalContactInfo.getEa(),
//                externalContactInfo.getOutEa());
//        if(!result.isSuccess() && ObjectUtils.isEmpty(result)) {
//            return new Result<>();
//        }
//        String corpId = result.getData().getOutEa();
//        com.facishare.open.qywx.accountinner.result.Result<QyweixinUnassignedExternalContactResult> externalContactResult = qyWeixinManager.unassignedExternalContact(externalContactInfo, corpId);
//        if(!externalContactResult.isSuccess() || ObjectUtils.isEmpty(externalContactResult.getData())) {
//            return new Result<>(ErrorRefer.INTERNAL_ERROR.getCode(), externalContactResult.getMsg(),null);
//        }
//        return new Result<>(externalContactResult.getData());
    }

//    @Override
//    public Result<Void> updateCorpInfo(String ea) {
//        int sum = 0;
//        QyweixinCorpBindBo corpBindBo = new QyweixinCorpBindBo();
//        corpBindBo.setCorpId(getCorpIdByFsEa(ea));
//        List<QyweixinCorpBindBo> corpBindList = qyweixinCorpBindDao.findByEntity(corpBindBo);
//        if(CollectionUtils.isEmpty(corpBindList)) {
//            return new Result<>();
//        }
//        Result<QyweixinCorpInfo> corpInfoResult = this.getCorpInfo(ea, crmAppId);
//        if(!corpInfoResult.isSuccess() || ObjectUtils.isEmpty(corpInfoResult)) {
//            return new Result<>();
//        }
//        String corpName = corpInfoResult.getData().getCorpName();
//        if(StringUtils.isNotEmpty(corpName)) {
//            for(QyweixinCorpBindBo qyweixinCorpBindBo : corpBindList) {
//                qyweixinCorpBindBo.setCorpName(corpName);
//                qyweixinCorpBindDao.update(qyweixinCorpBindBo);
//                sum += 1;
//            }
//            log.info("QyweixinAccountSyncServiceImpl.updateCorpInfo,ea={},sum={}", ea, sum);
//        }
//        return new Result<>();
//    }

//    @Override
//    public Result<List<QyweixinExternalContactTransferInfo>> getExternalContactTransferInfo(QyweixinQueryTransferInfo transferInfo) {
//        List<QyweixinExternalContactTransferInfo> contactTransferInfoList = qyweixinExternalManager.getExternalContactTransfer(transferInfo);
//        return new Result<>(contactTransferInfoList);
//    }

    @Override
    public Result<Void> autoTransferStatus() {
//        //查询所有的外部联系人信息
//        List<QyweixinExternalContactTransferBo> handoverAndTakeover = qyweixinExternalManager.getHandoverAndTakeover();
//        if(CollectionUtils.isEmpty(handoverAndTakeover)) {
//            return new Result<>();
//        }
//        log.info("QyweixinAccountSyncServiceImpl.autoTransferStatus,handoverAndTakeover={}.", handoverAndTakeover);
//        //找出相关人员的状态
//        for(QyweixinExternalContactTransferBo externalContactTransferBo : handoverAndTakeover) {
//            com.facishare.open.qywx.accountbind.result.Result<QyweixinAccountEnterpriseMapping> qyweixinCorpIDResult =
//                    qyweixinAccountBindService.fsEaToOutEaResult("qywx", externalContactTransferBo.getEa());
//            if(!qyweixinCorpIDResult.isSuccess() || ObjectUtils.isEmpty(qyweixinCorpIDResult.getData())) {
//                continue;
//            }
//            int status = externalContactTransferBo.getHandoverUserStatus();
//            QyweixinTransferCustomerStatusInfo customerStatusInfo = new QyweixinTransferCustomerStatusInfo();
//            customerStatusInfo.setEa(externalContactTransferBo.getEa());
//            customerStatusInfo.setHandoverUserId(externalContactTransferBo.getHandoverUserId());
//            customerStatusInfo.setTakeoverUserId(externalContactTransferBo.getTakeoverUserId());
//            List<QyweixinCustomer> customerList;
//            if(status == 5) {
//                //离职继承状态查询
//                customerList = this.getCustomerStatusResult(customerStatusInfo, qyweixinCorpIDResult.getData().getOutEa(), true);
//            } else {
//                //在职继承状态查询
//                customerList = this.getCustomerStatusResult(customerStatusInfo, qyweixinCorpIDResult.getData().getOutEa(), false);
//            }
//            if(CollectionUtils.isEmpty(customerList)) {
//                continue;
//            }
//            //更新库
//            Integer count = qyweixinExternalManager.updateTransferStatus(customerList, externalContactTransferBo);
//            log.info("QyweixinAccountSyncServiceImpl.autoTransferStatus,externalContactTransferBo={},count={}.", externalContactTransferBo, count);
//        }
        return new Result<>();
    }

//    public List<QyweixinCustomer> getCustomerStatusResult(QyweixinTransferCustomerStatusInfo customerStatusInfo, String corpId, Boolean isResigned) {
//        log.info("QyweixinAccountSyncServiceImpl.getCustomerStatusResult,customerStatusInfo={},corpId={},isResigned={}.", customerStatusInfo, corpId, isResigned);
//        List<QyweixinCustomer> customerList = new LinkedList<>();
//        String nextCursor = null;
//        do{
//            com.facishare.open.qywx.accountinner.result.Result<QyweixinTransferCustomerStatusResult> customerStatusResult = qyWeixinManager.transferCustomerStatusResult(customerStatusInfo, corpId, isResigned);
//            if(customerStatusResult.isSuccess() && ObjectUtils.isNotEmpty(customerStatusResult.getData())) {
//                customerList.addAll(customerStatusResult.getData().getCustomer());
//                nextCursor = customerStatusResult.getData().getNextCursor();
//            }
//        } while (StringUtils.isNotEmpty(nextCursor));
//        return customerList;
//    }

    @Override
    public Result<Map<String, String>> getTransferMappingFields() {
//        Map<String, String> mappingFields = qyweixinExternalManager.getTransferMappingFields();
//        return new Result<>(mappingFields);
        return new Result<>();
    }

    @Override
    public Result<List<QyweixinTransferCustomerResult>> transferExternalContact(QyweixinTransferCustomerInfo customerInfo) {
        if(ObjectUtils.isEmpty(customerInfo)) {
            return new Result<>();
        }
        QyweixinTransferInfo transferInfo = qyweixinExternalManager.switchQyweixinTransferInfo(customerInfo);

        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntities(OuterOaEnterpriseBindParams.builder().channel(ChannelEnum.qywx).fsEa(customerInfo.getEa()).outEa(customerInfo.getOutEa()).bindStatus(BindStatusEnum.normal).build());
        if(CollectionUtils.isEmpty(enterpriseBindEntities)) {
            return new Result<>();
        }

        List<QyweixinTransferCustomerResult> transferCustomerResults = new ArrayList<>();
        for (OuterOaEnterpriseBindEntity enterpriseBindEntity : enterpriseBindEntities) {
            String corpId = enterpriseBindEntity.getOutEa();
            String appId = enterpriseBindEntity.getAppId();
            transferInfo.setCorpId(corpId);
            log.info("QyweixinAccountSyncServiceImpl.transferExternalContact,corpId={}", corpId);

            List<OuterOaEmployeeBindEntity> employeeBindEntities = outerOaEmployeeBindManager.batchGetEmployeeBindEntity(ChannelEnum.qywx, corpId, customerInfo.getEa(), appId, Lists.newArrayList(customerInfo.getHandoverUserId(), customerInfo.getTakeoverUserId()), null);

            String outHandoverUserId = null;
            String outTakeoverUserId = null;
            for(OuterOaEmployeeBindEntity matchResult : employeeBindEntities) {
                if(StringUtils.equalsIgnoreCase(matchResult.getFsEmpId(),customerInfo.getHandoverUserId())) {
                    outHandoverUserId = matchResult.getOutEmpId();
                    continue;
                }
                if(StringUtils.equalsIgnoreCase(matchResult.getFsEmpId(),customerInfo.getTakeoverUserId())) {
                    outTakeoverUserId = matchResult.getOutEmpId();
                }
            }

            transferInfo.setHandoverQWUserId(ObjectUtils.isNotEmpty(outHandoverUserId) ? outHandoverUserId : customerInfo.getHandoverUserId());
            transferInfo.setTakeoverQWUserId(ObjectUtils.isNotEmpty(outTakeoverUserId) ? outTakeoverUserId : customerInfo.getTakeoverUserId());

            //查询接替员工详情
            com.facishare.open.qywx.accountinner.result.Result<QyweixinUserDetailInfoRsp> takeoverUserInfoResult = qyWeixinManager.getUserInfo(appId, corpId, transferInfo.getTakeoverQWUserId());
            if(!takeoverUserInfoResult.isSuccess()) {
                continue;
            }

            transferInfo.setTakeoverUserName(takeoverUserInfoResult.getData().getName());

            //查询原跟进员工详情
            com.facishare.open.qywx.accountinner.result.Result<QyweixinUserDetailInfoRsp> handoverUserInfoResult = qyWeixinManager.getUserInfoResult(appId, corpId, transferInfo.getHandoverQWUserId());

            if(handoverUserInfoResult.isSuccess()) {
//            transferInfo.setHandoverUserName(handoverUserInfoResult.getName());
//            transferInfo.setHandoverUserStatus(1);
                //在职继承会导致原跟进人无法与当前客户再次沟通，现暂时屏蔽
                log.info("QyweixinAccountSyncServiceImpl.transferExternalContact,users of active employees do not need to inherit,customerInfo={},handoverUserInfoResult={}.", customerInfo, handoverUserInfoResult);
                continue;
            }

            if(handoverUserInfoResult.getCode().equals("60111") && !transferInfo.getHandoverUserId().contains(transferInfo.getHandoverQWUserId())) {
                transferInfo.setHandoverUserStatus(5);
            } else {
                transferInfo.setHandoverUserStatus(0);
            }

            if(CollectionUtils.isEmpty(employeeBindEntities) || employeeBindEntities.size() < 2 ||
                    transferInfo.getHandoverUserStatus() == 0 ||  ObjectUtils.isEmpty(takeoverUserInfoResult)) {
                continue;
            }

            com.facishare.open.qywx.accountinner.result.Result<List<QyweixinTransferCustomerResult>> customerResult = null;
            QyweixinTransferCustomer customer = new QyweixinTransferCustomer();
            customer.setExternalUserId(transferInfo.getExternalUserId());
            customer.setHandoverUserId(transferInfo.getHandoverQWUserId());
            customer.setTakeoverUserId(transferInfo.getTakeoverQWUserId());
            customer.setTransferSuccessMsg(transferInfo.getTransferSuccessMsg());
            if(transferInfo.getHandoverUserStatus() == 1) {
                //在职继承
                customerResult = qyWeixinManager.externalContactTransferCustomer(customer, corpId, false, appId);
            }

            if(transferInfo.getHandoverUserStatus() == 5) {
                //离职继承
                customerResult = qyWeixinManager.externalContactTransferCustomer(customer, corpId, true, appId);
            }

            if(ObjectUtils.isNotEmpty(customerResult)) {
                if(customerResult.isSuccess()) {
                    transferCustomerResults.addAll(customerResult.getData());
                }
            }
        }

        return new Result<>(transferCustomerResults);
//
//        transferInfo.setCorpId(corpId);
//        log.info("QyweixinAccountSyncServiceImpl.transferExternalContact,corpId={}", corpId);
//        //员工id传的是fs的userId,直接在这转换
//        String handoverUserId = "E." + customerInfo.getEa() + "." + customerInfo.getHandoverUserId();
//        String takeoverUserId = "E." + customerInfo.getEa() + "." + customerInfo.getTakeoverUserId();
//        com.facishare.open.qywx.accountbind.result.Result<List<EmployeeAccountMatchResult>> outAccountResult = qyweixinAccountBindService.fsAccountToOutAccount2("qywx",
//                Lists.newArrayList(handoverUserId, takeoverUserId),
//                -1,
//                corpId);
//
//        String outHandoverUserId = null;
//        String outTakeoverUserId = null;
//        for(EmployeeAccountMatchResult matchResult : outAccountResult.getData()) {
//            if(StringUtils.equalsIgnoreCase(matchResult.getFsAccount(),handoverUserId)) {
//                outHandoverUserId = matchResult.getOutAccount();
//            }
//            if(StringUtils.equalsIgnoreCase(matchResult.getFsAccount(),takeoverUserId)) {
//                outTakeoverUserId = matchResult.getOutAccount();
//            }
//        }
//
//        transferInfo.setHandoverQWUserId(ObjectUtils.isNotEmpty(outHandoverUserId) ? outHandoverUserId : customerInfo.getHandoverUserId());
//        transferInfo.setTakeoverQWUserId(ObjectUtils.isNotEmpty(outTakeoverUserId) ? outTakeoverUserId : customerInfo.getTakeoverUserId());
//
//        //查询接替员工详情
//        com.facishare.open.qywx.accountinner.result.Result<QyweixinUserDetailInfoRsp> takeoverUserInfoResult = qyWeixinManager.getUserInfo(repAppId, corpId, transferInfo.getTakeoverQWUserId());
//        if(takeoverUserInfoResult.isSuccess() && ObjectUtils.isNotEmpty(takeoverUserInfoResult.getData())) {
//            transferInfo.setTakeoverUserName(takeoverUserInfoResult.getData().getName());
//        }
//
//        //查询原跟进员工详情
//        com.facishare.open.qywx.accountinner.result.Result<QyweixinUserDetailInfoRsp> handoverUserInfoResult = qyWeixinManager.getUserInfoResult(repAppId, corpId, transferInfo.getHandoverQWUserId());
//
//        if(handoverUserInfoResult.isSuccess()) {
////            transferInfo.setHandoverUserName(handoverUserInfoResult.getName());
////            transferInfo.setHandoverUserStatus(1);
//            //在职继承会导致原跟进人无法与当前客户再次沟通，现暂时屏蔽
//            log.info("QyweixinAccountSyncServiceImpl.transferExternalContact,users of active employees do not need to inherit,customerInfo={},handoverUserInfoResult={}.", customerInfo, handoverUserInfoResult);
//            return new Result<>();
//        } else if(handoverUserInfoResult.getCode().equals("60111") && !transferInfo.getHandoverUserId().contains(transferInfo.getHandoverQWUserId())) {
//            transferInfo.setHandoverUserStatus(5);
//        } else {
//            transferInfo.setHandoverUserStatus(0);
//        }
//
//        if(ObjectUtils.isEmpty(outAccountResult.getData()) || outAccountResult.getData().size() < 2 ||
//                transferInfo.getHandoverUserStatus() == 0 ||  ObjectUtils.isEmpty(takeoverUserInfoResult)) {
//            transferInfo.setErrorCode(1);
//            transferInfo.setSyncStatus(1);
//            Thread thread = new Thread(() -> qyweixinExternalManager.saveExternalContactTransfer(transferInfo));
//            thread.start();
//            return new Result<>();
//        }
//
//        com.facishare.open.qywx.accountinner.result.Result<List<QyweixinTransferCustomerResult>> customerResult = null;
//        QyweixinTransferCustomer customer = new QyweixinTransferCustomer();
//        customer.setExternalUserId(transferInfo.getExternalUserId());
//        customer.setHandoverUserId(transferInfo.getHandoverQWUserId());
//        customer.setTakeoverUserId(transferInfo.getTakeoverQWUserId());
//        customer.setTransferSuccessMsg(transferInfo.getTransferSuccessMsg());
//        if(transferInfo.getHandoverUserStatus() == 1) {
//            //在职继承
//            customerResult = qyWeixinManager.externalContactTransferCustomer(customer, corpId, false);
//        }
//
//        if(transferInfo.getHandoverUserStatus() == 5) {
//            //离职继承
//            customerResult = qyWeixinManager.externalContactTransferCustomer(customer, corpId, true);
//        }
//        List<QyweixinTransferCustomerResult> customerResultList = new LinkedList<>();
//        if(ObjectUtils.isNotEmpty(customerResult)) {
//            if(customerResult.isSuccess()) {
//                customerResultList.addAll(customerResult.getData());
//            } else {
//                for(String userId : transferInfo.getExternalUserId()) {
//                    QyweixinTransferCustomerResult transferCustomerResult = new QyweixinTransferCustomerResult();
//                    transferCustomerResult.setExternalUserId(userId);
//                    customerResultList.add(transferCustomerResult);
//                }
//            }
//
//        }
//        log.info("QyweixinAccountSyncServiceImpl.transferExternalContact,customerResult={}", customerResult);
//
//        if(ObjectUtils.isNotEmpty(customerResultList)) {
//            transferInfo.setTransferCustomerResults(customerResultList);
//            //异步保存结果
//            Thread thread = new Thread(() -> qyweixinExternalManager.saveExternalContactTransfer(transferInfo));
//            thread.start();
//        }
//        return new Result<>(customerResultList);
    }

//    @Override
//    public Result<Integer> deleteExternalContactTransfers(List<Integer> deleteIds) {
//        if(CollectionUtils.isEmpty(deleteIds)) {
//            return new Result<>(0);
//        }
//        return new Result<>(qyweixinExternalManager.deleteExternalContactTransfers(deleteIds));
//    }

    public Result<FsEmployeeBasicInfo> getEmployeeBasicInfoByMobile(String ea, String mobile) {
        EmployeeDto employeeDto = fsManager.getEmployeesDtoByEnterpriseAndMobile(ea, mobile);
        if(ObjectUtils.isEmpty(employeeDto)) {
            return new Result<>();
        }

        List<OuterOaEmployeeBindEntity> employeeBindEntities = outerOaEmployeeBindManager.getEntitiesByNotPage(OuterOaEmployeeBindParams.builder().channel(ChannelEnum.qywx).fsEa(ea).fsEmpId(String.valueOf(employeeDto.getEmployeeId())).build());

        if(CollectionUtils.isEmpty(employeeBindEntities)) {
            return new Result<>();
        }

        Department department = fsManager.getDepartment(ea, employeeDto.getMainDepartmentId());

        FsEmployeeBasicInfo fsEmployeeBasicInfo = new FsEmployeeBasicInfo();
        fsEmployeeBasicInfo.setEa(ea);
        fsEmployeeBasicInfo.setEmployeeId(employeeDto.getEmployeeId());
        fsEmployeeBasicInfo.setEmployeeName(employeeDto.getFullName());
        fsEmployeeBasicInfo.setEmployeeNickName(employeeDto.getName());
        fsEmployeeBasicInfo.setEmployeePhone(employeeDto.getMobile());
        if(ObjectUtils.isNotEmpty(department)) {
            fsEmployeeBasicInfo.setMainDepartmentId(employeeDto.getMainDepartmentId());
            fsEmployeeBasicInfo.setMainDepartmentName(department.getName());
        }
        return new Result<>(fsEmployeeBasicInfo);
    }

    @Deprecated
    @Override
    public Result<List<QyweixinGetOpenIdResult>> getOpenIds(String ea, List<String> plaintextIds, List<String> openids) {
        return getOpenIds2(ea, plaintextIds, openids, null);
    }

    @Override
    public Result<List<QyweixinGetOpenIdResult>> getOpenIds2(String ea, List<String> plaintextIds, List<String> openids, String outEa) {
        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntities(OuterOaEnterpriseBindParams.builder().channel(ChannelEnum.qywx).fsEa(ea).outEa(outEa).bindStatus(BindStatusEnum.normal).build());
        if(CollectionUtils.isEmpty(enterpriseBindEntities)) {
            return new Result<>();
        }
        Set<QyweixinIdToOpenidEntity> qyweixinIds = new HashSet<>();
        for (OuterOaEnterpriseBindEntity enterpriseBindEntity : enterpriseBindEntities) {
            String corpId = enterpriseBindEntity.getOutEa();
            if(CollectionUtils.isNotEmpty(plaintextIds)) {
                List<QyweixinIdToOpenidEntity> qyweixinIdToOpenidEntityList = qyweixinIdToOpenidManager.getEntities(corpId, plaintextIds, null);
                if(CollectionUtils.isNotEmpty(qyweixinIdToOpenidEntityList)) {
                    qyweixinIds.addAll(qyweixinIdToOpenidEntityList);
                }
            }
            if(CollectionUtils.isNotEmpty(openids)) {
                List<QyweixinIdToOpenidEntity> qyweixinIdToPlaintextIdEntityList = qyweixinIdToOpenidManager.getEntities(corpId, null, openids);
                if(CollectionUtils.isNotEmpty(qyweixinIdToPlaintextIdEntityList)) {
                    qyweixinIds.addAll(qyweixinIdToPlaintextIdEntityList);
                }
            }
        }

        List<QyweixinGetOpenIdResult> qyweixinGetOpenIdResultList = qyweixinIds.stream()
                .map(id -> {
                    QyweixinGetOpenIdResult result = new QyweixinGetOpenIdResult();
                    result.setCorpId(id.getOutEa());
                    result.setPlaintextId(id.getPlaintextId());
                    result.setOpenid(id.getOpenid());
                    result.setType(id.getType());
                    return result;
                })
                .collect(Collectors.toList());
        return new Result<>(qyweixinGetOpenIdResultList);
    }

    @Override
    public Result<List<EmployeeDto>> getFsEmpUser(String fsEa, Integer employeeId, List<Integer> userList) {
        if(employeeId == null) {
            employeeId = 1000;
        }
        return new Result<>(fsManager.getEmployeeInfos(fsEa, employeeId, userList));
    }

    private List<QyweixinGetOpenIdResult> voToResult(List<QyweixinIdToOpenidEntity> qyweixinIdToOpenidBoList) {
        List<QyweixinGetOpenIdResult> resultArrayList = new ArrayList<>();
        qyweixinIdToOpenidBoList.stream().forEach(item -> {
            QyweixinGetOpenIdResult result = new QyweixinGetOpenIdResult();
            BeanUtils.copyProperties(item, result);
            resultArrayList.add(result);
        });
        return resultArrayList;
    }

    @Override
    public Result<Void> uploadOaConnectorData(OaConnectorDataModel oaConnectorDataModel) {
        oaConnectorDataManager.send(oaConnectorDataModel);
        return new Result<>();
    }

    @Override
    public Result<Void> uploadOaConnectorOpenData(OAConnectorOpenDataModel oaConnectorOpenDataModel) {
        oaConnectorOpenDataManager.send(oaConnectorOpenDataModel);
        return new Result<>();
    }

    @Deprecated
    @Override
    public Result<List<QyweixinEmployeeInfoResult>> batchGetEmployeeInfo2(String fsEa, List<String> userIds) {
        return batchGetEmployeeInfo21(fsEa, userIds, null);
    }

    @Override
    public Result<List<QyweixinEmployeeInfoResult>> batchGetEmployeeInfo21(String fsEa, List<String> userIds, String outEa) {
        //查询企业绑定关系
        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntities(OuterOaEnterpriseBindParams.builder().channel(ChannelEnum.qywx).fsEa(fsEa).outEa(outEa).build());
        if(CollectionUtils.isEmpty(enterpriseBindEntities)) {
            return Result.newInstance(ErrorRefer.OUT_EA_NOT_BIND);
        }

        List<OuterOaEmployeeBindEntity> employeeBindEntities = outerOaEmployeeBindManager.batchGetEmployeeBindEntity(ChannelEnum.qywx, null, fsEa, enterpriseBindEntities.get(0).getAppId(), userIds, null);
        //Map<String, String> outAccountMap,key 为fsEmpId,value 为OuterOaEmployeeBindEntity
        Map<String, OuterOaEmployeeBindEntity> outAccountMap = employeeBindEntities.stream()
                .filter(entity -> entity.getFsEmpId() != null) // 过滤掉 fsEmpId 为 null 的对象
                .collect(Collectors.toMap(
                        OuterOaEmployeeBindEntity::getFsEmpId,
                        entity -> entity,
                        (existingValue, newValue) -> existingValue
                ));

        List<QyweixinEmployeeInfoResult> results = new LinkedList<>();
        for (String userId : userIds) {
            QyweixinEmployeeInfoResult result = new QyweixinEmployeeInfoResult();
            result.setFsUserId(userId);
            //判断员工是否已绑定
            if(!outAccountMap.containsKey(userId)) {
                result.setErrorCode(ErrorRefer.QYWX_EMP_MAPPING_NOT_EXIST.getCode());
                result.setErrorMsg(ErrorRefer.QYWX_EMP_MAPPING_NOT_EXIST.getQywxCode());
            } else {
                String outUserId = outAccountMap.get(userId).getOutEmpId();
                String appId = outAccountMap.get(userId).getAppId();
                String corpId = outAccountMap.get(userId).getOutEa();
                result.setOutUserId(outUserId);
                //获取员工详情
                com.facishare.open.qywx.accountinner.result.Result<QyweixinUserDetailInfoRsp> qyweixinUserDetailInfoRspResult = qyWeixinManager.getUserInfo(appId, corpId, outUserId);
                if(qyweixinUserDetailInfoRspResult.isSuccess()){
                    QyweixinUserDetailInfoRsp qyweixinUserDetailInfoRsp = qyweixinUserDetailInfoRspResult.getData();
                    QyweixinEmployeeInfo employeeInfo = convertToQyweixinEmployeeInfo(qyweixinUserDetailInfoRsp);
                    result.setQyweixinEmployeeInfo(employeeInfo);
                } else {
                    result.setErrorCode(qyweixinUserDetailInfoRspResult.getCode());
                    result.setErrorMsg(qyweixinUserDetailInfoRspResult.getMsg());
                }
            }
            results.add(result);
        }
        return new Result<>(results);
    }

    @Override
    public Result<EnterpriseTrialInfo> getEnterpriseTrialInfo(String fsEa) {
        //查询企业绑定关系
        EnterpriseTrialInfo enterpriseTrialInfo = new EnterpriseTrialInfo();
        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntities(OuterOaEnterpriseBindParams.builder().channel(ChannelEnum.qywx).fsEa(fsEa).bindType(BindTypeEnum.auto).build());

        if(CollectionUtils.isEmpty(enterpriseBindEntities)) {
            enterpriseTrialInfo.setBindType(BindTypeEnum.manual);
            return new Result<>(enterpriseTrialInfo);
        }

        OuterOaEnterpriseBindEntity enterpriseBindEntity = enterpriseBindEntities.get(0);
        String outEa = enterpriseBindEntity.getOutEa();
        enterpriseTrialInfo.setOutEa(outEa);
        QywxConnectorVo qywxConnectorVo;
        if(StringUtils.isNotEmpty(enterpriseBindEntity.getConnectInfo())) {
            qywxConnectorVo = JSON.parseObject(enterpriseBindEntity.getConnectInfo(), QywxConnectorVo.class);
        } else {
            qywxConnectorVo = new QywxConnectorVo();
        }
        String enterpriseExtend = String.format(
                "{\"isFirstLand\":%s,\"isRetainInformation\":%s}",
                qywxConnectorVo.getIsFirstLand(),
                qywxConnectorVo.getIsRetainInformation()
        );
        enterpriseTrialInfo.setExtend(enterpriseExtend);
        Gson gson = new Gson();
        if(qywxConnectorVo.getIsFirstLand() == Boolean.TRUE) {
            //更新至数据库
            qywxConnectorVo.setIsFirstLand(Boolean.FALSE);
            enterpriseBindEntity.setConnectInfo(gson.toJson(qywxConnectorVo));
            outerOaEnterpriseBindManager.updateById(enterpriseBindEntity);
        }

        //云动代客下单的订单详情不会与企微下单的订单详情保持一致，从企微下的试用订单不会保持在数据库中，如果有数据，证明是正式订单
        //企微支付成功事件，如果是代客下单，就丢弃了，这里只要是有订单都不弹框
        //TODO 后续企微保存试用订单，这里的逻辑需要改动
        List<OuterOaOrderInfoEntity> oaOrderInfoEntities = outerOaOrderInfoManager.getEntities(OuterOaOrderInfoParams.builder().channel(ChannelEnum.qywx).paidOutEa(outEa).build());
        log.info("QyweixinAccountSyncServiceImpl.getEnterpriseTrialInfo,oaOrderInfoEntities={}", oaOrderInfoEntities);
        if(CollectionUtils.isEmpty(oaOrderInfoEntities)) {
            //查询最新的订单，购买了存在正式订单的情况下，再下试用订单，这个也默认需要弹窗
            //企微没有保存试用订单
            HeaderObj headerObj = new HeaderObj(ConfigCenter.MASTER_EA, CrmConstants.SYSTEM_USER);
            SearchQuery searchSalesOrderQuery = new SearchQuery();
            searchSalesOrderQuery.addFilter(ConfigCenter.SALES_ORDER_EA_FIELD_NAME, Lists.newArrayList(fsEa), FilterOperatorEnum.EQ);
            searchSalesOrderQuery.addFilter(ConfigCenter.OBJ_LIFE_STATUS_FIELD_NAME, Lists.newArrayList("normal"), FilterOperatorEnum.EQ);
            searchSalesOrderQuery.setLimit(100);
            searchSalesOrderQuery.setOffset(0);
            ControllerListArg salesOrderControllerListArg = new ControllerListArg();
            salesOrderControllerListArg.setSearchQuery(searchSalesOrderQuery);
            com.fxiaoke.crmrestapi.common.result.Result<Page<ObjectData>> salesOrderPageResult = metadataControllerService.list(headerObj, "SalesOrderObj", salesOrderControllerListArg);
            log.info("QyweixinAccountSyncServiceImpl.getEnterpriseTrialInfo,headerObj={},salesOrderControllerListArg={},salesOrderPageResult={}", headerObj, salesOrderControllerListArg, salesOrderPageResult);
            if(!salesOrderPageResult.isSuccess() || ObjectUtils.isEmpty(salesOrderPageResult.getData()) || CollectionUtils.isEmpty(salesOrderPageResult.getData().getDataList())) {
                return Result.newInstance(ErrorRefer.OUT_EA_NOT_BIND);
            }
            //找到最新的一个订单
            List<ObjectData> salesOrderList = salesOrderPageResult.getData().getDataList().stream()
                    .sorted(Comparator.comparing(ObjectData::getCreateTime).reversed())
                    .collect(Collectors.toList());
            ObjectData salesOrder = salesOrderList.get(0);
            log.info("QyweixinAccountSyncServiceImpl.getEnterpriseTrialInfo,salesOrder={}", salesOrder);
            //判断订单类型
            if(Integer.parseInt(salesOrder.get(ConfigCenter.SALES_ORDER_TYPE_FIELD_NAME).toString()) == 2) {
                //最新订单是试用订单，还得查询关联的产品订单
                SearchQuery searchSalesOrderProductQuery = new SearchQuery();
                searchSalesOrderProductQuery.addFilter(ConfigCenter.SALES_ORDER_PRODUCT_ID_FIELD_NAME, Lists.newArrayList(salesOrder.getId()), FilterOperatorEnum.EQ);
                searchSalesOrderProductQuery.addFilter(ConfigCenter.OBJ_LIFE_STATUS_FIELD_NAME, Lists.newArrayList("normal"), FilterOperatorEnum.EQ);
                searchSalesOrderProductQuery.setLimit(100);
                searchSalesOrderProductQuery.setOffset(0);
                ControllerListArg salesOrderProductControllerListArg = new ControllerListArg();
                salesOrderProductControllerListArg.setSearchQuery(searchSalesOrderProductQuery);
                com.fxiaoke.crmrestapi.common.result.Result<Page<ObjectData>> salesOrderProductPageResult = metadataControllerService.list(headerObj, "SalesOrderProductObj", salesOrderProductControllerListArg);
                log.info("QyweixinAccountSyncServiceImpl.getEnterpriseTrialInfo,salesOrderProductControllerListArg={},salesOrderProductPageResult={}", salesOrderProductControllerListArg, salesOrderProductPageResult);
                if(!salesOrderProductPageResult.isSuccess() || ObjectUtils.isEmpty(salesOrderProductPageResult.getData()) || CollectionUtils.isEmpty(salesOrderProductPageResult.getData().getDataList())) {
                    return Result.newInstance(ErrorRefer.OUT_EA_NOT_BIND);
                }
                ObjectData salesOrderProduct = salesOrderProductPageResult.getData().getDataList().get(0);
                enterpriseTrialInfo.setIsTrial(Boolean.TRUE);
                BigDecimal startTimeBD = new BigDecimal(salesOrderProduct.get(ConfigCenter.SALES_ORDER_PRODUCT_SERVICE_START_TIME_FIELD_NAME).toString());
                long startTime = Long.parseLong(startTimeBD.toPlainString());
                enterpriseTrialInfo.setBeginTime(new Timestamp(startTime));
                BigDecimal endTimeBD = new BigDecimal(salesOrderProduct.get(ConfigCenter.SALES_ORDER_PRODUCT_SERVICE_END_TIME_FIELD_NAME).toString());
                long endTime = Long.parseLong(endTimeBD.toPlainString());
                enterpriseTrialInfo.setEndTime(new Timestamp(endTime));
            } else {
                enterpriseTrialInfo.setIsTrial(Boolean.FALSE);
            }
        } else {
            enterpriseTrialInfo.setIsTrial(Boolean.FALSE);
        }

        enterpriseTrialInfo.setBindType(BindTypeEnum.auto);
        return new Result<>(enterpriseTrialInfo);
    }

    @Override
    public Result<FsEmployeeDetailInfo> getFsCurEmployeeDetailInfo(Integer ei, Integer userId) {
        com.facishare.open.order.contacts.proxy.api.result.Result<List<EmployeeDto>> employeeDtoListResult = fsEmployeeServiceProxy.batchGetEmployeeDto(ei, Lists.newArrayList(userId));
        log.info("QyweixinAccountSyncServiceImpl.getEnterpriseTrialInfo,employeeDtoListResult={}", employeeDtoListResult);
        if(!employeeDtoListResult.isSuccess()) {
            return Result.newInstance(ErrorRefer.OUT_EA_NOT_BIND);
        }
        com.facishare.open.order.contacts.proxy.api.result.Result<BatchGetRoleCodesByEmployeeIds.Result> employeeRoleCodesResult = fsEmployeeServiceProxy.batchGetEmployeeRoleCodes(ei, Lists.newArrayList(userId));
        log.info("QyweixinAccountSyncServiceImpl.getEnterpriseTrialInfo,employeeDtoListResult={}", employeeDtoListResult);
        if(!employeeRoleCodesResult.isSuccess()) {
            return Result.newInstance(ErrorRefer.OUT_EA_NOT_BIND);
        }
        FsEmployeeDetailInfo info = new FsEmployeeDetailInfo();
        info.setEnterpriseId(ei);
        info.setEmployeeId(userId);
        info.setFullName(employeeDtoListResult.getData().get(0).getFullName());
        info.setName(employeeDtoListResult.getData().get(0).getName());
        info.setRoleCodes(employeeRoleCodesResult.getData().getEmployeeIdRolesMap().get(String.valueOf(userId)));
        return new Result<>(info);
    }

    @Deprecated
    @Override
    public Result<Void> updateEnterpriseExtend(String fsEa, String extendField, Object extendValue) {
        return updateEnterpriseExtend2(fsEa, extendField, extendValue, null);
    }

    @Override
    public Result<Void> updateEnterpriseExtend2(String fsEa, String extendField, Object extendValue, String outEa) {
        //通过fsEa查询企业绑定关系
        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntities(OuterOaEnterpriseBindParams.builder().channel(ChannelEnum.qywx).fsEa(fsEa).build());

        if(CollectionUtils.isEmpty(enterpriseBindEntities)) {
            return Result.newInstance(ErrorRefer.OUT_EA_NOT_BIND);
        }

        for (OuterOaEnterpriseBindEntity enterpriseBindEntity : enterpriseBindEntities) {
            QywxConnectorVo qywxConnectorVo = JSON.parseObject(enterpriseBindEntity.getConnectInfo(), QywxConnectorVo.class);
            //更新字段，要先判断字段是否符合
            switch(extendField) {
                case "isFirstLand":
                    if(StringUtils.equalsIgnoreCase(extendValue.toString(), Boolean.FALSE.toString())
                            || StringUtils.equalsIgnoreCase(extendValue.toString(), Boolean.TRUE.toString())) {
                        qywxConnectorVo.setIsFirstLand(Boolean.valueOf(extendValue.toString()));
                    }
                    break;
                case "isRetainInformation":
                    if(StringUtils.equalsIgnoreCase(extendValue.toString(), Boolean.FALSE.toString())
                            || StringUtils.equalsIgnoreCase(extendValue.toString(), Boolean.TRUE.toString())) {
                        qywxConnectorVo.setIsRetainInformation(Boolean.valueOf(extendValue.toString()));
                    }
                    break;
                default:
                    log.info("QyweixinAccountSyncServiceImpl.updateEnterpriseExtend,This field is not supported,extendField={}",extendField);
                    return Result.newInstance(ErrorRefer.PARAM_ERROR);
            }
            //保存数据
            enterpriseBindEntity.setConnectInfo(JSON.toJSONString(qywxConnectorVo));
            Integer count = outerOaEnterpriseBindManager.updateById(enterpriseBindEntity);
            log.info("QyweixinAccountSyncServiceImpl.updateEnterpriseExtend,count={}",count);
        }
        return new Result<>();
    }

    @Override
    public Result<QyweixinDepartmentInfo> getDepartmentInfo(String fsEa, String departmentId) {
        return getDepartmentInfo2(fsEa, departmentId, null);
    }

    @Override
    public Result<QyweixinDepartmentInfo> getDepartmentInfo2(String fsEa, String departmentId, String outEa) {
        //查询企业绑定关系
        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntities(OuterOaEnterpriseBindParams.builder().channel(ChannelEnum.qywx).fsEa(fsEa).outEa(outEa).bindStatus(BindStatusEnum.normal).build());

        if(CollectionUtils.isEmpty(enterpriseBindEntities)) {
            return Result.newInstance(ErrorRefer.OUT_EA_NOT_BIND);
        }

        OuterOaEnterpriseBindEntity enterpriseBindEntity = enterpriseBindEntities.get(0);
        String appId = enterpriseBindEntity.getAppId();
        String corpId = enterpriseBindEntity.getOutEa();

        //查询企微部门详情
        com.facishare.open.qywx.accountinner.result.Result<QyweixinDepartmentInfoRsp> departmentInfoRspResult = qyWeixinManager.getDepartmentInfo(appId, corpId, departmentId);
        if(departmentInfoRspResult.isSuccess() && ObjectUtils.isNotEmpty(departmentInfoRspResult.getData()) && ObjectUtils.isNotEmpty(departmentInfoRspResult.getData().getDepartment())) {
            QyweixinDepartmentRsp rsp = departmentInfoRspResult.getData().getDepartment();
            QyweixinDepartmentInfo info = new QyweixinDepartmentInfo();
            info.setAppId(appId);
            info.setId(departmentId);
            info.setName(rsp.getName());
            info.setOrder(rsp.getOrder());
            info.setParentId(rsp.getParentid());
            return new Result<>(info);
        }
        return new Result<>(ErrorRefer.INTERNAL_ERROR.getCode(), departmentInfoRspResult.getMsg(),null);
    }

    @Override
    public Result<String> getAppAccessToken(String outEa, String appId) {
        //查询企业绑定关系
        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntities(OuterOaEnterpriseBindParams.builder().channel(ChannelEnum.qywx).outEa(outEa).appId(appId).build());

        if(CollectionUtils.isEmpty(enterpriseBindEntities)) {
            return Result.newInstance(ErrorRefer.OUT_EA_NOT_BIND);
        }

        OuterOaEnterpriseBindEntity enterpriseBindEntity = enterpriseBindEntities.get(0);
        String corpId = enterpriseBindEntity.getOutEa();
        String finalAppId = enterpriseBindEntity.getAppId();

        //判断appId是否正常
        OuterOaAppInfoEntity oaAppInfoEntity = outerOaAppInfoManager.getEntity(ChannelEnum.qywx, corpId, finalAppId);
        if (ObjectUtils.isEmpty(oaAppInfoEntity) || oaAppInfoEntity.getStatus() == OuterOaAppInfoStatusEnum.stop) {
            return Result.newInstance(ErrorRefer.QYWX_NOT_INSTALL_VALID_FS_APP);
        }
        //获取token
        com.facishare.open.qywx.accountinner.result.Result<String> tokenResult = qyWeixinManager.getCorpAccessToken(finalAppId, corpId, false);
        if(!tokenResult.isSuccess()) {
            return new Result<>(tokenResult.getCode(), tokenResult.getMsg(),null);
        }
        return new Result<>(tokenResult.getData());
    }

    @Override
    public Result<String> getAppAccessTokenByFsEa(String fsEa) {
        //客开的只能查一个
        //查询企业绑定关系
        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntities(OuterOaEnterpriseBindParams.builder().channel(ChannelEnum.qywx).fsEa(fsEa).build());

        if(CollectionUtils.isEmpty(enterpriseBindEntities)) {
            return Result.newInstance(ErrorRefer.OUT_EA_NOT_BIND);
        }

        String outEa = enterpriseBindEntities.get(0).getOutEa();
        String appId = enterpriseBindEntities.get(0).getAppId();
        return getAppAccessToken(outEa,appId);
    }

    @Override
    public Result<String> getOutEaResultByFsEa(String fsEa) {
        //这些接口没法做
        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntities(OuterOaEnterpriseBindParams.builder().channel(ChannelEnum.qywx).fsEa(fsEa).build());

        if(CollectionUtils.isEmpty(enterpriseBindEntities)) {
            return Result.newInstance(ErrorRefer.OUT_EA_NOT_BIND);
        }
        return new Result<>(enterpriseBindEntities.get(0).getOutEa());
    }

    @Override
    public Result<List<String>> getOutEaResultListByFsEa(String fsEa) {
        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntities(OuterOaEnterpriseBindParams.builder().channel(ChannelEnum.qywx).fsEa(fsEa).build());

        if(CollectionUtils.isEmpty(enterpriseBindEntities)) {
            return Result.newInstance(ErrorRefer.OUT_EA_NOT_BIND);
        }
        List<String> outEaList = enterpriseBindEntities.stream()
                .map(OuterOaEnterpriseBindEntity::getOutEa)
                .collect(Collectors.toList());
        return new Result<>(outEaList);
    }

    @Override
    public Result<List<String>> getFsEaResultByOutEa(String outEa) {
        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntities(OuterOaEnterpriseBindParams.builder().channel(ChannelEnum.qywx).outEa(outEa).build());

        if(CollectionUtils.isEmpty(enterpriseBindEntities)) {
            return Result.newInstance(ErrorRefer.OUT_EA_NOT_BIND);
        }
        return new Result<>(enterpriseBindEntities.stream().map(OuterOaEnterpriseBindEntity::getFsEa).collect(Collectors.toList()));
    }

    @Override
    public Result<Void> autoGetUserAndDepartmentInfo() {
        int pageNum = 0;
        int size;
        Map<String, List<OaConnectorSyncEventDataDoc>> outEaApps = new HashMap<>();
        QueryOaConnectorSyncEventDataArg arg = new QueryOaConnectorSyncEventDataArg();
        arg.setChannel(ChannelEnum.qywx);
//        arg.setAppId(repAppId);
        arg.setEventType(Lists.newArrayList("change_auth", "change_contact"));
        arg.setStatus(0);
        arg.setPageSize(ConfigCenter.EVENT_PAGE_SIZE);
        //分页循环，防止单次拉取数据太大导致oom
        do {
            arg.setPageNum(pageNum ++);
            List<OaConnectorSyncEventDataDoc> messageSaveDocs = oaConnectorSyncEventDataMongoDao.pageByQuerySyncEventDataArg(arg);
            if(CollectionUtils.isNotEmpty(messageSaveDocs)) {
                for (OaConnectorSyncEventDataDoc messageSaveDoc : messageSaveDocs) {
                    if (!outEaApps.containsKey(messageSaveDoc.getOutEa())) {
                        outEaApps.put(messageSaveDoc.getOutEa(), new ArrayList<>());
                    }
                    List<OaConnectorSyncEventDataDoc> syncEventDataDocs = outEaApps.get(messageSaveDoc.getOutEa());
                    syncEventDataDocs.add(messageSaveDoc);
                    outEaApps.put(messageSaveDoc.getOutEa(), syncEventDataDocs);
                }
            }
            size = messageSaveDocs.size();
        } while(size == ConfigCenter.EVENT_PAGE_SIZE);
        log.info("QyweixinAccountSyncServiceImpl.autoGetUserAndDepartmentInfo,outEaApps={}", outEaApps);
        if(outEaApps.isEmpty()) {
            return new Result<>();
        }

        // 2. 收集所有异步任务
        List<CompletableFuture<Void>> futures = new ArrayList<>();

        for (Map.Entry<String, List<OaConnectorSyncEventDataDoc>> entry : outEaApps.entrySet()) {
            String outEa = entry.getKey(); // 获取 key
            log.info("QyweixinAccountSyncServiceImpl.autoGetUserAndDepartmentInfo,outEa={}", outEa);
            Boolean aBoolean = oaNewBaseManager.canRunInNewBaseByOutEa(outEa);
            if(!aBoolean) {
                continue;
            }

            CompletableFuture<Void> future = CompletableFuture.runAsync(
                    () -> processOutEaEntry(outEa, entry.getValue()),
                    ThreadPoolHelper.saveOrUpdateUserAndDepartmentInfoThreadPool
            );

            // 异常处理（避免单个任务失败影响整体）
            future.exceptionally(e -> {
                log.error("QyweixinAccountSyncServiceImpl.autoGetUserAndDepartmentInfo,outEa={},error={}", outEa, e.getMessage());
                return null;
            });

            futures.add(future);
        }

        // 3. 阻塞等待所有任务完成
        try {
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).get();
            log.info("QyweixinAccountSyncServiceImpl.autoGetUserAndDepartmentInfo,all tasks completed successfully.");
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("QyweixinAccountSyncServiceImpl.autoGetUserAndDepartmentInfo,all tasks completed with an InterruptedException.", e);
        } catch (ExecutionException e) {
            log.error("QyweixinAccountSyncServiceImpl.autoGetUserAndDepartmentInfo,all tasks completed with an ExecutionException.", e);
        }

        return new Result<>();
    }

    private void processOutEaEntry(String outEa, List<OaConnectorSyncEventDataDoc> syncEventDataDocs) {
        log.info("QyweixinAccountSyncServiceImpl.autoGetUserAndDepartmentInfo,outEa={},syncEventDataDocs={}", outEa,syncEventDataDocs);
//        List<OaConnectorSyncEventDataDoc> syncEventDataDocs = entry.getValue(); // 获取 value
        // 将 syncEventDataDocs 转换为 String 类型的 ID 列表
        try {
            List<String> ids = syncEventDataDocs.stream()
                    .map(doc -> doc.getId().toString()) // 将 ObjectId 转换为 String
                    .collect(Collectors.toList());
            DeleteResult result = oaConnectorSyncEventDataMongoDao.deleteByIds(ids);
            log.info("QyweixinAccountSyncServiceImpl.autoGetUserAndDepartmentInfo,result={}", result);

            //按appId分类
            Map<String, List<OaConnectorSyncEventDataDoc>> groupedByAppId = new HashMap<>();
            for (OaConnectorSyncEventDataDoc doc : syncEventDataDocs) {
                String appId = doc.getAppId(); // 获取 appId
                groupedByAppId.computeIfAbsent(appId, k -> new ArrayList<>()).add(doc);
            }

            // 输出分组结果
            for (Map.Entry<String, List<OaConnectorSyncEventDataDoc>> group : groupedByAppId.entrySet()) {

                List<OaConnectorSyncEventDataDoc> docs = group.getValue();
                //存在change_auth，就不处理change_contact
                if (docs.stream().anyMatch(doc -> doc.getEventType().equals("change_auth"))) {
                    //过滤
                    docs = docs.stream()
                            .filter(doc -> "change_auth".equals(doc.getEventType()))
                            .limit(1) // 只取第一个
                            .collect(Collectors.toList());
                }
                //根据创建事件从小到大排序
                docs = docs.stream()
                        .sorted(Comparator.comparing(OaConnectorSyncEventDataDoc::getCreateTime))
                        .collect(Collectors.toList());

                for (OaConnectorSyncEventDataDoc doc : docs) {
                    log.info("QyweixinAccountSyncServiceImpl.autoGetUserAndDepartmentInfo,doc={}", doc);
                    try {
                        contactsService.syncOuterContacts(doc.getOutEa(), doc.getAppId(), doc.getEventType(), doc.getEvent());
                    } catch (Exception e) {
                        log.info("QyweixinAccountSyncServiceImpl.autoGetUserAndDepartmentInfo,doc={}", doc);
                    }
                }
            }
        } catch (Exception e) {
            log.error("QyweixinAccountSyncServiceImpl.autoGetUserAndDepartmentInfo,error={}", e.getMessage());
        }
    }

    @Override
    public Result<Void> updateUserAndDepartmentInfo(String outEa, String appId) {
        outerOaEmployeeDataManager.deleteInvisibleUsers(outEa, ChannelEnum.qywx, appId, new HashSet<>());
        outerOaDeptDataManager.deleteInvisibleDepts(outEa, ChannelEnum.qywx, appId, new HashSet<>());
        saveOrUpdateUserAndDepartmentInfo(outEa, appId);
        return new Result<>();
    }

    private void saveOrUpdateUserAndDepartmentInfo(String outEa, String appId) {
        String traceId = UUID.randomUUID().toString();
        TraceUtil.initTrace("saveOuterInfo_" + outEa + "_" + traceId);
        log.info("QyweixinAccountSyncServiceImpl.saveOrUpdateUserAndDepartmentInfo,outEa={}", outEa);
        Gson gson = new Gson();
        //获取部门详情
        com.facishare.open.qywx.accountinner.result.Result<QyweixinDepartmentListRsp> departmentInfoListResult = qyWeixinManager.getDepartmentInfoList(appId, outEa, null);
        if(departmentInfoListResult.isSuccess()) {
            //保存入库
            List<OuterOaDeptDataEntity> deptDataEntities = new LinkedList<>();
            if(ObjectUtils.isNotEmpty(departmentInfoListResult.getData()) && CollectionUtils.isNotEmpty(departmentInfoListResult.getData().getDepartment())) {
                for(QyweixinDepartmentRsp rsp : departmentInfoListResult.getData().getDepartment()) {
                    OuterOaDeptDataEntity deptDataEntity = new OuterOaDeptDataEntity();
                    deptDataEntity.setId(IdGenerator.get());
                    deptDataEntity.setChannel(ChannelEnum.qywx);
                    deptDataEntity.setOutEa(outEa);
                    deptDataEntity.setAppId(appId);
                    deptDataEntity.setOutDeptId(rsp.getId());
                    deptDataEntity.setDeptName(rsp.getName());
                    deptDataEntity.setDeptOrder(Long.valueOf(rsp.getOrder()));
                    deptDataEntity.setParentDeptId(rsp.getParentid());
                    deptDataEntity.setOutDeptInfo((JSONObject) JSONObject.toJSON(rsp));
                    deptDataEntity.setCreateTime(System.currentTimeMillis());
                    deptDataEntity.setUpdateTime(System.currentTimeMillis());
                    deptDataEntities.add(deptDataEntity);
                }
                Integer updateCount = outerOaDeptDataManager.batchUpdateOutDeptId(deptDataEntities);
                log.info("QyweixinAccountSyncServiceImpl.saveOrUpdateUserAndDepartmentInfo,department,updateCount={}", updateCount);
            }
            //删除获取不到的部门详情文档
            Set<String> outDeptList = deptDataEntities.stream().map(OuterOaDeptDataEntity::getOutDeptId).collect(Collectors.toSet());
            Integer deleteCount = outerOaDeptDataManager.deleteInvisibleDepts(outEa, ChannelEnum.qywx, appId, outDeptList);
            log.info("QyweixinAccountSyncServiceImpl.saveOrUpdateUserAndDepartmentInfo,department,deleteCount={}", deleteCount);
        }

        //获取人员详情，查询范围默认为1
        Result<List<QyweixinUserDetailInfoRsp>> allEmployeeLisResult = contactBindInnerService.getAllEmployeeList(outEa, appId, "1");
        if(allEmployeeLisResult.isSuccess()) {
            //保存入库
            List<OuterOaEmployeeDataEntity> employeeDataEntities = new LinkedList<>();
            if(CollectionUtils.isNotEmpty(allEmployeeLisResult.getData())) {
                for(QyweixinUserDetailInfoRsp rsp : allEmployeeLisResult.getData()) {
                    OuterOaEmployeeDataEntity employeeDataEntity = new OuterOaEmployeeDataEntity();
                    employeeDataEntity.setId(IdGenerator.get());
                    employeeDataEntity.setChannel(ChannelEnum.qywx);
                    employeeDataEntity.setOutEa(outEa);
                    employeeDataEntity.setAppId(appId);
                    employeeDataEntity.setOutUserId(rsp.getUserid());
                    employeeDataEntity.setOutUserInfo((JSONObject) JSONObject.toJSON(rsp));
                    employeeDataEntity.setOutDeptId(rsp.getMain_department());
                    employeeDataEntity.setCreateTime(System.currentTimeMillis());
                    employeeDataEntity.setUpdateTime(System.currentTimeMillis());
                    employeeDataEntities.add(employeeDataEntity);
                }
                Integer updateCount = outerOaEmployeeDataManager.batchUpdateOutUserId(employeeDataEntities);
                log.info("QyweixinAccountSyncServiceImpl.saveOrUpdateUserAndDepartmentInfo,user,updateCount={}", updateCount);
            }
            //删除获取不到的人员详情文档
            Set<String> outEmpList = employeeDataEntities.stream().map(OuterOaEmployeeDataEntity::getOutUserId).collect(Collectors.toSet());
            Integer deleteCount = outerOaEmployeeDataManager.deleteInvisibleUsers(outEa, ChannelEnum.qywx, appId, outEmpList);
            log.info("QyweixinAccountSyncServiceImpl.saveOrUpdateUserAndDepartmentInfo,user,deleteCount={}", deleteCount);

            //自动绑定逻辑
            autoBindEmpAccounts(outEa, appId, allEmployeeLisResult.getData());
        }
    }

    private void autoBindEmpAccounts(String outEa, String appId, List<QyweixinUserDetailInfoRsp> userList) {
        //查询企业绑定
        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntitiesByOutEa(ChannelEnum.qywx, outEa, appId);
        if(CollectionUtils.isEmpty(enterpriseBindEntities)) {
            return;
        }
        for (OuterOaEnterpriseBindEntity enterpriseBindEntity : enterpriseBindEntities) {
            String fsEa = enterpriseBindEntity.getFsEa();
            Map<String, Map<String, String>> autoBindEmpEnterpriseMap = new Gson().fromJson(ConfigCenter.EXTATTR_AUTO_BIND_EMP_ENTERPRISE, new TypeToken<Map<String, Map<String, String>>>() {
            }.getType());
            if(autoBindEmpEnterpriseMap.containsKey(fsEa)) {
                corpManager.autoBindEmpAccount(enterpriseBindEntity, userList);
            } else if(ConfigCenter.AUTO_BIND_ACCOUNT_EA.contains(fsEa)) {
                corpManager.autoBindAccountEnterprise2(enterpriseBindEntity, userList);
            }
        }
    }

    @Override
    public Result<Boolean> checkEnterpriseProductVersion(QyweixinEnterpriseLicenseModel enterpriseLicenseModel) {
        if(StringUtils.isAnyEmpty(enterpriseLicenseModel.getFsEa(), enterpriseLicenseModel.getCheckType())) {
            return Result.newInstance(ErrorRefer.PARAM_ERROR);
        }
        String fsEa = enterpriseLicenseModel.getFsEa();
        String outEa = enterpriseLicenseModel.getOutEa();
        String appId = enterpriseLicenseModel.getAppId();
        String checkType = enterpriseLicenseModel.getCheckType();
        boolean flag = Boolean.FALSE;

        //查询是否存在绑定关系

        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntities(OuterOaEnterpriseBindParams.builder().channel(ChannelEnum.qywx).fsEa(fsEa).outEa(outEa).appId(appId).build());

        if(CollectionUtils.isEmpty(enterpriseBindEntities)) {
            return Result.newInstance(ErrorRefer.OUT_EA_NOT_BIND);
        }

        //检查企业当前checkType的license版本
        Map<String, List<String>> checkEnterpriseLicenseMap = new Gson().fromJson(ConfigCenter.CHECK_ENTERPRISE_LICENSE, new TypeToken<Map<String, List<String>>>() {
        }.getType());
        log.info("QyweixinAccountSyncServiceImpl.checkEnterpriseProductVersion,checkEnterpriseLicenseMap={}",checkEnterpriseLicenseMap);
        if(ObjectUtils.isEmpty(checkEnterpriseLicenseMap)) {
            return Result.newInstance(ErrorRefer.OUT_EA_NOT_BIND);
        }

        if(checkEnterpriseLicenseMap.containsKey(checkType)) {
            //只需要符合一个就行
            List<String> licenseList = checkEnterpriseLicenseMap.get(checkType);
            LicenseContext context = new LicenseContext();
            context.setAppId("CRM");
            context.setTenantId(String.valueOf(eieaConverter.enterpriseAccountToId(fsEa)));
            context.setUserId("-10000");

            QueryProductArg queryProductArg = new QueryProductArg();
            queryProductArg.setLicenseContext(context);
            LicenseVersionResult result = licenseClient.queryProductVersion(queryProductArg);
            if(ObjectUtils.isNotEmpty(result) && CollectionUtils.isNotEmpty(result.getResult())) {
                List<ProductVersionPojo> pojoList = result.getResult();
                for(ProductVersionPojo pojo : pojoList) {
                    if(licenseList.contains(pojo.getCurrentVersion())) {
                        flag = Boolean.TRUE;
                        break;
                    }
                }
            }
        }
        return new Result<>(flag);
    }

    @Override
    public Result<Void> createEmployeeInfo(String outEa, String appId, String outUserId, String fsEa) {
        //检查是否绑定了
        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntities(OuterOaEnterpriseBindParams.builder().channel(ChannelEnum.qywx).fsEa(fsEa).outEa(outEa).appId(appId).build());

        if(CollectionUtils.isEmpty(enterpriseBindEntities)) {
            return Result.newInstance(ErrorRefer.OUT_EA_NOT_BIND);
        }

        List<OuterOaEmployeeBindEntity> employeeBindEntities = outerOaEmployeeBindManager.getEntitiesByNotPage(OuterOaEmployeeBindParams.builder().channel(ChannelEnum.qywx).fsEa(fsEa).outEa(outEa).appId(appId).outEmpId(outUserId).build());

        if(CollectionUtils.isNotEmpty(employeeBindEntities)) {
            return new Result<>();
        }

        //创建员工
        return contactsService.addUser(enterpriseBindEntities.get(0), outUserId);
    }

    @Override
    public Result<Boolean> isQywxDepExist(String appId, String corpId, String departmentId) {
        com.facishare.open.qywx.accountinner.result.Result<QyweixinDepartmentInfoRsp> result = qyWeixinManager.getDepartmentInfo(appId,
                corpId,
                departmentId);
        return new Result<>(result.getData()!=null);
    }

    @Override
    public Department getFsDepartment(String ea, Integer departmentId) {
        return fsManager.getDepartment(ea, departmentId);
    }


    @Override
    public Result<Void> batchInsertBusinessAppInfo(List<QyweixinBusinessBindInfo> qyweixinBusinessBindInfos) {
        if(CollectionUtils.isEmpty(qyweixinBusinessBindInfos)){
            return Result.newInstance(ErrorRefer.PARAM_ERROR);
        }
        if(CollectionUtils.size(qyweixinBusinessBindInfos)>100){
            return Result.newInstance(ErrorRefer.PARAM_ERROR);
        }
        List<QyweixinBusinessInfoBindEntity> qyweixinBusinessInfoBindEntities = Lists.newArrayList();
        for (QyweixinBusinessBindInfo qyweixinBusinessBindInfo : qyweixinBusinessBindInfos) {
            if(StringUtils.isAnyBlank(qyweixinBusinessBindInfo.getAppId(),qyweixinBusinessBindInfo.getOutEa(),qyweixinBusinessBindInfo.getFsEa())){
                return Result.newInstance(ErrorRefer.PARAM_ERROR);
            }
            QyweixinBusinessInfoBindEntity qyweixinBusinessInfoBindBo=new QyweixinBusinessInfoBindEntity();
            BeanUtils.copyProperties(qyweixinBusinessBindInfo,qyweixinBusinessInfoBindBo);
            qyweixinBusinessInfoBindBo.setId(IdGenerator.get());
            qyweixinBusinessInfoBindBo.setUpdateTime(System.currentTimeMillis());
            qyweixinBusinessInfoBindBo.setCreateTime(System.currentTimeMillis());

            qyweixinBusinessInfoBindEntities.add(qyweixinBusinessInfoBindBo);
        }
        int insertAccount = qyweixinBusinessInfoBindManager.batchUpsertInfos(qyweixinBusinessInfoBindEntities);
        return new Result<>();
    }

    @Override
    public Result<QueryMessageIdResult> queryQywxSessionMessage(QywxQueryMessageArgByEa qywxQueryMessageArgByEa) {
        //只能有一个会话留存，目前的设计
        String fsEa= qywxQueryMessageArgByEa.getFsEa();
        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntities(OuterOaEnterpriseBindParams.builder().channel(ChannelEnum.qywx).fsEa(fsEa).bindStatus(BindStatusEnum.normal).build());

        if(CollectionUtils.isEmpty(enterpriseBindEntities)) {
            return Result.newInstance(ErrorRefer.OUT_EA_NOT_BIND);
        }
        OuterOaEnterpriseBindEntity enterpriseBindEntity = null;
        for (OuterOaEnterpriseBindEntity oaEnterpriseBindEntity : enterpriseBindEntities) {
            OuterOaConfigInfoEntity configInfoEntity = outerOaConfigInfoManager.getEntityByDataCenterId(OuterOaConfigInfoTypeEnum.CONVERSATION_ARCHIVE_CONFIG, oaEnterpriseBindEntity.getId());
            if (ObjectUtils.isEmpty(configInfoEntity)){
                continue;
            }

            ConversationArchiveInfo conversationArchiveInfo = JSON.parseObject(configInfoEntity.getConfigInfo(), ConversationArchiveInfo.class);
            if (conversationArchiveInfo.getIsOpen()) {
                enterpriseBindEntity = oaEnterpriseBindEntity;
                break;
            }
        }

        if (ObjectUtils.isEmpty(enterpriseBindEntity)){
            return new Result<>();
        }

        String corpId = enterpriseBindEntity.getOutEa();
        String appId = enterpriseBindEntity.getAppId();

        String finalAppId = appId;
        //查看是否有repAppId
        if (finalAppId.equals(ConfigCenter.crmAppId)) {
            OuterOaAppInfoEntity appInfoEntity = outerOaAppInfoManager.getEntity(ChannelEnum.qywx, corpId, ConfigCenter.repAppId);
            if (ObjectUtils.isNotEmpty(appInfoEntity) && appInfoEntity.getStatus() == OuterOaAppInfoStatusEnum.normal) {
                log.info("QyweixinGatewayInnerServiceImpl.getGroupChatDetail2,appInfoEntity={}", appInfoEntity);
                finalAppId = ConfigCenter.repAppId;
            }
        }

        QywxQueryMessageArg qywxQueryMessageArg=new QywxQueryMessageArg();
        BeanUtils.copyProperties(qywxQueryMessageArgByEa,qywxQueryMessageArg);
        com.facishare.open.qywx.save.result.Result<QueryMessageIdResult> queryMessageIdResultResult = autoPullMessageService.conditionQueryMessageData(corpId, finalAppId, qywxQueryMessageArg);
        if(queryMessageIdResultResult.isSuccess()){
            return new Result<>(queryMessageIdResultResult.getData());
        } else {
            return new Result(queryMessageIdResultResult.getErrorCode().toString(),queryMessageIdResultResult.getErrorMsg());
        }

//        if(enterpriseMappingResult.isSuccess()&&ObjectUtils.isNotEmpty(enterpriseMappingResult.getData())){
//            String corpId=enterpriseMappingResult.getData().getOutEa();
//            QywxQueryMessageArg qywxQueryMessageArg=new QywxQueryMessageArg();
//            BeanUtils.copyProperties(qywxQueryMessageArgByEa,qywxQueryMessageArg);
//            com.facishare.open.qywx.save.result.Result<QueryMessageIdResult> queryMessageIdResultResult = autoPullMessageService.conditionQueryMessageData(corpId, ConfigCenter.repAppId, qywxQueryMessageArg);
//            if(queryMessageIdResultResult.isSuccess()){
//                return new Result<>(queryMessageIdResultResult.getData());
//            } else {
//                return new Result(queryMessageIdResultResult.getErrorCode().toString(),queryMessageIdResultResult.getErrorMsg());
//            }
//        }
//        return new Result<>();
    }

    @Override
    public Result<IntelligentAppInfoResult> queryIntelligentSessionInfo(String fsEa, String appId) {
        IntelligentAppInfoResult intelligentAppInfoResult = corpManager.queryIntelligentBind(fsEa, appId);
        if(ObjectUtils.isEmpty(intelligentAppInfoResult)){
            return Result.newInstance(ErrorRefer.REP_APP_NOT_ENABLE);
        }
        return new Result<>(intelligentAppInfoResult);
    }

    @Override
    public Result<Void> triggerActiveCodes(String fsEa, String activeCode,String userId) {
        if(StringUtils.isAnyBlank(userId,activeCode)){
            return Result.newInstance(ErrorRefer.PARAM_ERROR);
        }

        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntities(OuterOaEnterpriseBindParams.builder().channel(ChannelEnum.qywx).fsEa(fsEa).bindStatus(BindStatusEnum.normal).build());

        if(CollectionUtils.isEmpty(enterpriseBindEntities)) {
            return Result.newInstance(ErrorRefer.OUT_EA_NOT_BIND);
        }
        StringBuilder errorMsg = new StringBuilder();
        //只要有一个成功就成功
        boolean isSuccess = false;
        for (OuterOaEnterpriseBindEntity oaEnterpriseBindEntity : enterpriseBindEntities) {
            String corpId=oaEnterpriseBindEntity.getOutEa();
            QywxBaseResult qywxBaseResult = qyWeixinManager.triggerActiveCodeTOUserId(corpId, activeCode, userId);
            log.info("triggerActiveCodes result:{}",qywxBaseResult);
            if(!qywxBaseResult.isSuccess()){
                errorMsg.append(corpId).append(":").append(qywxBaseResult.getErrmsg()).append(";");
            } else {
                isSuccess = true;
            }
        }
        if(StringUtils.isNotBlank(errorMsg) && !isSuccess){
            return new Result(ErrorRefer.INTERNAL_ERROR.getCode(),errorMsg.toString());
        }
        return new Result<>();
    }

    @Override
    public Result<Void> transferUserIds(String fsEa,List<QywxActviceCodeArg.QywxTransferUserItem> transferList) {
        if(CollectionUtils.isNotEmpty(transferList)){
            return Result.newInstance(ErrorRefer.PARAM_ERROR);
        }
        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntities(OuterOaEnterpriseBindParams.builder().channel(ChannelEnum.qywx).fsEa(fsEa).bindStatus(BindStatusEnum.normal).build());

        if(CollectionUtils.isEmpty(enterpriseBindEntities)) {
            return Result.newInstance(ErrorRefer.OUT_EA_NOT_BIND);
        }
        StringBuilder errorMsg = new StringBuilder();
        //只要有一个成功就成功
        boolean isSuccess = false;
        for (OuterOaEnterpriseBindEntity oaEnterpriseBindEntity : enterpriseBindEntities) {
            String corpId = oaEnterpriseBindEntity.getOutEa();
            QywxTransferResult qywxTransferResult = qyWeixinManager.transferUserIds(corpId, transferList);
            if(!qywxTransferResult.isSuccess()){
                errorMsg.append(corpId).append(":").append(qywxTransferResult.getErrmsg()).append(";");
            } else {
                isSuccess = true;
            }
        }
        if(StringUtils.isNotBlank(errorMsg) && !isSuccess){
            return new Result(ErrorRefer.INTERNAL_ERROR.getCode(),errorMsg.toString());
        }

        return new Result<>();
    }
}
