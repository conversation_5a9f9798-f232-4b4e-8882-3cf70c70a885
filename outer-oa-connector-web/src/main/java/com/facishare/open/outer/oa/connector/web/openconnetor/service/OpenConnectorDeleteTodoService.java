package com.facishare.open.outer.oa.connector.web.openconnetor.service;


import com.facishare.open.erpdss.outer.oa.connector.base.context.TemplateResult;
import com.facishare.open.outer.oa.connector.common.api.result.Result;
import com.facishare.open.outer.oa.connector.web.openconnetor.model.OuterMsgModel;
import com.fxiaoke.message.extrnal.platform.model.arg.CreateTodoArg;
import com.fxiaoke.message.extrnal.platform.model.arg.DeleteTodoArg;

public interface OpenConnectorDeleteTodoService {
    /**
     * 过滤消息
     * @param context
     */
    Result<Void> filterDeleteTodoMsg(String tenantId, String dataCenterId, String connectorApiName, DeleteTodoArg context);

    /**
     * 组装消息
     * @param context
     */
    Result<OuterMsgModel> buildDeleteTodoMsg(String tenantId, String dataCenterId, String connectorApiName,DeleteTodoArg context);

    /**
     * 发送消息
     * @param context
     */
    Result<Void> sendDeleteTodoMsg(String tenantId, String dataCenterId, String connectorApiName,OuterMsgModel context);
}
