package com.facishare.open.ding.web.controller;

import base.BaseAbstractTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockHttpServletResponse;

import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2025/4/3 12:00:29
 */
public class CallBackControllerTest extends BaseAbstractTest {

    @Autowired
    private CallBackController callBackController;

    @Test
    public void authorizeByApp() throws IOException {
        callBackController.authorizeByApp("910c380c9f8c378aa206b38513c11120", "ding328b43b93b4861db4ac5d6980864d335", null, "Mozilla/5.0 (Macintosh; Intel Mac OS X 1\n" +
                "0_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3.1 Safari/605.1.15 DingTalk(7.6.49-macOS-arm64-43986354) nw DTWKWebView Channel/201200 Architecture/arm64", new MockHttpServletResponse());
    }
}