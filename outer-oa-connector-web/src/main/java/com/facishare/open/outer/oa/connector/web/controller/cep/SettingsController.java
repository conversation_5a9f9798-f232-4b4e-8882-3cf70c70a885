package com.facishare.open.outer.oa.connector.web.controller.cep;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.facishare.converter.EIEAConverter;
import com.facishare.open.oa.base.dbproxy.manager.DescManager;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaConfigInfoEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaConfigInfoManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEmployeeBindManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEnterpriseBindManager;
import com.facishare.open.outer.oa.connector.common.api.admin.BaseConnectorVo;
import com.facishare.open.outer.oa.connector.common.api.enums.*;
import com.facishare.open.outer.oa.connector.common.api.info.SettingAccountRulesModel;
import com.facishare.open.outer.oa.connector.common.api.info.SystemParams;
import com.facishare.open.outer.oa.connector.common.api.outerInterface.OuterAbstractSettingService;
import com.facishare.open.outer.oa.connector.common.api.params.CepArg;
import com.facishare.open.outer.oa.connector.common.api.result.*;
import com.facishare.open.outer.oa.connector.i18n.I18NStringManager;
import com.facishare.open.outer.oa.connector.web.manager.OuterOASettingFactory;
import com.facishare.open.outer.oa.connector.web.manager.RedisManager;
import com.facishare.open.outer.oa.connector.web.model.GetConnectorIntroArg;
import com.facishare.open.outer.oa.connector.web.model.OaConnectorAuthType;
import com.facishare.open.outer.oa.connector.web.model.OaSystemParamsArg;
import com.facishare.open.outer.oa.connector.web.model.SelectOption;
import com.facishare.open.outer.oa.connector.web.openconnetor.OpenConnectorManager;
import com.facishare.open.outer.oa.connector.web.openconnetor.model.ConnectorIntro;
import com.facishare.open.outer.oa.connector.web.util.AsyncHelper;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;
import java.util.Comparator;
import java.util.TreeMap;
import java.util.concurrent.ExecutorService;

/**
 * 规则设置控制器
 */
@Slf4j
@RestController
@RequestMapping("/oaBase/settings")
public class SettingsController extends BaseController {

    @Autowired
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;

    @Autowired
    private DescManager descManager;
    @Autowired
    private OuterOaConfigInfoManager outerOaConfigInfoManager;
    @Autowired
    private OpenConnectorManager openConnectorManager;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private OuterOASettingFactory outerOASettingFactory;
    @Autowired
    private RedisManager redisManager;
    @Autowired
    private ExecutorService asyncExecutor;
    @Autowired
    private OuterOaEmployeeBindManager outerOaEmployeeBindManager;

    /**
     * 返回设置规则
     *
     * @param arg
     * @return
     */
    @RequestMapping(value = "/getSettingRules", method = RequestMethod.POST)
    public Result<SettingsResult> getSettingRules(@RequestBody CepArg arg) {
        OuterOaEnterpriseBindEntity enterpriseBindEntity = outerOaEnterpriseBindManager
                .getEntityById(arg.getCurrentDcId());
        if (ObjectUtils.isEmpty(enterpriseBindEntity)) {
            return Result.newError(ResultCodeEnum.BIND_ENTERPRISE_ERROR);
        }
        SettingsResult settingsResult = new SettingsResult();
        OuterOaConfigInfoEntity entityByDataCenterId = outerOaConfigInfoManager
                .getEntityByDataCenterId(OuterOaConfigInfoTypeEnum.SETTING_BIND_RULES, arg.getCurrentDcId());

        if (ObjectUtils.isNotEmpty(entityByDataCenterId)) {
            SettingAccountRulesModel settingAccountRulesModel = JSONObject
                    .parseObject(entityByDataCenterId.getConfigInfo(), new TypeReference<SettingAccountRulesModel>() {
                    });
            BeanUtils.copyProperties(settingAccountRulesModel, settingsResult);
            // 需要查询对应的字段
            Result<SystemFieldMappingResult> orInitFieldMapping = descManager
                    .getOrInitFieldMapping(enterpriseBindEntity, OuterOaConfigInfoTypeEnum.EMPLOYEE_UNIQUE_IDENTITY);
            if (settingAccountRulesModel.getSyncTypeEnum() == EnterpriseConfigAccountSyncTypeEnum.accountBind) {
                for (SystemFieldMappingResult.ItemFieldMapping itemFieldMapping : orInitFieldMapping.getData()
                        .getItemFieldMappings()) {
                    if (Boolean.valueOf(itemFieldMapping.getMatchUnique())) {
                        settingsResult.setEmployeeBindRule(itemFieldMapping);
                    }
                }
            }
            // 自动绑定或者自动同步
            if (settingAccountRulesModel.getBindTypeEnum() == BindTypeEnum.auto
                    || settingAccountRulesModel.getSyncTypeEnum() == EnterpriseConfigAccountSyncTypeEnum.accountSync) {
                List<SystemFieldMappingResult.ItemFieldMapping> employeeFieldMappings = Lists.newArrayList();
                if (orInitFieldMapping.isSuccess() && ObjectUtils.isNotEmpty(orInitFieldMapping.getData())) {
                    for (SystemFieldMappingResult.ItemFieldMapping itemFieldMapping : orInitFieldMapping.getData()
                            .getItemFieldMappings()) {
                        SystemFieldMappingResult.ItemFieldMapping employeeFieldMapping = new SystemFieldMappingResult.ItemFieldMapping();
                        employeeFieldMapping.setCrmFieldLabel(itemFieldMapping.getCrmFieldLabel());
                        employeeFieldMapping.setCrmFieldApiName(itemFieldMapping.getCrmFieldApiName());
                        employeeFieldMapping.setOuterOAFieldLabel(itemFieldMapping.getOuterOAFieldLabel());
                        employeeFieldMapping.setOuterOAFieldApiName(itemFieldMapping.getOuterOAFieldApiName());
                        employeeFieldMapping.setFieldType(itemFieldMapping.getFieldType());
                        employeeFieldMappings.add(employeeFieldMapping);
                    }
                    // TODO 部门映射
                }

                settingsResult.setEmployeeFieldMapping(employeeFieldMappings);

            }
        }
        return Result.newSuccess(settingsResult);
    }

    /**
     * 保存绑定规则
     *
     * @param settingsResult
     * @return
     */
    @RequestMapping(value = "/saveSettingRules", method = RequestMethod.POST)
    public Result<Void> saveSettingRules(@RequestBody SettingsResult settingsResult) {
        OuterOaEnterpriseBindEntity outerOaEnterpriseBindEntity = outerOaEnterpriseBindManager
                .getEntityById(settingsResult.getCurrentDcId());
        if (ObjectUtils.isEmpty(outerOaEnterpriseBindEntity)) {
            return Result.newError(ResultCodeEnum.FS_EA_NOT_BIND);
        }
        //避免数据还在后台重置，又重新保存规则触发更新用户数据
        if (!redisManager.validRefreshUserDataLock(settingsResult.getCurrentDcId())) {
            return Result.newError(ResultCodeEnum.WAIT_FEW_MINUTES_SAVE);
        }
        OuterOaConfigInfoEntity settingRules = outerOaConfigInfoManager
                .getEntityByDataCenterId(OuterOaConfigInfoTypeEnum.SETTING_BIND_RULES, settingsResult.getCurrentDcId());
        SettingAccountRulesModel dbRules = JSONObject.parseObject(settingRules.getConfigInfo(), SettingAccountRulesModel.class);
        //需要看下，数据库是不是手动绑定的，手动绑定不允许切换到自动同步
        if (dbRules.getSyncTypeEnum().equals(EnterpriseConfigAccountSyncTypeEnum.accountBind)) {
            if(settingsResult.getSyncTypeEnum().equals(EnterpriseConfigAccountSyncTypeEnum.accountSync)){
                //需要判断人员表是不是空的，不为空就不允许切换
                Integer employeeCount = outerOaEmployeeBindManager.countCrmUserIds(settingsResult.getCurrentDcId());
                if (employeeCount > 0) {
                    log.info("employee count exists bind:{}:{}", outerOaEnterpriseBindEntity.getFsEa(), employeeCount);
                    return Result.newError(ResultCodeEnum.NOT_SUPPORT_SWITCH_AUTH_RULES);
                }
            }
        }

        // 需要比对保存的字段映射
        OuterOaConfigInfoEntity employeeFieldConfig = outerOaConfigInfoManager.getEntityByDataCenterId(
                OuterOaConfigInfoTypeEnum.EMPLOYEE_UNIQUE_IDENTITY, settingsResult.getCurrentDcId());
        SystemFieldMappingResult systemFieldMappingResult = JSONObject.parseObject(employeeFieldConfig.getConfigInfo(),
                SystemFieldMappingResult.class);
        SystemFieldMappingResult.ItemFieldMapping employeeBindRule = settingsResult.getEmployeeBindRule();
        // 比对保存规则
        saveSettings(settingsResult, settingRules);
        // 需要比对一下与数据库的是不是一样：
        if (employeeBindRule != null) {
            // 账号绑定规则
            OuterOaConfigInfoEntity entityByAutoFieldDeFault = outerOaConfigInfoManager.initDefault(
                    OuterOaConfigInfoTypeEnum.EMPLOYEE_UNIQUE_IDENTITY, settingsResult.getCurrentDcId());
            SystemFieldMappingResult defaultFieldMappings = JSONObject
                    .parseObject(entityByAutoFieldDeFault.getConfigInfo(), SystemFieldMappingResult.class);
            // 增加前端传递的数据，fs_crm fieldName一样的会被覆盖。
            defaultFieldMappings.getItemFieldMappings().add(employeeBindRule);
            log.info("upsert employy bind rules:{}", defaultFieldMappings);
            // 用TreeMap替换普通Map，并按照name排序
            Map<String, SystemFieldMappingResult.ItemFieldMapping> itemFieldMappingMap = defaultFieldMappings
                    .getItemFieldMappings().stream().collect(Collectors.toMap(item -> item.getCrmFieldApiName(),
                            item -> item, (v1, v2) -> v2, () -> new TreeMap<>(Comparator.comparing(String::toString))));
            for (SystemFieldMappingResult.ItemFieldMapping value : itemFieldMappingMap.values()) {
                if (value.getCrmFieldApiName().equals(employeeBindRule.getCrmFieldApiName())) {
                    value.setMatchUnique(true);
                } else {
                    value.setMatchUnique(false);
                }
                settingsResult.setEmployeeFieldMapping(new ArrayList<>(itemFieldMappingMap.values()));
            }
        }
        // 这里需要排序，避免Text错乱settingsResult employyFileMapping
        // settingsResult.getEmployeeFieldMapping().sort(Comparator.comparing(SystemFieldMappingResult.ItemFieldMapping::getCrmFieldApiName));
        Boolean validCrmName = validUserName(settingsResult, outerOaEnterpriseBindEntity.getChannel());
        if (!validCrmName) {
            return Result.newError(ResultCodeEnum.NEED_OUTER_OA_EMP_NAME);
        }
        // 需要根据里面的字段进行保存
        List<SystemFieldMappingResult.ItemFieldMapping> updateFieldMapping = Lists.newArrayList();
        if (ObjectUtils.isNotEmpty(settingsResult.getEmployeeFieldMapping())) {
            for (int i = 0; i < settingsResult.getEmployeeFieldMapping().size(); i++) {
                int index = i + 1;
                SystemFieldMappingResult.ItemFieldMapping indexFieldMappings = settingsResult.getEmployeeFieldMapping()
                        .get(i);
                if (index <= 4) {
                    String queryText = new StringBuilder().append("text").append(index).toString();
                    indexFieldMappings.setOuterDataText(queryText);
                }
                updateFieldMapping.add(indexFieldMappings);
            }
            //手动->自动，需要重新拉取数据,绑定的字段有更新，也需要重新拉取数据
            if (!assertFields(updateFieldMapping, systemFieldMappingResult.getItemFieldMappings()) || dbRules.getBindTypeEnum().equals(BindTypeEnum.manual) && settingsResult.getBindTypeEnum().equals(BindTypeEnum.auto)) {
                // 更新
//            if(true){
                log.info("update unique identity not equals:{}", settingsResult);
                systemFieldMappingResult.setItemFieldMappings(updateFieldMapping);
                employeeFieldConfig.setConfigInfo(JSONObject.toJSONString(systemFieldMappingResult));
                // OuterOASettingInterface 执行对应的逻辑。
                BaseConnectorVo connectorVo = JSONObject.parseObject(outerOaEnterpriseBindEntity.getConnectInfo(),
                        BaseConnectorVo.class);
                // 改字段配置了需要重新刷out_emp_data数据
                log.info("upset dat a bind setting");
                // 保存映射的字段
                outerOaConfigInfoManager.batchUpsertByDcIdAndType(Lists.newArrayList(employeeFieldConfig));
                //使用redis存储，避免下次打开页面搜索的时候，由于列名更改，前端展示错乱。
                OuterAbstractSettingService implementation = outerOASettingFactory
                        .getImplementation(outerOaEnterpriseBindEntity.getChannel(), connectorVo.getAppType());
                AsyncHelper.executeAsync(
                        () -> redisManager.refreshUserData(outerOaEnterpriseBindEntity.getId(),
                                employeeFieldConfig.getChannel(), implementation), 10, ResultCodeEnum.ASYNC_ALERT);
            }
        }


        return Result.newSuccess();
    }

    private void saveSettings(SettingsResult settingsResult, OuterOaConfigInfoEntity settingRules) {

        if (ObjectUtils.isNotEmpty(settingRules)) {
            SettingAccountRulesModel settingAccountRulesModel = JSONObject.parseObject(
                    JSONObject.toJSONString(settingsResult), new TypeReference<SettingAccountRulesModel>() {
                    });
            //这里注意；手动切换到自动绑定的时候,前端类型不同，有些数据没有改
            if (settingAccountRulesModel.getSyncTypeEnum() == EnterpriseConfigAccountSyncTypeEnum.accountSync) {
                //需要转换绑定类型
                settingAccountRulesModel.setBindTypeEnum(BindTypeEnum.auto);
            }

            settingRules.setConfigInfo(JSONObject.toJSONString(settingAccountRulesModel));
            outerOaConfigInfoManager.batchUpsertByDcIdAndType(Lists.newArrayList(settingRules));
        }
    }


    private boolean assertFields(List<SystemFieldMappingResult.ItemFieldMapping> updateFieldMapping,
                                 List<SystemFieldMappingResult.ItemFieldMapping> dbFieldMappings) {
        // 如果任一列表为空，返回 false
        if (CollUtil.isEmpty(updateFieldMapping) || CollUtil.isEmpty(dbFieldMappings)) {
            return false;
        }
        // 将字段映射转换为 JSON 字符串，确保字段顺序一致
        String updateJson = JSONObject.toJSONString(updateFieldMapping);
        String dbJson = JSONObject.toJSONString(dbFieldMappings);
        // 使用 Hutool 的 MD5 工具计算 MD5 值
        String updateMd5 = SecureUtil.md5(updateJson);
        String dbMd5 = SecureUtil.md5(dbJson);
        // 比较 MD5 值
        return StrUtil.equals(updateMd5, dbMd5);
    }

    /**
     * 查询系统字段
     *
     * @param cepArg
     * @return
     */
    @RequestMapping(value = "/queryOuterSystemField", method = RequestMethod.POST)
    public Result<Set<SystemFieldResult>> queryOuterSystemField(@RequestBody CepArg cepArg) {
        OuterOaEnterpriseBindEntity entityById = outerOaEnterpriseBindManager.getEntityById(cepArg.getCurrentDcId());
        if (entityById == null) {
            return Result.newError(ResultCodeEnum.BIND_ENTERPRISE_ERROR);
        }
        Result<Set<SystemFieldResult>> outerSystemField = descManager.getOuterSystemField(entityById,
                entityById.getChannel());
        return outerSystemField;
    }

    /**
     * 保存外部系统字段
     *
     * @param systemField
     * @return
     */
    @RequestMapping(value = "/saveOuterSystemField", method = RequestMethod.POST)
    public Result<Integer> saveOuterSystemField(@RequestBody SystemFieldResult systemField) {
        OuterOaEnterpriseBindEntity entityById = outerOaEnterpriseBindManager
                .getEntityById(systemField.getCurrentDcId());
        if (ObjectUtils.isEmpty(entityById)) {
            return Result.newError(ResultCodeEnum.BIND_ENTERPRISE_ERROR);
        }
        OuterOaConfigInfoEntity configInfoEntity = outerOaConfigInfoManager.getEntityByDataCenterId(
                OuterOaConfigInfoTypeEnum.OUTER_SYSTEM_OBJECT_FIELDS, systemField.getCurrentDcId());
        if (ObjectUtils.isNotEmpty(configInfoEntity)) {
            Set<SystemFieldResult> data = JSONObject.parseObject(configInfoEntity.getConfigInfo(),
                    new TypeReference<Set<SystemFieldResult>>() {
                    });
            SystemFieldResult itemField = new SystemFieldResult();
            itemField.setFieldApiName(systemField.getFieldApiName());
            itemField.setFieldLabel(systemField.getFieldLabel());
            itemField.setFieldType(systemField.getFieldType());
            data.add(itemField);
            configInfoEntity.setConfigInfo(JSONObject.toJSONString(data));
            outerOaConfigInfoManager.updateById(configInfoEntity);
        }
        return Result.newSuccess();
    }

    /**
     * 删除外部系统字段
     *
     * @param systemField
     * @return
     */
    @RequestMapping(value = "/removeOuterSystemField", method = RequestMethod.POST)
    public Result<Integer> removeOuterSystemField(@RequestBody SystemFieldResult systemField) {
        OuterOaEnterpriseBindEntity entityById = outerOaEnterpriseBindManager
                .getEntityById(systemField.getCurrentDcId());
        if (ObjectUtils.isEmpty(entityById)) {
            return Result.newError(ResultCodeEnum.BIND_ENTERPRISE_ERROR);
        }

        OuterOaConfigInfoEntity configInfoEntity = outerOaConfigInfoManager.getEntityByDataCenterId(
                OuterOaConfigInfoTypeEnum.OUTER_SYSTEM_OBJECT_FIELDS, systemField.getCurrentDcId());

        if (ObjectUtils.isNotEmpty(configInfoEntity)) {
            Set<SystemFieldResult> data = JSONObject.parseObject(configInfoEntity.getConfigInfo(),
                    new TypeReference<Set<SystemFieldResult>>() {
                    });

            // 使用Stream过滤掉要删除的字段
            data = data.stream()
                    .filter(field -> !field.getFieldApiName().equals(systemField.getFieldApiName()))
                    .collect(Collectors.toSet());

            configInfoEntity.setConfigInfo(JSONObject.toJSONString(data));
            outerOaConfigInfoManager.updateById(configInfoEntity);
        }
        return Result.newSuccess();
    }

    private Boolean validUserName(SettingsResult settingsResult, ChannelEnum channelEnum) {
        if (settingsResult.getEmployeeFieldMapping() != null) {
            SystemFieldMappingResult systemFieldMappingResult = channelEnum.buildFieldMapping();
            String erpFieldUserName =
                    systemFieldMappingResult.getItemFieldMappings().stream().filter(item -> item.getCrmFieldApiName().equals("name")).map(SystemFieldMappingResult.ItemFieldMapping::getOuterOAFieldApiName).findFirst().orElse("name");
            Set<SystemFieldMappingResult.ItemFieldMapping> requireFields = settingsResult.getEmployeeFieldMapping().stream().filter(item -> item.getOuterOAFieldApiName().equals(erpFieldUserName)).collect(Collectors.toSet());
            if (CollectionUtils.isEmpty(requireFields)) {
                //避免外部动态列展示空字段数据，后续应该考虑前端显示逻辑
                return false;
            }
        }
        return true;
    }

    /**
     * 返回crm外部字段
     */
    @RequestMapping(value = "/queryCrmSystemField", method = RequestMethod.POST)
    public Result<Set<SystemFieldResult>> queryCrmSystemField(@RequestBody CepArg cepArg,
                                                              @RequestHeader(value = I18NStringManager.X_FS_LOCALE, required = false) String lang) {
        OuterOaEnterpriseBindEntity entityById = outerOaEnterpriseBindManager.getEntityById(cepArg.getCurrentDcId());
        if (ObjectUtils.isEmpty(entityById)) {
            return Result.newError(ResultCodeEnum.BIND_ENTERPRISE_ERROR);
        }
        int tenantId = eieaConverter.enterpriseAccountToId(entityById.getFsEa());
        Set<SystemFieldResult> crmSystemField = descManager.getCrmSystemField(tenantId,
                AccountTypeEnum.EMP_BIND.getCode());
        return Result.newSuccess(crmSystemField);
    }

    /**
     * 返回连接器字段
     *
     * @param arg
     * @return
     */
    @RequestMapping(value = "/getOaConnectorIntro", method = RequestMethod.POST)
    public Result<ConnectorIntro> getOaConnectorIntro(@RequestBody CepArg arg) {
        OuterOaEnterpriseBindEntity enterpriseBindEntity = outerOaEnterpriseBindManager
                .getEntityById(arg.getCurrentDcId());
        if (ObjectUtils.isEmpty(enterpriseBindEntity)) {
            return Result.newError(ResultCodeEnum.BIND_ENTERPRISE_ERROR);
        }
        String fsEa = enterpriseBindEntity.getFsEa();
        Integer tenantId=eieaConverter.enterpriseAccountToId(fsEa);
        if(tenantId==null){
            return Result.newError(ResultCodeEnum.SYSTEM_ERROR);
        }
        String dataCenterId = enterpriseBindEntity.getId();
        OuterOAConnectSettingResult.ConnectParams connectParams = enterpriseBindEntity.getConnectParams();
        if (connectParams == null || connectParams.getStandard_oa() == null) {
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }
        GetConnectorIntroArg getConnectorIntroArg=new GetConnectorIntroArg();
        Result<ConnectorIntro> result = openConnectorManager.getOaConnectorIntro(tenantId.toString(),dataCenterId,connectParams.getStandard_oa().getApiName(),getConnectorIntroArg);
        return result;
    }
    @ApiOperation(value = "获取连接器授权方式列表")
    @PostMapping("/getConnectorAuthTypeList")
    public Result<List<SelectOption>> getConnectorAuthTypeList(@RequestBody(required = false) CepArg arg) {
        OuterOaEnterpriseBindEntity enterpriseBindEntity = outerOaEnterpriseBindManager
                .getEntityById(arg.getCurrentDcId());
        if (ObjectUtils.isEmpty(enterpriseBindEntity)) {
            return Result.newError(ResultCodeEnum.BIND_ENTERPRISE_ERROR);
        }
        String fsEa = enterpriseBindEntity.getFsEa();
        Integer tenantId=eieaConverter.enterpriseAccountToId(fsEa);
        if(tenantId==null){
            return Result.newError(ResultCodeEnum.SYSTEM_ERROR);
        }
        String dataCenterId = enterpriseBindEntity.getId();
        OuterOAConnectSettingResult.ConnectParams connectParams = enterpriseBindEntity.getConnectParams();
        if (connectParams == null || connectParams.getStandard_oa() == null) {
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }
        Result<List<OaConnectorAuthType>> result = openConnectorManager.getConnectorAuthTypeList(tenantId.toString(),dataCenterId,connectParams.getStandard_oa().getApiName());
        if (!result.isSuccess()) {
            return Result.copy(result);
        }
        return Result.newSuccess(OaConnectorAuthType.toSelectOptions(result.getData()));
    }

    @ApiOperation(value = "获取OAuth2授权接口")
    @PostMapping("/getOAuth2AuthUrl")
    public Result<String> getOAuth2AuthUrl(@RequestBody() OaSystemParamsArg arg) {
        OuterOaEnterpriseBindEntity enterpriseBindEntity = outerOaEnterpriseBindManager
                .getEntityById(arg.getCurrentDcId());
        if (ObjectUtils.isEmpty(enterpriseBindEntity)) {
            return Result.newError(ResultCodeEnum.BIND_ENTERPRISE_ERROR);
        }
        String fsEa = enterpriseBindEntity.getFsEa();
        Integer tenantId=eieaConverter.enterpriseAccountToId(fsEa);
        if(tenantId==null){
            return Result.newError(ResultCodeEnum.SYSTEM_ERROR);
        }
        String dataCenterId = enterpriseBindEntity.getId();
        OuterOAConnectSettingResult.ConnectParams connectParams = enterpriseBindEntity.getConnectParams();
        if (connectParams == null || connectParams.getStandard_oa() == null) {
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }
        return openConnectorManager.getOAuth2AuthUrl(tenantId.toString(),dataCenterId,connectParams.getStandard_oa().getApiName(), arg.getSystemParams());
    }

}