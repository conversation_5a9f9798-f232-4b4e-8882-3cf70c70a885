package com.facishare.open.feishu.syncapi.service;

import com.facishare.open.feishu.syncapi.arg.SendTextCardMessagePushArg;
import com.facishare.open.feishu.syncapi.arg.SendTextMessagePushArg;
import com.facishare.open.feishu.syncapi.result.Result;

public interface ExternalMsgService {

    /**
     * 处理文本通知消息
     * @param sendTextMessagePushArg
     * @return
     */
    Result<Void> sendTextMessage(SendTextMessagePushArg sendTextMessagePushArg);

    /**
     * 处理卡片通知消息
     * @param sendTextCardMessagePushArg
     * @return
     */
    Result<Void> sendTextCardMessage(SendTextCardMessagePushArg sendTextCardMessagePushArg);
}
