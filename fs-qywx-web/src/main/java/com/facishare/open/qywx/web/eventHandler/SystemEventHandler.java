package com.facishare.open.qywx.web.eventHandler;

import com.facishare.open.qywx.accountinner.service.QyweixinGatewayInnerService;
import com.facishare.open.qywx.accountsync.constant.Constant;
import com.facishare.open.qywx.accountsync.utils.xml.EventXml;
import com.facishare.open.qywx.accountsync.utils.xml.QyweixinAutoActiveMsgBaseXml;
import com.facishare.open.qywx.accountsync.utils.xml.QyweixinPaySuccessMsgBaseXml;
import com.facishare.open.qywx.accountsync.utils.xml.XStreamUtils;
import com.facishare.open.qywx.web.manager.OANewBaseManager;
import com.facishare.open.qywx.web.manager.QYWeixinManager;
import com.facishare.open.qywx.web.manager.QywxPermissionManager;
import com.facishare.open.qywx.web.service.impl.QyweixinGatewayInnerServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 企业微信代开发应用事件处理器
 * <AUTHOR>
 * @date 2022/12/20
 */
@Slf4j
@Component
public class SystemEventHandler extends EnterpriseWeChatEventHandler {
    @Resource
    private QyweixinGatewayInnerServiceImpl qyweixinGatewayInnerService;
    @Autowired
    private QYWeixinManager qyWeixinManager;
    @Autowired
    private QywxPermissionManager qywxPermissionManager;
    @Resource
    private OANewBaseManager oANewBaseManager;

    @Override
    public void handle(String plainMsg,String appid) {
        super.handle(plainMsg,appid);

        //String plainMsg = null;
        try {
//            plainMsg = decryptMsg(eventProto.getSignature(), eventProto.getTimestamp(),
//                    eventProto.getNonce(), eventProto.getData(),eventProto.getAppId());
            log.info("SystemEventHandler.handle,plainMsg={}", plainMsg);

            //QyweixinMsgBaseXml baseMsgXml = XmlParser.fromXml(plainMsg, QyweixinMsgBaseXml.class);
            QyweixinPaySuccessMsgBaseXml baseMsgXml = XStreamUtils.parseXml(plainMsg, QyweixinPaySuccessMsgBaseXml.class);
            log.info("SystemEventHandler.handle,baseMsgXml={}",baseMsgXml);
            String infoType = baseMsgXml.getInfoType();
            log.info("SystemEventHandler.handle,infoType={}",infoType);

            if(StringUtils.isEmpty(infoType)) {
                EventXml eventXml = XStreamUtils.parseXml(plainMsg, EventXml.class);
                infoType = eventXml.getEvent();
                log.info("SystemEventHandler.handle,infoType2={}",infoType);
            }
            String outEa = baseMsgXml.getAuthCorpId();
            log.info("SystemEventHandler.repMsgEvent,outEa={}", outEa);
            //判断当前企业数据是否可以在当前环境运行
//            if(runInCurrentEnv(outEa,plainMsg)==false){
//                return;
//            }

            //是否执行在新基座
            if(!oANewBaseManager.canRunInNewBase(outEa, plainMsg)) {
                log.info("SystemEventHandler.handle,runInNewBase,outEa={},plainMsg={}", outEa, plainMsg);
                return;
            }

            if(infoType.equals(Constant.PAY_LICENSE_SUCCESS)){

                // 根据订单查询账号信息
                qywxPermissionManager.handlerOrderPaySuccess(baseMsgXml.getOrderId(), appid);
            }
            if(infoType.equals(Constant.AUTO_ACTIVE)){
                // 自动激活
                QyweixinAutoActiveMsgBaseXml qyweixinAutoActiveMsgBaseXml=XStreamUtils.parseXml(plainMsg,QyweixinAutoActiveMsgBaseXml.class);
                List<String> activeCodes = qyweixinAutoActiveMsgBaseXml.getAccountList().stream().map(QyweixinAutoActiveMsgBaseXml.Account::getActiveCode).collect(Collectors.toList());
                qywxPermissionManager.handlerAutoActive(activeCodes,qyweixinAutoActiveMsgBaseXml.getAuthCorpId(), appid);
            }


        } catch (Exception e) {
            log.error("SystemEventHandler.handle,exception={}", e.getMessage(),e);
            return;
        }

    }
}
