package com.facishare.open.oa.base.dbproxy.pg.mapper;

import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaConfigInfoEntity;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

public interface OuterOaConfigInfoMapper extends BaseMapper2<OuterOaConfigInfoEntity> {

    /**
     * 批量插入或更新配置信息 当dcId和type冲突时更新相关字段
     * 
     * @param list       配置信息实体列表
     * @return 影响的记录数
     */
    @Update("<script>" + "INSERT INTO outer_oa_config_info "
            + "(id, channel, dc_id, fs_ea, out_ea, app_id, type, config_info, create_time, update_time) " + "VALUES "
            + "<foreach collection='list' item='item' separator=','>" + "(" + "  #{item.id}," + "  #{item.channel},"
            + "  #{item.dcId}," + "  #{item.fsEa}," + "  #{item.outEa}," + "  #{item.appId}," + "  #{item.type},"
            + "  #{item.configInfo}," + "  #{item.createTime}," +
            "  #{item.updateTime}" + ")" + "</foreach> " + "ON CONFLICT (dc_id, channel,type) DO UPDATE SET "
            + "  channel = EXCLUDED.channel," + "  fs_ea = EXCLUDED.fs_ea," + "  out_ea = EXCLUDED.out_ea,"
            + "  app_id = EXCLUDED.app_id," + "  config_info = EXCLUDED.config_info,"
            + "  update_time = EXCLUDED.update_time" + // create_time不更新，保持原值
            "</script>")
    Integer batchUpsertByDcIdAndType(@Param("list") List<OuterOaConfigInfoEntity> list);

    @Select("SELECT * FROM outer_oa_config_info WHERE dc_id = #{dcId} and type=#{configType} for update limit 1")
    OuterOaConfigInfoEntity selectOaConfigforUpdate(@Param("dcId") String dcId, @Param("configType") String configType);
}
