package com.facishare.open.ding.provider.service;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.facishare.open.ding.api.service.RedisDingService;
import com.facishare.open.ding.api.vo.DeptVo;
import com.facishare.open.ding.api.vo.DingUserVo;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.common.result.ResultCode;
import com.facishare.open.ding.web.redis.RedisDataSource;
import com.github.jedis.support.MergeJedisCmd;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import redis.clients.jedis.Jedis;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2020/9/29 12:09
 * @Version 1.0
 */
@Slf4j
@Service("redisDingService")
public class RedisDingServiceImpl implements RedisDingService {
    @Autowired
    private RedisDataSource redisDataSource;
    public static int INFO_REDIS_TIME_OUT = 60;

    public static int INFO_DEPT_TIME_OUT = 60*60*3;

    public static String DING_DEPT_INFOS="ding_dept:%s";

    @Override
    public Result<Void> saveInfoToRedis(String code, DingUserVo vo) {
        log.info("redisDingService code:{},vo:{}",code,vo);
        String info= JSONObject.toJSONString(vo);
        try {
            redisDataSource.getRedisClient().setex(code,INFO_REDIS_TIME_OUT,info);
        } catch (Exception e) {
            log.error("set dingInfoRedis  user from redis failed,key:{},value:{}", code, info, e);
        }
        return null;
    }

    @Override
    public Result<DingUserVo> getInfoFromRedis(String code) {
        String info = StringUtils.EMPTY;
        //从redis获取用户信息
        try {
            info=  redisDataSource.getRedisClient().get(code);
            if(info==null){
                //防止save的进程还未执行
                log.info("thread sleep....");
                Thread.sleep(1000);
                info=  redisDataSource.getRedisClient().get(code);
            }
        } catch (Exception e) {
            log.error("get ding user from redis failed,key:{}", code, e);
        }
        DingUserVo vo = JSONObject.parseObject(info,DingUserVo.class);
        log.info("redis getInfo :{}",vo);
       return  Result.newSuccess(vo);
    }

    @Override
    public Result<Void> saveDeptInfos(String code, Map<Long, DeptVo> deptMaps) {
        //保存部门信息，避免部门次数多的用户信息超时
        String key=String.format(DING_DEPT_INFOS,code);
        log.info("redisDingService saveDeptInfos code:{}",key);
        String info= JSONObject.toJSONString(deptMaps);
        try {
            redisDataSource.getRedisClient().setex(key,INFO_DEPT_TIME_OUT,info);
        } catch (Exception e) {
            log.error("set dingInfoRedis  user from redis failed,key:{},value:{}", code, info, e);
        }
        return null;
    }

    @Override
    public Result<Map<Long, DeptVo>> getDeptInfos(String code) {
        String info = StringUtils.EMPTY;
        //从redis获取用户信息
        String key=String.format(DING_DEPT_INFOS,code);
        try {
            info=  redisDataSource.getRedisClient().get(key);
        } catch (Exception e) {
            log.error("get ding user from redis failed,key:{}", code, e);
        }
        Map<Long, DeptVo> deptMaps = JSONObject.parseObject(info, new TypeReference<Map<Long, DeptVo>>(){});

        log.info("redisDingService getDeptInfos code:{}",key);
        return  Result.newSuccess(deptMaps);
    }

    @Override
    public Result<Void> deleteInfos(String code) {
        String key=String.format(DING_DEPT_INFOS,code);
        try {
            redisDataSource.getRedisClient().del(key);
        } catch (Exception e) {
            log.error("get ding user from redis failed,key:{}", code, e);
        }
        return null;
    }


}
