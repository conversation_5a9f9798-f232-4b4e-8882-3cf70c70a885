package com.facishare.open.outer.oa.connector.common.api.params;

import com.facishare.open.outer.oa.connector.common.api.annotation.SecurityField;
import com.facishare.open.outer.oa.connector.common.api.info.BaseOuterOaAppInfoParams;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@SuperBuilder
public class StandardAppInfoParams extends BaseOuterOaAppInfoParams {
    private static final long serialVersionUID = -1L;

}
