package com.facishare.open.outer.oa.connector.web.controller.cep;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.facishare.converter.EIEAConverter;
import com.facishare.open.oa.base.dbproxy.configVo.ConstantDb;
import com.facishare.open.oa.base.dbproxy.manager.DescManager;
import com.facishare.open.oa.base.dbproxy.manager.ObjectDataManager;
import com.facishare.open.oa.base.dbproxy.pg.entity.*;
import com.facishare.open.oa.base.dbproxy.pg.manager.*;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaDeptDataParams;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaEmployeeBindParams;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.open.outer.oa.connector.common.api.admin.BaseConnectorVo;
import com.facishare.open.outer.oa.connector.common.api.enums.*;
import com.facishare.open.outer.oa.connector.common.api.info.SettingAccountRulesModel;
import com.facishare.open.outer.oa.connector.common.api.outerInterface.OuterAbstractSettingService;
import com.facishare.open.outer.oa.connector.common.api.params.CepArg;
import com.facishare.open.outer.oa.connector.common.api.result.Result;
import com.facishare.open.outer.oa.connector.common.api.result.ResultCodeEnum;
import com.facishare.open.outer.oa.connector.common.api.result.SystemFieldMappingResult;
import com.facishare.open.outer.oa.connector.i18n.I18NStringManager;
import com.facishare.open.outer.oa.connector.web.manager.OuterOASettingFactory;
import com.facishare.open.outer.oa.connector.web.model.admin.IdListsArg;
import com.facishare.open.outer.oa.connector.web.model.admin.OuterSettingsBindArg;
import com.facishare.open.outer.oa.connector.web.model.admin.QueryBindArg;
import com.facishare.open.outer.oa.connector.web.model.excel.DownloadTemplateArg;
import com.facishare.open.outer.oa.connector.web.model.excel.ImportBindDataArg;
import com.facishare.open.outer.oa.connector.web.result.*;
import com.facishare.open.outer.oa.connector.web.result.excel.ExcelFileResult;
import com.facishare.open.outer.oa.connector.web.result.excel.ImportResult;
import com.facishare.open.outer.oa.connector.web.service.ExcelFileService;
import com.facishare.open.outer.oa.connector.web.util.SearchQueryUtils;
import com.fxiaoke.api.IdGenerator;
import com.fxiaoke.crmrestapi.arg.v3.FindV3Arg;
import com.fxiaoke.crmrestapi.common.contants.CrmConstants;
import com.fxiaoke.crmrestapi.common.contants.FilterOperatorEnum;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.common.data.SearchTemplateQuery;
import com.fxiaoke.crmrestapi.result.ActionAddResult;
import com.fxiaoke.crmrestapi.result.v3.ObjectDataQueryListResult;
import com.fxiaoke.crmrestapi.service.ObjectDataServiceV3;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.async.DeferredResult;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import com.facishare.open.outer.oa.connector.web.util.AsyncHelper;

import static io.protostuff.MapSchema.MessageFactories.HashMap;

/**
 * 账号绑定管理Controller
 */
@Slf4j
@RestController
@RequestMapping("/oaBase/objectMapping")
public class ObjectDataMappingController extends BaseController {
    @Autowired
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;
    @Autowired
    private OuterOaConfigInfoManager outerOaConfigInfoManager;
    @Autowired
    private ObjectDataServiceV3 objectDataServiceV3;
    @Autowired
    private DescManager descManager;
    @Autowired
    private OuterOaEmployeeBindManager outerOaEmployeeBindManager;
    @Autowired
    private OuterOaEmployeeDataManager outerOaEmployeeDataManager;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private OuterOASettingFactory outerOASettingFactory;
    @Autowired
    private ExcelFileService excelFileService;
    @Autowired
    private OuterOaDeptDataManager outerOaDeptDataManager;
    @Autowired
    private ObjectDataManager objectDataManager;

    /**
     * 查询账号绑定列表
     */
    @RequestMapping(value = "/queryBind", method = RequestMethod.POST)
    public Result<OuterDataMappingResult> queryBind(@RequestBody QueryBindArg queryBindArg) {
        try {
            OuterOaEnterpriseBindEntity outerOaEnterpriseBindEntity = outerOaEnterpriseBindManager
                    .getEntityById(queryBindArg.getCurrentDcId());
            List<EmployeeBindResult> employeeBindResults = Lists.newArrayList();
            OuterDataMappingResult outerDataMappingResult = new OuterDataMappingResult();
            // 返回页面规则，前端有些按钮依赖：
            OuterOaConfigInfoEntity entityByDataCenterId = outerOaConfigInfoManager.getEntityByDataCenterId(
                    OuterOaConfigInfoTypeEnum.SETTING_BIND_RULES, queryBindArg.getCurrentDcId());

            if (ObjectUtils.isNotEmpty(entityByDataCenterId)) {
                SettingAccountRulesModel settingAccountRulesModel = JSONObject.parseObject(
                        entityByDataCenterId.getConfigInfo(), new TypeReference<SettingAccountRulesModel>() {
                        });
                outerDataMappingResult.setSettingAccountRulesModel(settingAccountRulesModel);

            }
            List<OuterOaEmployeeBindEntity> outerOaEmployeeBindEntities = null;
            // 根据筛选条件匹配，是先找到CRM人员。这个时候需要回表查询中间表，返回对应的绑定人员
            Boolean LookUp = false;
            Map<String, String> crmToOuterMap = Maps.newHashMap();
            List<Object> crmUserIds = Lists.newArrayList();

            int tenantId = eieaConverter.enterpriseAccountToId(outerOaEnterpriseBindEntity.getFsEa());
            HeaderObj headerObj = HeaderObj.newInstance(tenantId, ConstantDb.CRM_OPERATOR_USER_ID);
            FindV3Arg findV3Arg = new FindV3Arg();
            findV3Arg.setDescribeApiName(queryBindArg.getAccountTypeEnum().getCode());
            // 返回查询字段
            Result<SystemFieldMappingResult> orInitFieldMapping = descManager.getOrInitFieldMapping(
                    outerOaEnterpriseBindEntity, OuterOaConfigInfoTypeEnum.EMPLOYEE_UNIQUE_IDENTITY);
            List<SystemFieldMappingResult.ItemFieldMapping> useEmployeeFieldMappings = orInitFieldMapping.getData()
                    .getItemFieldMappings();
            List<String> selectFields = useEmployeeFieldMappings.stream().map(item -> item.getCrmFieldApiName())
                    .collect(Collectors.toList());
            // 需要补充主属部门
            selectFields.add(CRMEmployeeFiledEnum.OWNER_DEPARTMENT.getCode());
            // selectFields.add(CRMEmployeeFiledEnum.MAIN_DEPARTMENT.getCode());
            selectFields.add(CRMEmployeeFiledEnum.STATUS.getCode());
            selectFields.add(CRMEmployeeFiledEnum.USER_ID.getCode());
            selectFields.add(CRMEmployeeFiledEnum.PHONE.getCode());
            findV3Arg.setSelectFields(selectFields);
            // 设置总数
            Integer total = 0;
            String searchQueryInfo = null;
            if (ObjectUtils.isEmpty(queryBindArg.getCrmQueryFileValue())) {
                OuterOaEmployeeBindParams outerOaEmployeeBindParams = OuterOaEmployeeBindParams.builder()
                        .dcId(queryBindArg.getCurrentDcId()).channel(outerOaEnterpriseBindEntity.getChannel())
                        .page(queryBindArg.getPage()).pageSize(queryBindArg.getPageSize()).build();

                Page<OuterOaEmployeeBindEntity> entityPage = outerOaEmployeeBindManager
                        .getPageEntities(outerOaEmployeeBindParams);
                total = Math.toIntExact(entityPage.getTotal());
                outerOaEmployeeBindEntities = entityPage.getRecords();

                crmUserIds = outerOaEmployeeBindEntities.stream().map(item -> {
                    crmToOuterMap.put(item.getFsEmpId(), item.getOutEmpId());
                    return item.getFsEmpId();
                }).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(crmUserIds)) {
                    // 没有绑定的中间表
                    return Result.newSuccess(outerDataMappingResult);
                }
                searchQueryInfo = SearchQueryUtils.buildSearchQueryInfo(CRMEmployeeFiledEnum.USER_ID.getCode(),
                        crmUserIds, "IN");
            } else {
                LookUp = true;
                // 构建查询条件,buildSearchQueryInfo兼容不筛选
                searchQueryInfo = SearchQueryUtils.buildSearchQueryInfo(queryBindArg.getCrmQueryFiled(),
                        Lists.newArrayList(queryBindArg.getCrmQueryFileValue()), "LIKE");
            }
            findV3Arg.setSearchQueryInfo(searchQueryInfo);
            com.fxiaoke.crmrestapi.common.result.Result<ObjectDataQueryListResult> objectDataQueryListResult = objectDataServiceV3
                    .queryList(headerObj, findV3Arg);
            // 处理返回结果
            if (objectDataQueryListResult != null && objectDataQueryListResult.isSuccess()) {
                if (LookUp) {
                    List<String> collect = objectDataQueryListResult.getData().getQueryResult().getDataList().stream()
                            .map(item -> String.valueOf(item.get("user_id"))).collect(Collectors.toList());
                    outerOaEmployeeBindEntities = outerOaEmployeeBindManager.getEntitiesByFsEmpIds(collect,
                            queryBindArg.getCurrentDcId());
                    total = employeeBindResults.size();
                    outerOaEmployeeBindEntities
                            .forEach(item -> crmToOuterMap.put(item.getFsEmpId(), item.getOutEmpId()));
                }
                List<String> needOuterUserIds = crmToOuterMap.values().stream().collect(Collectors.toList());
                List<OuterOaEmployeeDataEntity> outerOaEmployeeDataEntities = outerOaEmployeeDataManager
                        .queryByChannelAndAppIdAndEmpIds(outerOaEnterpriseBindEntity.getOutEa(),
                                outerOaEnterpriseBindEntity.getChannel(), outerOaEnterpriseBindEntity.getAppId(),
                                needOuterUserIds);
                // 将 outerOaEmployeeDataEntities 转换为 Map，outer_user_id 为键，整个 entity 为值
                Map<String, OuterOaEmployeeDataEntity> outerUserIdToEntityMap = outerOaEmployeeDataEntities.stream()
                        .collect(Collectors.toMap(OuterOaEmployeeDataEntity::getOutUserId, entity -> entity,
                                (existing, replacement) -> existing));
                ObjectDataQueryListResult.QueryResult queryResult = objectDataQueryListResult.getData()
                        .getQueryResult();
                Map<String, ObjectData> crmObjectDataMap = queryResult.getDataList().stream().collect(Collectors
                        .toMap(item->String.valueOf(item.get("user_id")), objectData -> objectData, (existing, replacement) -> existing));

                for (OuterOaEmployeeBindEntity outerOaEmployeeBindEntity : outerOaEmployeeBindEntities) {
                    EmployeeBindResult employeeBindResult = new EmployeeBindResult();
                    employeeBindResult.setId(outerOaEmployeeBindEntity.getId());
                    employeeBindResult.setBindStatus(outerOaEmployeeBindEntity.getBindStatus());
                    employeeBindResult.setCrmEmpId(outerOaEmployeeBindEntity.getFsEmpId());
                    employeeBindResult.setOutEmpId(outerOaEmployeeBindEntity.getOutEmpId());
                    OuterOaEmployeeDataEntity outerOaEmployeeDataEntity = outerUserIdToEntityMap
                            .get(outerOaEmployeeBindEntity.getOutEmpId());
                    // 拿到crm对应信息
                    if (crmObjectDataMap.get(outerOaEmployeeBindEntity.getFsEmpId()) != null) {
                        employeeBindResult.setCrmMainDept(crmObjectDataMap.get(outerOaEmployeeBindEntity.getFsEmpId())
                                .getString(CRMEmployeeFiledEnum.OWNER_DEPARTMENT.getCode()));
                        employeeBindResult.setCrmDataMap(crmObjectDataMap.get(outerOaEmployeeBindEntity.getFsEmpId()));
                        employeeBindResult.setCrmEmpStatus(crmObjectDataMap.get(outerOaEmployeeBindEntity.getFsEmpId())
                                .getString(CRMEmployeeFiledEnum.STATUS.getCode()));
                    }
                    if (outerUserIdToEntityMap.get(outerOaEmployeeBindEntity.getOutEmpId()) != null) {
                        // 外部员工姓名
                        employeeBindResult
                                .setOutEmpName(getOuterEmpName(outerOaEmployeeDataEntity, useEmployeeFieldMappings));
                        // 外部员工部门
                        String outerDeptName = getOuterDeptName(outerOaEnterpriseBindEntity.getOutEa(),
                                outerUserIdToEntityMap.get(outerOaEmployeeBindEntity.getOutEmpId()).getOutDeptId());
                        employeeBindResult.setOutEmpDept(outerDeptName);
                    }
                    employeeBindResults.add(employeeBindResult);
                }
                outerDataMappingResult.setTotal(total);
                outerDataMappingResult.setEmployeeBindResults(employeeBindResults);
                outerDataMappingResult.setPageNumber(queryBindArg.getPage());
                outerDataMappingResult.setPageSize(queryBindArg.getPageSize());
            }

            return Result.newSuccess(outerDataMappingResult);
        } catch (Exception e) {
            log.error("查询账号绑定列表失败", e);
            return Result.newError(ResultCodeEnum.SYSTEM_ERROR);
        }
    }

    private String getOuterEmpName(OuterOaEmployeeDataEntity outerOaEmployeeDataEntity,
            List<SystemFieldMappingResult.ItemFieldMapping> userFieldMappings) {
        for (SystemFieldMappingResult.ItemFieldMapping userFieldMapping : userFieldMappings) {
            if (userFieldMapping.getCrmFieldApiName().equals(CRMEmployeeFiledEnum.NAME.getCode())) {
                return outerOaEmployeeDataEntity.getOutUserInfo().getString(userFieldMapping.getCrmFieldApiName());
            }
        }
        return null;
    }

    private String getOuterDeptName(String outEa, String outerDeptId) {
        OuterOaDeptDataParams outerOaDeptDataParams = OuterOaDeptDataParams.builder().outEa(outEa)
                .outDeptId(outerDeptId).build();
        List<OuterOaDeptDataEntity> entities = outerOaDeptDataManager.getEntities(outerOaDeptDataParams);
        if (ObjectUtils.isNotEmpty(entities)) {
            return entities.get(0).getDeptName();
        }
        return null;
    }

    /**
     * 手动绑定账号
     */
    @PostMapping("/manualBind")
    public Result<Void> manualBind(@RequestBody OuterSettingsBindArg.OuterBindParams outerBindParams) {
        OuterOaEnterpriseBindEntity entityById = outerOaEnterpriseBindManager
                .getEntityById(outerBindParams.getCurrentDcId());
        if (ObjectUtils.isEmpty(entityById)) {
            return Result.newError(ResultCodeEnum.OUT_EA_NOT_BIND);
        }
        // 需要判断下CRM_userid或者Dest_userid有咩有绑定
        List<OuterOaEmployeeBindEntity> entitiesOrOuterFsEmpId = outerOaEmployeeBindManager.getEntitiesOrOuterFsEmpId(
                outerBindParams.getCurrentDcId(), outerBindParams.getCrmDataId(), outerBindParams.getOutDataId());
        if (CollectionUtils.isNotEmpty(entitiesOrOuterFsEmpId)) {
            return Result.newError(ResultCodeEnum.EMPLOYEE_HAS_BIND);
        }

        OuterOaEmployeeBindEntity outerOaEmployeeBindEntity = OuterOaEmployeeBindEntity.builder()
                .fsEa(entityById.getFsEa()).outEa(entityById.getOutEa()).id(IdGenerator.get())
                .dcId(outerBindParams.getCurrentDcId()).appId(entityById.getAppId())
                .createTime(System.currentTimeMillis()).channel(entityById.getChannel())
                .updateTime(System.currentTimeMillis()).fsEmpId(outerBindParams.getCrmDataId())
                .bindStatus(BindStatusEnum.normal).outEmpId(outerBindParams.getOutDataId()).build();
        Integer count = outerOaEmployeeBindManager.batchUpsert(Lists.newArrayList(outerOaEmployeeBindEntity));

        try {
            if (entityById.getChannel() == ChannelEnum.qywx) {
                //先单独
                final BaseConnectorVo baseConnectorVo = JSON.parseObject(entityById.getConnectInfo(), BaseConnectorVo.class);
                final OuterAbstractSettingService implementation = outerOASettingFactory
                        .getImplementation(entityById.getChannel(), baseConnectorVo.getAppType());
                implementation.employeeBindChangeEvent(outerBindParams.getCurrentDcId(), Lists.newArrayList(outerOaEmployeeBindEntity.getId()), EmplyeeBindChangeTypeEnum.employee_bind);
            }
        } catch (Exception e) {
            log.warn("ObjectDataMappingController.manualBind.e=", e);
        }

        return Result.newSuccess();
    }

    /**
     * 批量绑定
     */
    @PostMapping("/batchBind")
    public Result<Integer> batchBind(@RequestBody OuterSettingsBindArg.BatchEmpParams outerBindParams) {
        OuterOaEnterpriseBindEntity entityById = outerOaEnterpriseBindManager
                .getEntityById(outerBindParams.getCurrentDcId());
        if (ObjectUtils.isEmpty(entityById)) {
            return Result.newError(ResultCodeEnum.OUT_EA_NOT_BIND);
        }
        // 批量绑定，需要校验下有没有设置了自动绑定规则
        OuterOaConfigInfoEntity entityByDataCenterId = outerOaConfigInfoManager.getEntityByDataCenterId(
                OuterOaConfigInfoTypeEnum.EMPLOYEE_UNIQUE_IDENTITY, outerBindParams.getCurrentDcId());
        if (ObjectUtils.isEmpty(entityByDataCenterId)) {
            return Result.newError(ResultCodeEnum.EMPLOYEE_NEED_SETTING_RULES);
        }
        SystemFieldMappingResult systemFieldMappingResult = JSONObject.parseObject(entityByDataCenterId.getConfigInfo(),
                SystemFieldMappingResult.class);
        // 需要根据配置，拿到CRM->ERP对应的字段信息
        List<SystemFieldMappingResult.ItemFieldMapping> itemFieldMappings = systemFieldMappingResult
                .getItemFieldMappings();
        if (CollectionUtils.isEmpty(itemFieldMappings)) {
            return Result.newError(ResultCodeEnum.EMPLOYEE_NEED_SETTING_RULES);
        }
        SystemFieldMappingResult.ItemFieldMapping uniqueEmployeeFields = itemFieldMappings.stream()
                .filter(item -> Boolean.valueOf(item.getMatchUnique())).findFirst().orElse(null);
        if (ObjectUtils.isEmpty(uniqueEmployeeFields)) {
            return Result.newError(ResultCodeEnum.EMPLOYEE_NEED_SETTING_RULES);
        }

        String crmField = uniqueEmployeeFields.getCrmFieldApiName();
        String outerOAField = uniqueEmployeeFields.getOuterOAFieldApiName();
        String outerDataText = uniqueEmployeeFields.getOuterDataText();
        Integer tenantId = eieaConverter.enterpriseAccountToId(entityById.getFsEa());
        List<String> crmQueryField = new ArrayList<>();
        outerBindParams.getUserList().forEach(item -> {
            crmQueryField.add(String.valueOf(item.getOuterData().get(outerOAField)));
        });
        // 需要分割成10个一次，避免底层查询数据量过大
        List<List<String>> crmQueryFieldList = Lists.partition(crmQueryField, 10);
        Map<String, String> crmQueryValueToIds = Maps.newHashMap();
        for (List<String> itemList : crmQueryFieldList) {
            crmQueryValueToIds.putAll(batchQueryCrmData(tenantId, "PersonnelObj", itemList, crmField));
        }
        LogUtils.info("query crmdata result value:{}",  crmQueryValueToIds);
        List<OuterOaEmployeeBindEntity> outerOaEmployeeBindEntities = Lists.newArrayList();
        for (EmpDataListItemVO empDataListItemVO : outerBindParams.getUserList()) {
            // 根据OA的数据，查询出对应的CRM数据
            if (ObjectUtils.isNotEmpty(crmQueryValueToIds.get(empDataListItemVO.getOuterData().get(outerOAField)))) {
                OuterOaEmployeeBindEntity outerOaEmployeeBindEntity = OuterOaEmployeeBindEntity.builder()
                        .fsEa(entityById.getFsEa()).outEa(entityById.getOutEa()).channel(entityById.getChannel())
                        .appId(entityById.getAppId()).dcId(outerBindParams.getCurrentDcId())
                        .bindStatus(BindStatusEnum.normal)
                        .fsEmpId(crmQueryValueToIds.get(empDataListItemVO.getOuterData().get(outerOAField)))
                        .outEmpId(empDataListItemVO.getOutEmpId()).createTime(System.currentTimeMillis())
                        .updateTime(System.currentTimeMillis()).id(IdGenerator.get()).build();
                outerOaEmployeeBindEntities.add(outerOaEmployeeBindEntity);
            }
        }
        Integer count = outerOaEmployeeBindManager.batchUpsert(outerOaEmployeeBindEntities);

        try {
            if (entityById.getChannel() == ChannelEnum.qywx) {
                //先单独
                final BaseConnectorVo baseConnectorVo = JSON.parseObject(entityById.getConnectInfo(), BaseConnectorVo.class);
                final OuterAbstractSettingService implementation = outerOASettingFactory
                        .getImplementation(entityById.getChannel(), baseConnectorVo.getAppType());
                implementation.employeeBindChangeEvent(outerBindParams.getCurrentDcId(), outerOaEmployeeBindEntities.stream().map(OuterOaEmployeeBindEntity::getId).collect(Collectors.toList()), EmplyeeBindChangeTypeEnum.employee_bind);
            }
        } catch (Exception e) {
            log.warn("ObjectDataMappingController.batchBind.e=", e);
        }

        return Result.newSuccess(count);
    }

    private Map<String, String> batchQueryCrmData(Integer tenantId, String objectApiName, List<String> dataValues,
            String objectField) {
        Map<String, String> queryValueToIds = Maps.newHashMap();
        HeaderObj headerObj = HeaderObj.newInstance(tenantId, CrmConstants.SYSTEM_USER);
        FindV3Arg findV3Arg = new FindV3Arg();
        findV3Arg.setDescribeApiName(objectApiName);
        findV3Arg.setIncludeInvalid(true);
        SearchTemplateQuery searchQueryInfo = new SearchTemplateQuery();
        searchQueryInfo.setPermissionType(0);
        searchQueryInfo.addFilter(objectField, dataValues, FilterOperatorEnum.IN);
        findV3Arg.setSearchQueryInfo(JSONObject.toJSONString(searchQueryInfo));
        com.fxiaoke.crmrestapi.common.result.Result<ObjectDataQueryListResult> listResult = objectDataServiceV3
                .queryList(headerObj, findV3Arg);
        if (!listResult.isSuccess()) {
            log.warn("batch query data error,{}", listResult.getMessage());
        }
        List<ObjectData> dataList = listResult.getData().getQueryResult().getDataList();
        dataList.forEach(objectData -> {
            queryValueToIds.put(objectData.getString(objectField), objectData.getString("user_id"));
        });
        return queryValueToIds;
    }

    /**
     * 解除账号绑定
     */
    @PostMapping("/batchUnbind")
    public Result<Void> batchUnbind(@RequestBody IdListsArg idListsArg) {
        // 获取数据中心信息
        OuterOaEnterpriseBindEntity entityById = outerOaEnterpriseBindManager
                .getEntityById(idListsArg.getCurrentDcId());
        if (entityById == null) {
            return Result.newError(ResultCodeEnum.BIND_ENTERPRISE_ERROR);
        }
        Integer data = outerOaEmployeeBindManager.updateBindStatusByIds(idListsArg.getIdsList(), BindStatusEnum.stop);
        return Result.newSuccess();
    }

    /**
     * 删除账号绑定
     */
    @PostMapping("/deleteBind")
    public Result<Void> deleteBind(@RequestBody IdListsArg idListsArg) {
        // 获取数据中心信息
        OuterOaEnterpriseBindEntity entityById = outerOaEnterpriseBindManager
                .getEntityById(idListsArg.getCurrentDcId());
        if (entityById == null) {
            return Result.newError(ResultCodeEnum.BIND_ENTERPRISE_ERROR);
        }

        try {
            if (entityById.getChannel() == ChannelEnum.qywx) {
                //先单独
                final BaseConnectorVo baseConnectorVo = JSON.parseObject(entityById.getConnectInfo(), BaseConnectorVo.class);
                final OuterAbstractSettingService implementation = outerOASettingFactory
                        .getImplementation(entityById.getChannel(), baseConnectorVo.getAppType());
                implementation.employeeBindChangeEvent(idListsArg.getCurrentDcId(), Lists.newArrayList(idListsArg.getIdsList()), EmplyeeBindChangeTypeEnum.employee_unbind);
            }
        } catch (Exception e) {
            log.warn("ObjectDataMappingController.manualBind.e=", e);
        }

        Integer data = outerOaEmployeeBindManager.batchDeleteByIds(idListsArg.getIdsList());
        return Result.newSuccess();
    }

    /**
     * 获取外部员工列表(用于手动绑定)
     */
    @PostMapping("/getExternalEmployees")
    public Result<EmpDataList> getExternalEmployees(@RequestBody OuterSettingsBindArg.QueryEmpArg queryEmpArg) {
        try {
            // 获取数据中心信息
            OuterOaEnterpriseBindEntity entityById = outerOaEnterpriseBindManager
                    .getEntityById(queryEmpArg.getCurrentDcId());
            if (entityById == null) {
                return Result.newError(ResultCodeEnum.BIND_ENTERPRISE_ERROR);
            }
            // 查询未绑定的员工数据,需要根据配置文件，转换底层对应text语句。前端查询字段映射到text

            IPage<OuterOaEmployeeDataEntity> pageResult = outerOaEmployeeDataManager.queryUnboundEmployeesByOuterField(
                    entityById, queryEmpArg.getPage(), queryEmpArg.getPageSize(), queryEmpArg.getQueryFields(),
                    queryEmpArg.getQueryValue());
            // 构建返回结果
            EmpDataList result = new EmpDataList();
            result.setTotal(pageResult.getTotal());
            Result<SystemFieldMappingResult> orInitFieldMapping = descManager.getOrInitFieldMapping(entityById,
                    OuterOaConfigInfoTypeEnum.EMPLOYEE_UNIQUE_IDENTITY);
            if (!orInitFieldMapping.isSuccess() || ObjectUtils.isEmpty(orInitFieldMapping.getData())) {
                log.warn("getOrInitFieldMapping error,{}", orInitFieldMapping.getMsg());
            }
            Set<EmpDataList.DynamicColumn> dynamicColumns = Sets.newHashSet();
            Map<String, String> outerOAEmployeeDataText = Maps.newHashMap();
            orInitFieldMapping.getData().getItemFieldMappings().stream().forEach(item -> {
                outerOAEmployeeDataText.put(item.getOuterOAFieldApiName(), item.getOuterDataText());
                EmpDataList.DynamicColumn dynamicColumn = new EmpDataList.DynamicColumn();
                dynamicColumn.setName(item.getOuterOAFieldApiName());
                dynamicColumn.setLabel(item.getOuterOAFieldLabel());
                dynamicColumns.add(dynamicColumn);
            });
            // 获取全部部门
            List<String> outDeptIds = pageResult.getRecords().stream().map(OuterOaEmployeeDataEntity::getOutDeptId)
                    .collect(Collectors.toList());
            List<OuterOaDeptDataEntity> outerOaDeptDataEntities = outerOaDeptDataManager.batchGetByOutDeptIds(
                    entityById.getOutEa(), entityById.getChannel(), entityById.getAppId(), outDeptIds);
            Map<String, String> outDeptIdToName = outerOaDeptDataEntities.stream()
                    .collect(Collectors.toMap(OuterOaDeptDataEntity::getOutDeptId, OuterOaDeptDataEntity::getDeptName));
            List<EmpDataListItemVO> userList = Lists.newArrayList();
            //需要处理部门
            Field departmentField =
                    outerOaEmployeeDataManager.getDepartmentField(entityById.getChannel().getEmployeeObjectClass());
            //默认添加展示主属部门
            if(!outerOAEmployeeDataText.keySet().contains(departmentField.getName())){
                EmpDataList.DynamicColumn dynamicColumn = new EmpDataList.DynamicColumn();
                dynamicColumn.setName(departmentField.getName());
                dynamicColumn.setLabel(CRMEmployeeFiledEnum.MAIN_DEPARTMENT.getDesc());//默认label
                Result<Set<com.facishare.open.outer.oa.connector.common.api.result.SystemFieldResult>> outerSystemField = descManager.getOuterSystemField(entityById, entityById.getChannel());
                if(outerSystemField.isSuccess()&&CollectionUtils.isNotEmpty(outerSystemField.getData())){
                    List<com.facishare.open.outer.oa.connector.common.api.result.SystemFieldResult> departField=outerSystemField.getData().stream()
                            .filter(v->departmentField.getName().equals(v.getFieldApiName())).collect(Collectors.toList());
                    if(CollectionUtils.isNotEmpty(departField)){
                        dynamicColumn.setLabel(departField.get(0).getFieldLabel());
                    }
                }
                dynamicColumns.add(dynamicColumn);
            }
            for (OuterOaEmployeeDataEntity record : pageResult.getRecords()) {
                // 需要根据渠道返回对应的数据id
                EmpDataListItemVO erpDataListItemVO = new EmpDataListItemVO();
                erpDataListItemVO.setOutEmpId(record.getOutUserId());
                Map<String, Object> outerData = Maps.newHashMap();
                if (record.getOutUserInfo() != null) {
                    outerData.putAll(JSON.parseObject(record.getOutUserInfo().toJSONString(), Map.class));
                }
                outerData.put( departmentField.getName(), outDeptIdToName.get(record.getOutDeptId()));
                erpDataListItemVO.setOuterData(outerData);
                userList.add(erpDataListItemVO);
            }
            // 设置返回结果中的动态列和用户列表
            result.setDynamicColumns(dynamicColumns);
            result.setUserList(userList);
            return Result.newSuccess(result);
        } catch (Exception e) {
            log.error("getData error:{}", e);
            return Result.newError(ResultCodeEnum.SYSTEM_ERROR);
        }
    }

    /**
     * 手动触发重新查询外部员工
     */
    @PostMapping("/refreshOuterEmployees")

    public DeferredResult<Result<Void>> refreshExternalEmployees(@RequestBody CepArg cepArg) {
        OuterOaEnterpriseBindEntity entityById = outerOaEnterpriseBindManager.getEntityById(cepArg.getCurrentDcId());
        if (entityById == null) {
            DeferredResult<Result<Void>> result = new DeferredResult<>();
            result.setResult(Result.newError(ResultCodeEnum.BIND_ENTERPRISE_ERROR));
            return result;
        }
        final BaseConnectorVo baseConnectorVo = JSON.parseObject(entityById.getConnectInfo(), BaseConnectorVo.class);
        final OuterAbstractSettingService implementation = outerOASettingFactory
                .getImplementation(entityById.getChannel(), baseConnectorVo.getAppType());
        return AsyncHelper.executeAsync(
                () -> implementation.refreshOuterEmpData(entityById.getId(), entityById.getChannel()), 10,ResultCodeEnum.ASYNC_ALERT);
    }

    /**
     * 返回可以筛选的CRM字段列表
     */
    @PostMapping("/queryCrmFilterField")
    public Result<Set<SystemFieldMappingResult.SystemField>> queryCrmFilterField(@RequestBody CepArg cepArg) {
        OuterOaEnterpriseBindEntity entityById = outerOaEnterpriseBindManager.getEntityById(cepArg.getCurrentDcId());
        if (entityById == null) {
            return Result.newError(ResultCodeEnum.BIND_ENTERPRISE_ERROR);
        }
        Set<SystemFieldMappingResult.SystemField> systemFields = descManager
                .getLayoutCrmFilterField(cepArg.getCurrentDcId(), entityById.getChannel());

        return Result.newSuccess(systemFields);
    }
    /**
     * 返回out_oa外部字段
     */
    @RequestMapping(value = "/queryOuterFilterField", method = RequestMethod.POST)
    public Result<Set<SystemFieldMappingResult.SystemField>> queryOuterSystemField(@RequestBody CepArg cepArg,
                                                                                                                        @RequestHeader(value = I18NStringManager.X_FS_LOCALE, required = false) String lang) {
        OuterOaEnterpriseBindEntity entityById = outerOaEnterpriseBindManager.getEntityById(cepArg.getCurrentDcId());
        if (ObjectUtils.isEmpty(entityById)) {
            return Result.newError(ResultCodeEnum.BIND_ENTERPRISE_ERROR);
        }
        Set<SystemFieldMappingResult.SystemField> systemFields=Sets.newHashSet();

        Result<SystemFieldMappingResult> orInitFieldMapping = descManager.getOrInitFieldMapping(entityById,
                OuterOaConfigInfoTypeEnum.EMPLOYEE_UNIQUE_IDENTITY);
        if(orInitFieldMapping.isSuccess()&&orInitFieldMapping.getData()!=null){

            for (SystemFieldMappingResult.ItemFieldMapping itemFieldMapping : orInitFieldMapping.getData().getItemFieldMappings()) {
                if(itemFieldMapping.getOuterDataText()!=null){
                    SystemFieldMappingResult.SystemField systemField=new SystemFieldMappingResult.SystemField();
                    systemField.setFieldLabel(itemFieldMapping.getOuterOAFieldLabel());
                    systemField.setFieldApiName(itemFieldMapping.getOuterOAFieldApiName());
                    systemField.setText(itemFieldMapping.getOuterDataText());
                    systemField.setFieldType(FieldTypeEnum.text);
                    if(itemFieldMapping.getFieldType()!=null){
                        systemField.setFieldType(itemFieldMapping.getFieldType());
                    }
                    systemFields.add(systemField);
                }
            }
        }
        return Result.newSuccess(systemFields);

    }
    /**
     * 返回已经绑定的CRMuserID
     */
    @PostMapping("/queryCrmUserIds")
    public Result<SystemFieldMappingResult.CRMUserIds> queryCrmUserIds(@RequestBody CepArg cepArg) {
        List<String> crmUserIdsByDcId = outerOaEmployeeBindManager.getCrmUserIdsByDcId(cepArg.getCurrentDcId());
        return Result.newSuccess(new SystemFieldMappingResult.CRMUserIds(crmUserIdsByDcId));

    }

    /**
     * 导出账号绑定Excel
     */
    @PostMapping("/exportBindData")
    public Result<ExcelFileResult> exportBindData(@RequestBody CepArg arg) {
        try {
            return excelFileService.exportBindData(arg.getCurrentDcId());
        } catch (Exception e) {
            log.error("导出账号绑定数据失败", e);
            return Result.newError(ResultCodeEnum.SYSTEM_ERROR);
        }
    }

    /**
     * 下载绑定模板
     */
    @PostMapping("/downloadTemplate")
    public Result<ExcelFileResult> downloadTemplate(@RequestBody DownloadTemplateArg arg) {
        try {
            return excelFileService.downloadTemplate(arg);
        } catch (Exception e) {
            log.error("下载绑定模板失败", e);
            return Result.newError(ResultCodeEnum.SYSTEM_ERROR);
        }
    }

    /**
     * 导入账号绑定数据
     */
    @PostMapping("/importBindData")
    public Result<ImportResult> importBindData(@RequestBody ImportBindDataArg arg) {
        try {
            return excelFileService.importBindData(arg);
        } catch (Exception e) {
            log.error("导入账号绑定数据失败", e);
            return Result.newError(ResultCodeEnum.SYSTEM_ERROR);
        }
    }

    /**
     * 批量同步
     */
    @PostMapping("/batchSyncData")
    public Result<EmployeeSyncResult> batchSyncData(@RequestBody OuterSettingsBindArg.BatchEmpParams outerBindParams) {
        OuterOaEnterpriseBindEntity entityById = outerOaEnterpriseBindManager
                .getEntityById(outerBindParams.getCurrentDcId());
        if (ObjectUtils.isEmpty(entityById)) {
            return Result.newError(ResultCodeEnum.OUT_EA_NOT_BIND);
        }
        EmployeeSyncResult employeeSyncResult = new EmployeeSyncResult();
        // 批量绑定，需要校验下有没有设置了自动绑定规则
        OuterOaConfigInfoEntity entityByDataCenterId = outerOaConfigInfoManager.getEntityByDataCenterId(
                OuterOaConfigInfoTypeEnum.EMPLOYEE_UNIQUE_IDENTITY, outerBindParams.getCurrentDcId());
        if (ObjectUtils.isEmpty(entityByDataCenterId)) {
            return Result.newError(ResultCodeEnum.EMPLOYEE_NEED_SETTING_RULES);
        }
        List<String> outUserIds = outerBindParams.getUserList().stream().map(EmpDataListItemVO::getOutEmpId)
                .collect(Collectors.toList());
        StringBuilder failData = new StringBuilder();
        Map<String, Map<String, Object>> empData = outerBindParams.getUserList().stream()
                .collect(Collectors.toMap(EmpDataListItemVO::getOutEmpId, EmpDataListItemVO::getOuterData));
        Integer successCount = 0;
        Integer failCount = 0;
        for (String outUserId : outUserIds) {
            Result<ActionAddResult> employee = objectDataManager.createEmployee(entityById, outUserId);
            if (employee.isSuccess()) {
                try {
                    if (entityById.getChannel() == ChannelEnum.qywx) {
                        List<OuterOaEmployeeBindEntity> entitiesByNotPage = outerOaEmployeeBindManager.getEntitiesByNotPage(OuterOaEmployeeBindParams.builder().dcId(entityById.getId()).outEmpId(outUserId).build());
                        if (CollectionUtils.isEmpty(entitiesByNotPage) || entitiesByNotPage.size() > 1) {
                            log.warn("ObjectDataMappingController.batchSyncData.entitiesByNotPage={}", entitiesByNotPage);
                            continue;
                        }
                        //先单独
                        final BaseConnectorVo baseConnectorVo = JSON.parseObject(entityById.getConnectInfo(), BaseConnectorVo.class);
                        final OuterAbstractSettingService implementation = outerOASettingFactory
                                .getImplementation(entityById.getChannel(), baseConnectorVo.getAppType());
                        implementation.employeeBindChangeEvent(entityById.getId(), Lists.newArrayList(entitiesByNotPage.get(0).getId()), EmplyeeBindChangeTypeEnum.employee_bind);
                    }
                } catch (Exception e) {
                    log.warn("ObjectDataMappingController.batchSyncData.e=", e);
                }
                successCount++;
            } else {
                failCount++;
                failData.append(employee.getMsg());
            }
        }

        employeeSyncResult.setSuccessCount(successCount);
        employeeSyncResult.setFailCount(failCount);
        employeeSyncResult.setErrorMsg(failData.toString());
        if(ObjectUtils.isNotEmpty(failData)){
            //failData避免太长显示不了
            if(failData.length()>500){
                failData = new StringBuilder(failData.substring(0,500));
            }
            String errorMessage = String.format(ResultCodeEnum.EMPLOYEE_CREATE_ERROR.getMsg(), successCount, failCount, JSONObject.toJSONString(failData));

            return Result.newError(ResultCodeEnum.EMPLOYEE_CREATE_ERROR.getCode(),errorMessage);
        }
        return Result.newSuccess(employeeSyncResult);
    }

}