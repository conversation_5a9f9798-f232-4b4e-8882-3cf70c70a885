package com.facishare.open.ding.cloud.service.impl;

import com.facishare.eservice.rest.cases.model.DingObjectDataListModel;
import com.facishare.open.ding.api.arg.ComponentQueryArg;
import com.facishare.open.ding.api.model.AppParams;
import com.facishare.open.ding.api.model.ErpPushDataObj;
import com.facishare.open.ding.api.result.DingStorageResult;
import com.facishare.open.ding.api.service.DataStorageService;
import com.facishare.open.ding.api.service.DingCorpMappingService;
import com.facishare.open.ding.api.vo.DingCorpMappingVo;
import com.facishare.open.ding.cloud.constants.ConfigCenter;
import com.facishare.open.ding.cloud.manager.DataConvertManager;
import com.facishare.open.ding.cloud.manager.DataPushManager;
import com.facishare.open.ding.cloud.manager.DingManager;
import com.facishare.open.ding.common.result.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2022/1/20 15:58 工单数据源注册
 * @Version 1.0
 */
@Service("dataStorageServiceImpl")
@Slf4j
public class DataStorageServiceImpl implements DataStorageService {
    @Autowired
    private DataPushManager dataPushManager;
    @Autowired
    private DingManager dingManager;
    @Autowired
    private DingCorpMappingService corpMappingService;
    @Autowired
    private DataConvertManager dataConvertManager;
    @Override
    public DingStorageResult<DingObjectDataListModel.Result> getEserviceWorkData(ComponentQueryArg componentQueryArg) {
        DingStorageResult<DingObjectDataListModel.Result> eserviceWorkData = dataPushManager.getEserviceWorkData(componentQueryArg);
        return eserviceWorkData;
    }

    @Override
    public Result<String> createPersonCustomer(ErpPushDataObj erpPushDataObj, Integer tenantId) {
        AppParams marketingMap = ConfigCenter.APP_PARAMS_MAP.get(ConfigCenter.MARKETING_SUITE_ID);//目前支持营销通的 允许创建.
        Result<List<DingCorpMappingVo>> corpResult = corpMappingService.queryByEi(tenantId);
        if(ObjectUtils.isEmpty(corpResult.getData())){
            return Result.newSuccess();
        }
        corpResult.getData().removeIf(item ->!item.getAppCode().equals(Long.valueOf(marketingMap.getAppId())));
        if(ObjectUtils.isEmpty(corpResult.getData())){
            return Result.newSuccess();
        }
        DingCorpMappingVo corpMappingVo=corpResult.getData().get(0);
        Result<String> personalCustomer = dingManager.createPersonalCustomer(corpResult.getData().get(0).getDingCorpId(), marketingMap.getSuiteId(), erpPushDataObj.getMasterFieldVal(), corpMappingVo.getEi());
        return personalCustomer;
    }
}
