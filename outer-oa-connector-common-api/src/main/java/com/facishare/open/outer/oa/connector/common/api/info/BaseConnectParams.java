package com.facishare.open.outer.oa.connector.common.api.info;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @create 2024/11/24 14:58
 *渠道链接参数
 * @desc
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
public abstract class BaseConnectParams  implements Serializable {
    //连接器名称
    private String connectorName;

    public abstract String getAutoField();
}
