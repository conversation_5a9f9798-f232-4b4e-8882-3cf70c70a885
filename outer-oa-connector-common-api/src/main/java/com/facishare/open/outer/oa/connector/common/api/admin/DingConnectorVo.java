package com.facishare.open.outer.oa.connector.common.api.admin;

import com.facishare.open.outer.oa.connector.common.api.annotation.ConnectorType;
import com.facishare.open.outer.oa.connector.common.api.annotation.SecurityField;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.OuterOaAppInfoTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/2/26 17:17:48
 *
 * 自建应用: agentId/appSecret/redirectAppId/redirectAppSecret/clientIp/createBy/updateBy/isCallback/devModel/alertStatus/allIndex/autBind/todoType
 * isv应用: bindType/status/enterpriseName/dingMainCorpId/repeatIndex/isInit/connector/category/extend
 */
@Data
@ConnectorType(channel = ChannelEnum.dingding, fieldName = "dingding")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DingConnectorVo extends BaseConnectorVo {

    /**
     * 钉钉企业ID
     */
    private String dingCorpId;

    /**
     * AppKey
     */
    private String appKey;

    // 自建应用
    /**
     * AgentID（自建应用使用）
     */
    private String agentId;

    /**
     * AppSecret（自建应用使用）
     */
    @SecurityField
    private String appSecret;
    /**
     * 移动应用ID
     */
    private String redirectAppId;

    /**
     * 移动应用密钥
     */
    @SecurityField
    private String redirectAppSecret;
    /**
     * 客户端URL
     */
    private String clientIp;
    private Integer createBy;
    private Integer updateBy;
    /**
     * 回调地址是否调用 0:使用 1：不使用
     */
    private Integer isCallback;
    private Integer allIndex;
    /**
     * 待办推送的位置  0：工作通知 1：钉钉待办 2：都推送
     */
    private Integer todoType;

    /**
     * 待办创建者ID，可以使用虚拟员工
     */
    private String todoCreator;

    /**
     * 待办创建者UnionID
     */
    private String todoCreatorUnionId;

    // isv应用 -- 自动同步通讯录
    private Integer bindType;
    private Integer status;
    private String enterpriseName;
    private String dingMainCorpId;
    private Integer repeatIndex;
    private Integer isInit;
    private Integer connector;
    /**
     * 默认的产品分类code
     */
    private String category;
    private String extend;
}
