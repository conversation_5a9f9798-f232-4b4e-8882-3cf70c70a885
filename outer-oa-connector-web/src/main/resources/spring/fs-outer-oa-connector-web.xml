<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:p="http://www.springframework.org/schema/p" xmlns:c="http://www.springframework.org/schema/c"
       xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
		http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
        http://www.springframework.org/schema/context
        http://www.springframework.org/schema/context/spring-context.xsd http://code.alibabatech.com/schema/dubbo http://code.alibabatech.com/schema/dubbo/dubbo.xsd">

    <context:annotation-config />
    <context:component-scan base-package="com.facishare.open.outer.oa.connector,com.facishare.open.oa.base.dbproxy" />
    <!--配置中心 -->
    <bean class="com.github.autoconf.spring.reloadable.ReloadablePropertyPostProcessor"
          c:placeholderConfigurer-ref="autoConf"/>
    <bean id="autoConf" class="com.github.autoconf.spring.reloadable.ReloadablePropertySourcesPlaceholderConfigurer"
          p:fileEncoding="UTF-8"
          p:ignoreResourceNotFound="true"
          p:ignoreUnresolvablePlaceholders="false"
          p:location="classpath:application.properties"
          p:configName="outer-oa-connector-config"/>
    <import resource="classpath:spring/applicationContext.xml" />
    <import resource="classpath:spring/ei-ea-converter.xml"/>
    <import resource="classpath:spring/third-party-rest-client.xml"/>
    <import resource="classpath:spring/db-application.xml"/>
    <import resource="classpath:spring/fs-warehouse-rest-client.xml"/>
<!--    <import resource="classpath:spring/outer-oa-connector-dubbo-consumer.xml"/>-->
<!--    <import resource="classpath:spring/outer-oa-connector-dubbo-provider.xml"/>-->
    <dubbo:reference id="ssoLoginService" interface="com.facishare.userlogin.api.service.SSOLoginService"/>

    <!--okHttp-->
    <bean id="okHttpSupport" class="com.fxiaoke.common.http.spring.HttpSupportFactoryBean"
          p:configName="fs-erp-order-contacts-proxy"/>
    <bean id="nFileStorageService" class="com.facishare.fsi.proxy.FsiServiceProxyFactoryBean">
        <property name="factory" ref="fsiWarehouseProxyFactory"/>
        <property name="type" value="com.facishare.fsi.proxy.service.NFileStorageService"/>
    </bean>

    <bean id="fsiWarehouseProxyFactory" class="com.facishare.fsi.proxy.FsiWarehouseProxyFactory" init-method="init">
        <property name="configKey" value="fs-qixin-fsi-proxy"/>
        <property name="enterpriseEditionService"  ref="enterpriseEditionService"/>
    </bean>
    <!--redis配置-->
    <bean id="redissonClient" class="com.fxiaoke.common.redisson.RedissonFactoryBean">
        <property name="configName" value="fs-feishu-config"/>
    </bean>
    <bean id="globalConfigService" class="com.facishare.fsi.proxy.FsiServiceProxyFactoryBean">
        <property name="factory" ref="fsiServiceProxyFactory"/>
        <property name="type" value="com.facishare.fsi.proxy.service.GlobalConfigService"/>
    </bean>

    <bean id="fsiServiceProxyFactory" class="com.facishare.fsi.proxy.FsiServiceProxyFactory" init-method="init">
        <property name="configKey" value="fs-qixin-fsi-proxy"/>
    </bean>


    <bean id="aFileStorageService" class="com.facishare.fsi.proxy.FsiServiceProxyFactoryBean">
        <property name="factory" ref="fsiWarehouseProxyFactory"/>
        <property name="type" value="com.facishare.fsi.proxy.service.AFileStorageService"/>
    </bean>

    <bean class="com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer"
          init-method="start" destroy-method="shutdown">
        <constructor-arg name="configName" value="outer-oa-connector-config"/>
        <constructor-arg name="sectionNames" value="OPEN_CONNECTOR_TODO_SECTION"/>
        <constructor-arg name="messageListener" ref="openConnectorMessageListener"/>
    </bean>

</beans>


