package com.facishare.open.outer.oa.connector.common.api.enums;

import com.google.common.base.MoreObjects;

public enum OADataTypeEnum {
    SUITE_TICKET("suiteTicket", "suiteTicket"),
    ENTERPRISE_OPEN("enterpriseOpen", "企业开通"),
    EMPLOYEE_LOGIN("employeeLogin", "人员登陆"),
    NEW_EMPLOYEE_LOGIN("newEmployeeLogin", "新人员登陆"),
    ENTERPRISE_CREATE("enterpriseCreate", "企业创建"),
    ;

    /**
     * 类型
     */
    private String dataType;

    /**
     * 描述
     */
    private String dataDesc;

    OADataTypeEnum(String dataType, String dataDesc) {
        this.dataType = dataType;
        this.dataDesc = dataDesc;
    }

    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    public String getDataDesc() {
        return dataDesc;
    }

    public void setDataDesc(String DataDesc) {
        this.dataDesc = DataDesc;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this).add("dataType", dataType).add("dataDesc", dataDesc)
                .toString();
    }
}
