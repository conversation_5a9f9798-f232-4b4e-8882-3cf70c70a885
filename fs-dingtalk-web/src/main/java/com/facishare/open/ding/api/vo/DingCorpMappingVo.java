package com.facishare.open.ding.api.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2021/5/7 16:00  应用开通绑定的企业
 * @Version 1.0
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DingCorpMappingVo implements Serializable {
    private Long id;
    private Integer ei;
    private String ea;
    private String enterpriseName;
    private String dingCorpId;
    private String dingMainCorpId;
    private Integer bindType;
    private Integer status;
    private Long appCode;
    private Integer repeatIndex;
    private Integer isInit;//识别是否已经初始化过通讯录 默认是0不初始化 1 已经初始化
    private Integer connector;//连接器的连接状态，0 代表未安装连接器，1 代表已安装连接器
    private String category;//默认的产品分类code
    /**
     * 扩展字段
     * isFirstTrial:留资使用的字段，判断是否是第一次进入crm
     */
    private String extend;
    private Date createTime;
    private Date updateTime;
}
