package com.facishare.open.outer.oa.connector.web.mq.listener;

import com.facishare.open.erpdss.outer.oa.connector.base.context.MethodContext;
import com.facishare.open.outer.oa.connector.web.msg.template.OpenConnectorFsOrganizationChangedTemplate;
import com.facishare.organization.api.event.OrganizationChangedListener;
import com.facishare.organization.api.event.organizationChangeEvent.EmployeeChangeEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;



/**
 * 用于监听部门、人员变更
 * <AUTHOR>
 */
@Component
@Slf4j
public class OpenConnectorUserEventListener extends OrganizationChangedListener {
    @Resource
    private OpenConnectorFsOrganizationChangedTemplate openConnectorFsOrganizationChangedTemplate;

    public OpenConnectorUserEventListener() {
        super("outer-oa-connector-config");
    }

    @Override
    protected void onEmployeeChanged(EmployeeChangeEvent event) {
        log.info("FsOrganizationChangedListener.onEmployeeChanged,event={}", event);
        MethodContext context = MethodContext.newInstance(event);
        openConnectorFsOrganizationChangedTemplate.onEmployeeChanged(context);
    }
    /**
     * 暂不支持部门
     */

//    @Override
//    protected void onDepartmentChanged(DepartmentChangeEvent event) {
//        log.info("FsOrganizationChangedListener.onDepartmentChanged,event={}",event);
//        MethodContext context = MethodContext.newInstance(event);
//        openConnectorFsOrganizationChangedTemplate.onDepartmentChanged(context);
//    }
}
