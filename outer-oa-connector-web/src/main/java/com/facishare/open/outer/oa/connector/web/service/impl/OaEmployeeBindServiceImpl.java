package com.facishare.open.outer.oa.connector.web.service.impl;

import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaConfigInfoEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEmployeeBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaConfigInfoManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEmployeeBindManager;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaEmployeeBindParams;
import com.facishare.open.outer.oa.connector.common.api.enums.OuterOaConfigInfoTypeEnum;
import com.facishare.open.outer.oa.connector.common.api.result.Result;
import com.facishare.open.outer.oa.connector.common.api.result.ResultCodeEnum;
import com.facishare.open.outer.oa.connector.web.model.QueryBindListArg;
import com.facishare.open.outer.oa.connector.web.model.admin.CommonExecuteArg;
import com.facishare.open.outer.oa.connector.web.service.CommonExecuteService;
import com.facishare.rest.core.util.JacksonUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service("getOaEmployeeBind")
public class OaEmployeeBindServiceImpl implements CommonExecuteService {
    @Autowired
    private OuterOaEmployeeBindManager outerOaEmployeeBindManager;

    @Override
    public Result<?> executeLogic(CommonExecuteArg arg) {
        String tenantId = arg.getTenantId();
        String params = arg.getParams();
        QueryBindListArg dataMap = JacksonUtil.fromJson(params, QueryBindListArg.class);
        String dcId = dataMap.getDcId();
        if (StringUtils.isBlank(dcId)) {
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }
        List<String> crmIds = dataMap.getCrmIds();
        List<String> erpIds = dataMap.getErpIds();
        if (CollectionUtils.isEmpty(crmIds) && CollectionUtils.isEmpty(erpIds)) {
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }
        if (CollectionUtils.isNotEmpty(crmIds) && CollectionUtils.isNotEmpty(erpIds)) {
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }
        List<OuterOaEmployeeBindEntity> entities = outerOaEmployeeBindManager.getEntitiesByDcId(dcId, crmIds, erpIds);
        return Result.newSuccess(entities);
    }
}
