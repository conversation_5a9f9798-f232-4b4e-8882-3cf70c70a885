package com.facishare.open.order.contacts.proxy.service.impl;

import com.alibaba.fastjson.TypeReference;
import com.facishare.open.order.contacts.proxy.api.arg.DealCrmTodoArg;
import com.facishare.open.order.contacts.proxy.api.network.ProxyHttpClient;
import com.facishare.open.order.contacts.proxy.api.result.Result;
import com.facishare.open.order.contacts.proxy.api.result.ResultCodeEnum;
import com.facishare.open.order.contacts.proxy.api.result.ResultV2;
import com.facishare.open.order.contacts.proxy.api.service.FsObjServiceProxy;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.open.order.contacts.proxy.config.ConfigCenter;
import com.facishare.open.order.contacts.proxy.limiter.CrmRateLimiter;
import com.fxiaoke.crmrestapi.arg.ControllerDetailArg;
import com.fxiaoke.crmrestapi.arg.FindDescribeManageListArg;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.common.data.ObjectDescribe;
import com.fxiaoke.crmrestapi.result.ControllerGetDescribeResult;
import com.fxiaoke.crmrestapi.service.MetadataControllerService;
import com.fxiaoke.crmrestapi.service.ObjectDescribeCrmService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;

@Service("fsObjServiceProxy")
public class FsObjServiceProxyImpl implements FsObjServiceProxy {
    @Resource
    private ObjectDescribeCrmService objectDescribeCrmService;
    @Resource
    private MetadataControllerService metadataControllerService;
    @Resource
    private ProxyHttpClient proxyHttpClient;
    @Resource
    private FsEmployeeAndDepartmentProxy fsEmployeeAndDepartmentProxy;

    @Override
    public Result<List<ObjectDescribe>> listAllObjects(Integer ei) {
        HeaderObj headerObj = HeaderObj.newInstance(ei, -10000);
        List<ObjectDescribe> objectDescribeList = objectDescribeCrmService.findDescribeManageList(headerObj, new FindDescribeManageListArg()).getData().getObjectDescribeList();
        return Result.newSuccess(objectDescribeList);
    }


    @Override
    public Result<ControllerGetDescribeResult> queryObjectData(Integer ei, String objectApiName, String ObjectId) {
        HeaderObj headerObj = HeaderObj.newInstance(ei, -10000);

        ControllerDetailArg controllerDetailArg = new ControllerDetailArg();
        controllerDetailArg.setObjectDataId(ObjectId);
        controllerDetailArg.setObjectDescribeApiName(objectApiName);
        controllerDetailArg.setIsFromRecycleBin (Boolean.TRUE);
        com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> detail = metadataControllerService.detail(headerObj, objectApiName, controllerDetailArg);

        return Result.newSuccess(detail.getData());
    }

    @Override
    public ResultV2<Boolean> dealCrmTodo(Integer ei, String userId, DealCrmTodoArg arg) {
        if(!CrmRateLimiter.isAllowed(null)) {
            return ResultV2.newError(ResultCodeEnum.SYSTEM_ERROR);
        }

        String url = ConfigCenter.DEAL_CRM_TODO_URL;
        HashMap<String, String> headerMap = new HashMap<>();
        headerMap.put("Content-Type", "application/json; charset=utf-8");
        headerMap.put("x-tenant-id", String.valueOf(ei));
        headerMap.put("x-user-id", userId);
        ResultV2<Boolean> result = proxyHttpClient.postUrl(url, arg, headerMap, new TypeReference<ResultV2<Boolean>>() {
        });
        return result;
    }

    @Override
    public Result<List<ObjectData>> queryEnterpriseInterconnect(Integer ei, String param) {
        String url = ConfigCenter.CRM_REST_OBJ_URL+ "/EnterpriseRelationObj/controller/List";

        Result<List<ObjectData>> result = fsEmployeeAndDepartmentProxy.postUrl3(url, param, ei + "");
        LogUtils.info("FsOrderServiceProxyImpl.queryCustomer,ei={},result={}", ei, result);
        return result;
    }

    @Override
    public Result<List<ObjectData>> queryEmployeeInterconnect(Integer ei, String param) {
        String url = ConfigCenter.CRM_REST_OBJ_URL+ "/PublicEmployeeObj/controller/List";

        Result<List<ObjectData>> result = fsEmployeeAndDepartmentProxy.postUrl3(url, param, ei + "");
        LogUtils.info("FsOrderServiceProxyImpl.queryCustomer,ei={},result={}", ei, result);
        return result;
    }
}
