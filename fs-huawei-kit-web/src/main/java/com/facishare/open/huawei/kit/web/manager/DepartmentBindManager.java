package com.facishare.open.huawei.kit.web.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.facishare.open.huawei.kit.web.entity.DepartmentBindEntity;
import com.facishare.open.huawei.kit.web.mapper.DepartmentBindMapper;
import com.facishare.open.outer.oa.connector.common.api.enums.BindStatusEnum;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * tb_department_bind表管理器类
 *
 * <AUTHOR>
 * @date 20220722
 */
@Component
public class DepartmentBindManager {
    @Resource
    private DepartmentBindMapper departmentBindMapper;

    public int insert(DepartmentBindEntity entity) {
        int count = departmentBindMapper.insert(entity);
        LogUtils.info("DepartmentBindManager.insert,count={}", count);
        return count;
    }

    /**
     * 查询部门信息
     *
     * @param outEa
     * @param outDepId
     * @return
     */
    public DepartmentBindEntity getEntity(String outEa, String outDepId) {
        LambdaQueryWrapper<DepartmentBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DepartmentBindEntity::getOutEa, outEa);
        wrapper.eq(DepartmentBindEntity::getOutDepId, outDepId);

        DepartmentBindEntity entity = departmentBindMapper.selectOne(wrapper);
        return entity;
    }

    /**
     * 查询部门信息
     *
     * @param outEa
     * @param outDepId
     * @return
     */
    public DepartmentBindEntity getEntity(ChannelEnum channel, String fsEa, String fsDepId, String outEa, String outDepId) {
        LambdaQueryWrapper<DepartmentBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DepartmentBindEntity::getChannel,channel.name());
        wrapper.eq(DepartmentBindEntity::getFsEa, fsEa);
        wrapper.eq(DepartmentBindEntity::getFsDepId, fsDepId);
        wrapper.eq(DepartmentBindEntity::getOutEa, outEa);
        wrapper.eq(DepartmentBindEntity::getOutDepId, outDepId);

        DepartmentBindEntity entity = departmentBindMapper.selectOne(wrapper);
        return entity;
    }

//    /**
//     * 查询部门信息
//     *
//     * @param fsEa
//     * @param fsDepId
//     * @return
//     */
//    public DepartmentBindEntity getEntity2(String fsEa, String fsDepId) {
//        LambdaQueryWrapper<DepartmentBindEntity> wrapper = new LambdaQueryWrapper<>();
//        wrapper.eq(DepartmentBindEntity::getFsEa, fsEa);
//        wrapper.eq(DepartmentBindEntity::getFsDepId, fsDepId);
//
//        DepartmentBindEntity entity = departmentBindMapper.selectOne(wrapper);
//        return entity;
//    }

    /**
     * 查询部门信息
     *
     * @param fsEa
     * @param depCode
     * @return
     */
    public DepartmentBindEntity getEntityByFsEa(String fsEa, String depCode) {
        LambdaQueryWrapper<DepartmentBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DepartmentBindEntity::getFsEa, fsEa);
        wrapper.eq(DepartmentBindEntity::getDepCode, depCode);

        DepartmentBindEntity entity = departmentBindMapper.selectOne(wrapper);
        return entity;
    }

    /**
     * 查询部门信息
     *
     * @param outEa
     * @param depCode
     * @return
     */
    public DepartmentBindEntity getEntityByOutEa(String outEa, String depCode) {
        LambdaQueryWrapper<DepartmentBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DepartmentBindEntity::getOutEa, outEa);
        wrapper.eq(DepartmentBindEntity::getDepCode, depCode);

        DepartmentBindEntity entity = departmentBindMapper.selectOne(wrapper);
        return entity;
    }

    /**
     * 批量更新部门绑定状态
     *
     * @param fsEa
     * @param fsDepIdList
     * @param bindStatus
     * @return
     */
    public int batchUpdateBindStatus(String fsEa,
                                     List<String> fsDepIdList,
                                     BindStatusEnum bindStatus,
                                     String outEa) {
        DepartmentBindEntity entity = DepartmentBindEntity.builder()
                .bindStatus(bindStatus)
                .build();

        LambdaUpdateWrapper<DepartmentBindEntity> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(DepartmentBindEntity::getFsEa, fsEa);
        if(CollectionUtils.isNotEmpty(fsDepIdList)) {
            wrapper.in(DepartmentBindEntity::getFsDepId, fsDepIdList);
        }
        if(StringUtils.isNotEmpty(outEa)) {
            wrapper.eq(DepartmentBindEntity::getOutEa, outEa);
        }
        int count = departmentBindMapper.update(entity, wrapper);
        LogUtils.info("DepartmentBindManager.batchUpdateBindStatus,count={}", count);
        return count;
    }

//    /**
//     * 更新部门绑定状态
//     *
//     * @param fsEa
//     * @param bindStatus
//     * @return
//     */
//    public int updateBindStatus(String fsEa, BindStatusEnum bindStatus) {
//        DepartmentBindEntity entity = DepartmentBindEntity.builder()
//                .bindStatus(bindStatus)
//                .build();
//
//        LambdaUpdateWrapper<DepartmentBindEntity> wrapper = new LambdaUpdateWrapper<>();
//        wrapper.eq(DepartmentBindEntity::getFsEa, fsEa);
//        int count = departmentBindMapper.update(entity, wrapper);
//        LogUtils.info("DepartmentBindManager.updateBindStatus,count={}", count);
//        return count;
//    }

    /**
     * 删除已绑定的部门数据
     *
     * @param fsEa
     * @return
     */
    public int deleteByFsEa(String fsEa, String outEa) {
        LambdaUpdateWrapper<DepartmentBindEntity> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(DepartmentBindEntity::getFsEa, fsEa);
        wrapper.eq(DepartmentBindEntity::getOutEa, outEa);
        int count = departmentBindMapper.delete(wrapper);
        LogUtils.info("DepartmentBindManager.deleteByFsEa,count={}", count);
        return count;
    }

    public List<DepartmentBindEntity> getAllEntities(String fsEa, String outEa) {
        LambdaQueryWrapper<DepartmentBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DepartmentBindEntity::getOutEa, outEa);
        wrapper.eq(DepartmentBindEntity::getFsEa, fsEa);

        List<DepartmentBindEntity> entities = departmentBindMapper.selectList(wrapper);
        return entities;
    }
}
