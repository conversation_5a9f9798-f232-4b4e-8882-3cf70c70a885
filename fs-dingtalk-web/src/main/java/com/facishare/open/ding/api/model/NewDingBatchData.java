package com.facishare.open.ding.api.model;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/1/24 19:38
 * @Version 1.0
 */
@Data
@Slf4j
public class NewDingBatchData implements Serializable {
    private String creatorUserId;
    private String data;
    private String extendData;
    private String gmtCreate;
    private String objectType;
    private String procInstStatus;
    private String procOutResult;
    private Permission permission;
    private String instanceId;
    private String gmtModified;
    @Data
    public static class Permission implements Serializable {
        public List<String> ownerStaffIds;
        public List<String> participantStaffIds;
    }

    public  Long convertCreateTIme(){
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm'Z'");
        try {
            Date parse = df.parse(this.getGmtCreate());
            return parse.getTime();
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }
}
