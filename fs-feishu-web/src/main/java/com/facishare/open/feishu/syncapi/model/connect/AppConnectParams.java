package com.facishare.open.feishu.syncapi.model.connect;

import com.facishare.open.feishu.syncapi.consts.Constant;
import com.facishare.open.feishu.syncapi.enums.AppStatusEnum;
import com.facishare.open.outer.oa.connector.common.api.info.BaseConnectParams;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @create 2024/11/24 15:00
 * @desc
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
public class AppConnectParams implements Serializable {
    /**
     * 飞书企业内部应用需要用到的appId和appSecret
     */
    private String appId;
    private String appSecret;

    /**
     * "applicants": [ // 应用的申请者，可能有多个
     * {
     * "open_id":"xxx" ,  // 用户对此应用的唯一标识，同一用户对不同应用的open_id不同
     * }
     * ],
     */
    private String applicants;
    /**
     * 当应用被管理员安装时，返回此字段。如果是自动安装或由普通成员获取时，没有此字段
     */
    private String installerOpenId;
    /**
     * 当应用被普通成员安装时，返回此字段
     */
    private String installerEmployeeOpenId;
    /**
     * "operator": { // 仅status=start_by_tenant时有此字段
     * "open_id":"xxx",
     * "user_id":"yyy", // 仅自建应用才会返回
     * "union_id": "zzz" // 用户在ISV下的唯一标识
     * },
     */
    private String operator;

}
