package com.facishare.open.outer.oa.connector.web.msg.template;

import com.alibaba.fastjson2.JSON;
import com.facishare.open.erpdss.outer.oa.connector.base.context.MethodContext;
import com.facishare.open.erpdss.outer.oa.connector.base.context.TemplateResult;
import com.facishare.open.erpdss.outer.oa.connector.base.outer.msg.SendMsgHandlerTemplate;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.outer.oa.connector.common.api.admin.QywxConnectorVo;
import com.facishare.open.outer.oa.connector.common.api.admin.StandardConnectorVo;
import com.facishare.open.outer.oa.connector.common.api.enums.AlertTypeEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.BindStatusEnum;
import com.facishare.open.outer.oa.connector.common.api.result.Result;
import com.facishare.open.outer.oa.connector.web.openconnetor.OpenConnectorManager;
import com.facishare.open.outer.oa.connector.web.openconnetor.model.OuterDeleteTodoArg;
import com.facishare.open.outer.oa.connector.web.openconnetor.model.OuterMsgModel;
import com.facishare.open.outer.oa.connector.web.openconnetor.model.OuterSendTextCardMessageArg;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 *
 * <AUTHOR>
 * @date 2025/5/26
 */

@Slf4j
@Component
public class OpenConnectorSendTextCarMsgTemplate extends SendMsgHandlerTemplate {
    @Resource
    private OpenConnectorManager openConnectorManager;


    @Override
    protected void filterMsg(MethodContext context) {
        log.info("OpenConnectorSendTextCarMsgTemplate.filterMsg,context={}",context);
        OuterSendTextCardMessageArg sendTextCardMessagePushArg = context.getData();

        if (ObjectUtils.isEmpty(sendTextCardMessagePushArg)) {
            context.setResult(TemplateResult.newError(null));
            return;
        }

        OuterOaEnterpriseBindEntity enterpriseBindEntity = sendTextCardMessagePushArg.getOuterOaEnterpriseBindEntity();
        if (!enterpriseBindEntity.getBindStatus().equals(BindStatusEnum.normal)) {
            log.info("OpenConnectorSendTextCarMsgTemplate.filterMsg.is not normal,enterpriseBindEntity={}", enterpriseBindEntity);
            context.setResult(TemplateResult.newError(null));
            return;
        }

        StandardConnectorVo standardConnectorVo=enterpriseBindEntity.getConnectParams().getStandard_oa();
        if (!standardConnectorVo.isAlertConfig()) {
            log.info("OpenConnectorSendTextCarMsgTemplate.filterMsg.is not alert config,standardConnectorVo={}", standardConnectorVo);
            context.setResult(TemplateResult.newError(null));
            return;
        }
        if (!standardConnectorVo.getAlertTypes().contains(AlertTypeEnum.CRM_NOTIFICATION)) {
            log.info("OpenConnectorSendTextCarMsgTemplate.filterMsg.if not notify type,standardConnectorVo={}", standardConnectorVo);
            context.setResult(TemplateResult.newError(null));
            return;
        }
        Result<Void> voidResult = openConnectorManager.filterTextCarMsg(sendTextCardMessagePushArg.getTenantId(), sendTextCardMessagePushArg.getDcId(),
                sendTextCardMessagePushArg.getApiName(), sendTextCardMessagePushArg.getCrmArg());
        if (voidResult.isSuccess()) {
            context.setResult(TemplateResult.newSuccess());
        } else {
            context.setResult(TemplateResult.newError(voidResult.getMsg()));
        }
    }

    @Override
    public void buildMsg(MethodContext context) {
        log.info("OpenConnectorSendTextCarMsgTemplate.build,context={}", context);
        OuterSendTextCardMessageArg outerSendTextCardMessageArg = context.getData();
        Result<OuterMsgModel> result = openConnectorManager.buildTextCarMsg(outerSendTextCardMessageArg.getTenantId(), outerSendTextCardMessageArg.getDcId(),
                outerSendTextCardMessageArg.getApiName(), outerSendTextCardMessageArg.getCrmArg());
        if (result.isSuccess()) {
            outerSendTextCardMessageArg.setOuterModel(result.getData());
            context.setResult(TemplateResult.newSuccess());
        } else {
            context.setResult(TemplateResult.newError(result.getMsg()));
        }
    }

    @Override
    public void sendMsg(MethodContext context) {
        log.info("OpenConnectorSendTextCarMsgTemplate.sendMsg,context={}", context);
        OuterSendTextCardMessageArg outerSendTextCardMessageArg = context.getData();
        Result<Void> result = openConnectorManager.sendTextCarMsg(outerSendTextCardMessageArg.getTenantId(), outerSendTextCardMessageArg.getDcId(),
                outerSendTextCardMessageArg.getApiName(), outerSendTextCardMessageArg.getOuterModel());
        if (result.isSuccess()) {
            context.setResult(TemplateResult.newSuccess());
        } else {
            context.setResult(TemplateResult.newError(result.getMsg()));
        }
    }
}
