package com.facishare.open.outer.oa.connector.common.api.object;

import lombok.Data;

import java.io.Serializable;
import java.util.HashMap;

/**
 * 标准部门对象
 */
@Data
public class StandardDepartmentObject extends HashMap<String,Object> implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 部门ID
     */
    private String deptId;

    /**
     * 父部门ID
     */
    private String parentId;

    /**
     * 部门名称
     */

    private String name;

    /**
     * 子部门顺序
     */

    private Integer seq;
}