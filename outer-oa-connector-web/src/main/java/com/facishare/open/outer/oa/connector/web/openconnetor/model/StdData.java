package com.facishare.open.outer.oa.connector.web.openconnetor.model;

import lombok.Data;

import java.io.Serializable;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * 标准主从数据
 *
 * <AUTHOR> (^_−)☆
 */
@Data
public class StdData implements Serializable {

    private static final long serialVersionUID = -8910250656739958394L;
    /**
     * 对象ApiName
     */
    private String objAPIName;

    /**
     * 主对象数据
     */
    private OuterObjectData masterFieldVal;

    /**
     * 从对象数据;key:从对象apiName,value:从对象数据
     */
    private Map<String, List<OuterObjectData>> detailFieldVals = new LinkedHashMap<>(0);
}
