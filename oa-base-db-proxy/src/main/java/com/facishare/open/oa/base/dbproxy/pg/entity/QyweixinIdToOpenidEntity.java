package com.facishare.open.oa.base.dbproxy.pg.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * QyweixinIdToOpenidEntity
 * 企业微信ID与OpenID映射实体类
 */
@Data
@TableName("qyweixin_id_to_openid")
public class QyweixinIdToOpenidEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId
    private String id;

    /**
     * 企业ID
     */
    private String outEa;

    /**
     * 明文账号
     */
    private String plaintextId;

    /**
     * 密文账号
     */
    private String openid;

    /**
     * 账号类型，0：员工，1：客户
     */
    private Integer type;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateTime;
} 