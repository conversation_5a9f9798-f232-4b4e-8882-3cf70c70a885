package com.facishare.open.outer.oa.connector.web.openconnetor.model;

import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> (^_−)☆
 */
@Data
public class StdListData implements Serializable {
    private static final long serialVersionUID = 6148935304039163388L;
    /**
     * 数据列表
     */
    private List<StdData> dataList = new ArrayList<>();
    /**
     * 下次查询开始Id，非必填
     */
    private String maxId;
    /**
     * 下次查询开始时间，非必填
     */
    private Long maxTime;
}
