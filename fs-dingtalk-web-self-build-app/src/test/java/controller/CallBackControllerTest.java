package controller;

import base.BaseAbstractTest;
import com.dingtalk.api.response.OapiSnsGetuserinfoBycodeResponse;
import com.dingtalk.api.response.OapiUserGetResponse;
import com.dingtalk.oapi.lib.aes.DingTalkEncryptException;
import com.dingtalk.oapi.lib.aes.DingTalkEncryptor;
import com.facishare.open.ding.api.result.DingEnterpriseResult;
import com.facishare.open.ding.api.service.EnterpriseService;
import com.facishare.open.ding.api.service.ExternalMessageService;
import com.facishare.open.ding.api.vo.ConnectionVo;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.provider.arg.SendTextCardContextArg;
import com.facishare.open.ding.provider.entity.DingEnterprise;
import com.facishare.open.ding.provider.manager.DingEnterpriseManager;
import com.facishare.open.ding.template.message.DingSelfSendCardMessageTemplate;
import com.facishare.open.ding.web.constants.Constant;
import com.facishare.open.ding.web.controller.CallBackController;
import com.facishare.open.ding.web.controller.EnterpriseController;
import com.facishare.open.ding.web.dingding.DingRequestUtil;
import com.facishare.userlogin.api.model.CreateUserTokenDto;
import com.facishare.userlogin.api.model.UserTokenDto;
import com.facishare.userlogin.api.service.SSOLoginService;
import com.fxiaoke.message.extrnal.platform.model.arg.SendTextCardMessageArg;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import sun.misc.BASE64Decoder;
import sun.misc.BASE64Encoder;

import java.io.IOException;
import java.net.URLEncoder;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2018/7/17 18:01
 */
@Slf4j
public class CallBackControllerTest extends BaseAbstractTest {
    @Autowired
    private CallBackController callBackController;
    @Test
    public void testQuery() throws IOException {
        Result<String> stringResult = callBackController.authorizeByApp2("etdd", "ding328b43b93b4861db4ac5d6980864d335", "ding2tvtlkfe9vxewk8w", "dd", null);
        System.out.println(stringResult);
    }
}
