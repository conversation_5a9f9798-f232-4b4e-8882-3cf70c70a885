package com.facishare.open.outer.oa.connector.web.service.impl;

import com.facishare.open.outer.oa.connector.common.api.result.Result;
import com.facishare.open.outer.oa.connector.common.api.result.ResultCodeEnum;

import com.facishare.open.outer.oa.connector.web.model.admin.CommonExecuteArg;
import com.facishare.open.outer.oa.connector.web.service.CommonExecuteService;
import org.springframework.stereotype.Service;

@Service
public class BaseCommonExecuteServiceImpl implements CommonExecuteService {
    @Override
    public com.facishare.open.outer.oa.connector.common.api.result.Result<?> executeLogic(CommonExecuteArg arg) {
        return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
    }
}
